package com.mn.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.entity.ProductBasePrice;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 * 商品基础价格表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
public interface ProductBasePriceMapper extends BaseMapper<ProductBasePrice> {

    /**
     * 获取有效的基础价格
     *
     * @param productId 商品ID
     * @param skuId SKU ID
     * @param orderDate 订单日期
     * @return 基础价格
     */
    ProductBasePrice getEffectiveBasePrice(@Param("productId") Long productId, 
                                          @Param("skuId") Long skuId, 
                                          @Param("orderDate") Date orderDate);
}
