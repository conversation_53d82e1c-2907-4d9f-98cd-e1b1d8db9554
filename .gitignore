# Java项目忽略文件 (mn_mch_manage)
*.class
*.log
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE
.idea/
*.iws
*.iml
*.ipr
.vscode/
*.swp
*.swo
*~

# Eclipse
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.project
.externalToolBuilders/
*.launch
.pydevproject
.cproject
.autotools
.factorypath
.buildpath
.target
.tern-project
.texlipse
.springBeans
.recommenders/

# Vue.js项目忽略文件 (mn_mch_web)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Rollup.js default build output
dist/

# Uncomment this if you have a Yarn Workspace
# yarn-error.log

# 微信小程序忽略文件 (mn_mch_wx_app)
# 微信开发者工具生成的文件
miniprogram_npm/
node_modules/
.DS_Store

# 私有配置文件
project.private.config.json

# 日志文件
*.log

# 临时文件
*.tmp
*.temp

# 系统文件
Thumbs.db
.DS_Store

# 编辑器配置
.vscode/
.idea/

# 构建输出
build/
dist/

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 其他
*.bak
*.orig
*.rej
*~

# 开发工具生成的文件
.history/
.cache/

# 依赖分析文件
dependency-analysis.txt
dependency-tree.txt

# 日志目录
logs/
