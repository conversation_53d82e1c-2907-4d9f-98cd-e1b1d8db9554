package com.mn.controller;

import com.alibaba.fastjson.JSON;
import com.mn.entity.NyDept;
import com.mn.entity.ProductCategory;
import com.mn.form.DeptForm;
import com.mn.form.ProductCategoryForm;
import com.mn.service.IProductCategoryService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;
import com.mn.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 商品分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@RestController
@RequestMapping("/product-category")
@Slf4j
public class ProductCategoryController extends BaseController {

    @Resource
    IProductCategoryService categoryService;

    /**
     * 获取商品分类列表
     */
    @PreAuthorize("@ss.hasPermi('product:category:list')")
    @GetMapping("/list")
    public AjaxResult list(ProductCategoryForm form) {
        form.setDeptId(getDeptId());
        List<ProductCategory> categories = categoryService.listCategory(form);
        return success(categories);
    }

    @GetMapping("/list/exclude")
    public AjaxResult excludeChild(){
        ProductCategoryForm form = new ProductCategoryForm();
        form.setDeptId(getDeptId());
        long categoryId = 0L;
        List<ProductCategory> categories = categoryService.listCategory(form);
        log.info("处理前:{}", JSON.toJSONString(categories));
        categories.removeIf(d -> d.getCategoryId().intValue() == categoryId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), categoryId + ""));
        log.info("处理后:{}", JSON.toJSONString(categories));
        return success(categories);
    }

    /**
     * 根据分类编号获取详细信息
     */
    //@PreAuthorize("@ss.hasPermi('product:category:query')")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable Long categoryId) {
        return success(categoryService.getById(categoryId));
    }

    /**
     * 新增商品分类
     */
    //@PreAuthorize("@ss.hasPermi('product:category:add')")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ProductCategory category) {
        category.setCreateBy(getUsername());
        category.setCreateTime(DateUtils.getNowDate());
        category.setDeptId(getDeptId());
        return toAjax(categoryService.addCategory(category));
    }

    /**
     * 修改商品分类
     */
    //@PreAuthorize("@ss.hasPermi('product:category:edit')")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody ProductCategory category) {
        category.setUpdateBy(getUsername());
        category.setUpdateTime(DateUtils.getNowDate());
        return toAjax(categoryService.updateCategory(category));
    }

    /**
     * 删除商品分类
     */
    //@PreAuthorize("@ss.hasPermi('product:category:remove')")
    @PostMapping("/del")
    public AjaxResult remove(Long categoryId) {
        if (categoryService.hasChildByCategoryId(categoryId)) {
            return warn("存在下级分类,不允许删除");
        }
        if (categoryService.checkCategoryExistProduct(categoryId)) {
            return warn("分类下存在商品,不允许删除");
        }
        return toAjax(categoryService.removeById(categoryId));
    }
}
