.order-id {
  font-size: 24rpx;
  color: #666;
}

.order-time {
  font-size: 24rpx;
  color: #999;
  margin: 16rpx 0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
}

.order-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 顶部标题栏 */
.header-section {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 40rpx 30rpx 30rpx;
  color: #fff;
}

.page-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
}

/* 筛选标签样式 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx 0;
  margin: 0 30rpx;
  margin-top: -20rpx;
  border-radius: 16rpx 16rpx 0 0;
  position: relative;
  z-index: 10;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.06);
}

.tab {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.tab.active {
  color: #1890ff;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1890ff;
  border-radius: 2rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f8ff;
  border-radius: 50%;
}

.empty-text {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-desc {
  color: #999;
  font-size: 26rpx;
}

/* 订单卡片样式 */
.order-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 30rpx;
  margin-top: 0;
  padding: 30rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.order-card:first-child {
  margin-top: 20rpx;
}

.order-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 10rpx rgba(0, 0, 0, 0.06);
}

.order-header {
  display: flex;
  justify-content: space-between;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.order-time {
  font-size: 24rpx;
  color: #666;
}

.order-status {
  font-size: 26rpx;
  font-weight: bold;
}

.status-SUCCEEDED {
  color: #52c41a;
}

.status-PROCESSING {
  color: #1890ff;
}

.status-CLOSED {
  color: #999;
}

/* 商品列表样式 */
.order-goods {
  padding: 20rpx 0;
}

.goods-item {
  display: flex;
  margin-bottom: 20rpx;
}

.goods-item:last-child {
  margin-bottom: 0;
}

.goods-image-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f5f5f5;
}

.goods-image {
  width: 100%;
  height: 100%;
}

.goods-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f8ff;
}

.goods-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 26rpx;
  color: #1890ff;
  font-weight: 500;
}

.goods-qty {
  font-size: 24rpx;
  color: #999;
}

/* 订单底部样式 */
.order-footer {
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-total {
  font-size: 24rpx;
  color: #666;
}

.total-amount {
  margin-left: 20rpx;
  font-weight: bold;
  color: #333;
}

.order-actions {
  display: flex;
}

.action-btn {
  margin-left: 20rpx;
  font-size: 24rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  line-height: 1.5;
  min-height: auto;
}

.pay-btn {
  background: linear-gradient(135deg, #1890ff, #0050b3);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
}

.detail-btn {
  background-color: #fff;
  color: #666;
  border: 1rpx solid #ddd;
}

/* 加载更多样式 */
.loading-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}

/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
  z-index: 1000;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1890ff;
}

.tab-css-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}

.tab-text {
  font-size: 24rpx;
  line-height: 1;
}

/* CSS图标样式 */
.css-icon {
  width: 48rpx;
  height: 48rpx;
  position: relative;
  display: inline-block;
}

.icon-home::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 16rpx solid currentColor;
  top: 8rpx;
  left: 12rpx;
}

.icon-home::after {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 12rpx;
  background-color: currentColor;
  bottom: 8rpx;
  left: 15rpx;
}

.icon-product::before {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid currentColor;
  border-radius: 6rpx;
  top: 6rpx;
  left: 6rpx;
}

.icon-product::after {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: currentColor;
  border-radius: 3rpx;
  top: 12rpx;
  left: 12rpx;
}

.icon-user::before {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border: 2rpx solid currentColor;
  border-radius: 50%;
  top: 8rpx;
  left: 18rpx;
}

.icon-user::after {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 10rpx;
  border: 2rpx solid currentColor;
  border-top: none;
  border-radius: 0 0 9rpx 9rpx;
  bottom: 8rpx;
  left: 15rpx;
}

/* 空状态图标 */
.icon-empty-order {
  width: 80rpx;
  height: 80rpx;
}

.icon-empty-order::before {
  content: '';
  position: absolute;
  width: 48rpx;
  height: 60rpx;
  border: 4rpx solid #1890ff;
  border-radius: 8rpx;
  top: 10rpx;
  left: 16rpx;
}

.icon-empty-order::after {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 3rpx;
  background-color: #1890ff;
  top: 28rpx;
  left: 28rpx;
  box-shadow: 0 12rpx 0 #1890ff, 0 24rpx 0 #1890ff;
}

/* 商品占位图标 */
.icon-product-placeholder {
  width: 60rpx;
  height: 60rpx;
}

.icon-product-placeholder::before {
  content: '';
  position: absolute;
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid #1890ff;
  border-radius: 6rpx;
  top: 12rpx;
  left: 12rpx;
}

.icon-product-placeholder::after {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 18rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
  top: 21rpx;
  left: 21rpx;
}