<view class="order-container">
  <!-- 顶部标题栏 -->
  <view class="header-section">
    <view class="page-title">
      <text>我的订单</text>
    </view>
  </view>

  <!-- 订单状态筛选 -->
  <view class="filter-tabs">
    <view class="tab {{activeTab === 'ALL' ? 'active' : ''}}" bindtap="switchTab" data-tab="ALL">全部</view>
    <view class="tab {{activeTab === 'PROCESSING' ? 'active' : ''}}" bindtap="switchTab" data-tab="PROCESSING">待付款</view>
    <view class="tab {{activeTab === 'SUCCEEDED' ? 'active' : ''}}" bindtap="switchTab" data-tab="SUCCEEDED">已付款</view>
    <view class="tab {{activeTab === 'CLOSED' ? 'active' : ''}}" bindtap="switchTab" data-tab="CLOSED">已关闭</view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list-container">
    <view wx:if="{{orderList.length === 0}}" class="empty-state">
      <view class="empty-icon">
        <view class="css-icon icon-empty-order"></view>
      </view>
      <text class="empty-text">暂无订单</text>
      <text class="empty-desc">您还没有任何订单记录</text>
    </view>

    <view wx:for="{{orderList}}" wx:key="outPaymentId" class="order-card" bindtap="goToOrderDetail" data-order-id="{{item.outPaymentId}}">
      <view class="order-header">
        <view class="order-id">订单号：{{item.outPaymentId}}</view>
        <text class="order-status status-{{item.status}}">
          {{item.status === 'SUCCEEDED' ? '已付款' : (item.status === 'CLOSED' ? '已关闭' : '待付款')}}
        </text>
      </view>

      <view class="order-time">下单时间：{{item.createTimeFormatted}}</view>

      <view class="order-goods">
        <view wx:for="{{item.goodsList}}" wx:for-item="good" wx:key="index" class="goods-item">
          <view class="goods-image-container">
            <image wx:if="{{good.good_img}}" class="goods-image" src="{{good.good_img}}" mode="aspectFill"></image>
            <view wx:else class="goods-image-placeholder">
              <view class="css-icon icon-product-placeholder"></view>
            </view>
          </view>
          <view class="goods-info">
            <text class="goods-name">{{good.good_name}}</text>
            <view class="goods-price-qty">
              <text class="goods-price">¥{{good.good_amount/100}}</text>
              <text class="goods-qty">x{{good.good_number}}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="order-footer">
        <view class="order-total">
          <text>共{{item.goodsCount}}件商品</text>
          <text class="total-amount">合计: ¥{{item.amount/100}}</text>
        </view>
        <view class="order-actions">
          <button wx:if="{{item.status === 'PROCESSING'}}" class="action-btn pay-btn" catchtap="payOrder" data-order-id="{{item.outPaymentId}}">立即支付</button>
          <button wx:if="{{item.status === 'CLOSED' || item.status === 'SUCCEEDED'}}" class="action-btn detail-btn">查看详情</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="loading-more" wx:if="{{hasMore}}">
    <text wx:if="{{isLoading}}">加载中...</text>
    <text wx:else bindtap="loadMore">加载更多</text>
  </view>

  <!-- 使用统一的custom-tab-bar，删除旧的导航栏 -->

  <t-message id="t-message" />
</view>