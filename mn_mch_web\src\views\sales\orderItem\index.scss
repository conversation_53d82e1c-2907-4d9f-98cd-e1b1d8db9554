.order-item-container {
  .mb-20 {
    margin-bottom: 20px;
  }

  .mt-10 {
    margin-top: 10px;
  }

  .ml-20 {
    margin-left: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-buttons {
    display: flex;
    gap: 8px;
  }

  .batch-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .el-form-item {
      margin-bottom: 10px;
      margin-right: 0;
    }
  }

  .total-info {
    .total-row {
      display: flex;
      align-items: center;
      padding: 10px 0;

      .label {
        font-weight: bold;
        margin-right: 5px;
      }

      .value {
        color: #606266;

        &.amount {
          color: #f56c6c;
          font-weight: bold;
          font-size: 16px;
        }
      }
    }
  }

  // 调整表格内部元素的间距
  ::v-deep .el-table {
    font-size: 12px;

    .el-input-number {
      width: 100%;
    }

    .el-input__inner {
      padding-left: 5px;
      padding-right: 5px;
    }
  }
}