<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="card-header">
        <span><i class="el-icon-document header-icon"></i>{{ isEdit ? '编辑销售订单' : '新增销售订单' }}</span>
        <div class="header-buttons">
          <el-button icon="el-icon-back" size="small" @click="goBack">返回列表</el-button>
        </div>
      </div>

      <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="small">
        <!-- 基本信息卡片 -->
        <el-card class="box-card mb-20" shadow="hover">
          <div slot="header" class="card-header">
            <span><i class="el-icon-document header-icon"></i>订单基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="订单编号" prop="orderNo">
                <el-input
                  v-model="form.orderNo"
                  placeholder="请输入或自动生成订单编号"
                  :disabled="isEdit"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-refresh"
                    @click="generateOrderNo"
                    v-if="!isEdit"
                  >生成</el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单日期" prop="orderDate">
                <el-date-picker
                  v-model="form.orderDate"
                  type="date"
                  placeholder="选择日期"
                  style="width: 100%"
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="客户" prop="partnerId">
                <el-select
                  v-model="form.partnerId"
                  placeholder="请选择客户"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in partnerOptions"
                    :key="item.partnerId"
                    :label="item.partnerName"
                    :value="item.partnerId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 销售订单明细管理卡片 -->
        <el-card class="box-card mb-20" shadow="hover">
          <div slot="header" class="card-header">
            <span><i class="el-icon-shopping-cart-full header-icon"></i>订单明细</span>
            <div class="header-buttons">
              <el-button type="success" plain icon="el-icon-shopping-cart-2" size="mini" @click="openProductSelector">
                选择商品
              </el-button>
            </div>
          </div>

          <!-- 订单明细列表 -->
          <el-table
            v-loading="itemsLoading"
            :data="orderItemList"
            border
            size="small"
            stripe
            highlight-current-row
            class="mt-10"
            style="width: 100%"
          >
            <el-table-column label="商品名称" align="center" prop="productName" min-width="150">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.productName"
                  placeholder="请输入商品名称"
                  size="mini"
                  :disabled="true"
                />
              </template>
            </el-table-column>

            <el-table-column label="商品编码" align="center" prop="productCode" min-width="80">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.productCode"
                  placeholder="请输入商品编码"
                  size="mini"
                  :disabled="true"
                />
              </template>
            </el-table-column>

            <el-table-column label="SKU编码" align="center" prop="skuCode" min-width="120">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.skuId"
                  filterable
                  placeholder="请选择SKU"
                  size="mini"
                  @change="(val) => handleSkuChange(val, scope.row)"
                >
                  <el-option
                    v-for="item in scope.row.skuOptions || []"
                    :key="item.skuId"
                    :label="item.skuCode"
                    :value="item.skuId"
                  >
                    <span style="float: left">{{ item?.skuCode }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">
                      {{ formatSpecData(item.specData) }}
                    </span>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="规格" align="center" prop="specData" min-width="150">
              <template slot-scope="scope">
                <span>{{ formatSpecData(scope.row.specData) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="单位" align="center" width="80">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.unit"
                  placeholder="单位"
                  size="mini"
                  :disabled="true"
                />
              </template>
            </el-table-column>

            <el-table-column label="数量" align="center" width="150">
              <template slot-scope="scope">
                <el-input-number
                  v-model="scope.row.quantity"
                  :precision="2"
                  :step="1"
                  :min="0.01"
                  controls-position="right"
                  size="mini"
                  @change="(val) => handleQuantityChange(scope.row, val)"
                />
              </template>
            </el-table-column>

            <el-table-column label="单价(元)" align="center" width="150">
              <template slot-scope="scope">
                <el-input-number
                  v-model="scope.row.price"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  controls-position="right"
                  size="mini"
                  @change="(val) => handlePriceChange(scope.row, val)"
                />
              </template>
            </el-table-column>

            <el-table-column label="金额(元)" align="center" width="100">
              <template slot-scope="scope">
                <span>{{ formatAmount(scope.row.amount) }}</span>
              </template>
            </el-table-column>

      

            <el-table-column label="操作" align="center" width="60">
              <template slot-scope="scope">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  size="mini"
                  @click="removeOrderItem(scope.$index)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 合计信息 -->
          <div class="total-info mt-10" v-if="orderItemList.length > 0">
            <el-divider content-position="left">合计信息</el-divider>
            <div class="total-row">
              <span class="label">总数量:</span>
              <span class="value">{{ totalQuantity }}</span>
              <span class="label ml-20">总金额:</span>
              <span class="value amount">{{ formatAmount(totalAmount) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 金额信息和备注卡片 -->
        <el-card class="box-card mb-20" shadow="hover">
          <div slot="header" class="card-header">
            <span><i class="el-icon-money header-icon"></i>金额信息与备注</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="订单金额" prop="totalAmount">
                <el-input-number
                  v-model="form.totalAmount"
                  :precision="2"
                  :step="100"
                  :min="0"
                  style="width: 100%"
                  controls-position="right"
                  :disabled="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="优惠金额" prop="discountAmount">
                <el-input-number
                  v-model="form.discountAmount"
                  :precision="2"
                  :step="10"
                  :min="0"
                  style="width: 100%"
                  @change="calculateActualAmount"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="实际金额" prop="actualAmount">
                <el-input-number
                  v-model="form.actualAmount"
                  :precision="2"
                  :step="100"
                  :min="0"
                  style="width: 100%"
                  :disabled="true"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入备注信息"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <div class="form-footer">
          <el-button @click="goBack">取 消</el-button>
          <el-button type="primary" @click="submitForm">保 存</el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 商品选择器 -->
    <product-selector
      v-if="productSelectorVisible"
      :visible.sync="productSelectorVisible"
      @confirm="handleProductsSelected"
    />
  </div>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";
import { getSalesOrder, addSalesOrder, updateSalesOrder } from "@/api/sales/order";
import { listSalesOrderItem, batchAdd, batchEdit, delSalesOrderItem } from "@/api/sales/orderItem";
import { listPartner } from "@/api/company/partner";
import { formatSpecData, formatAmount } from "@/views/sales/orderItem/utils";
import ProductSelector from "@/views/sales/orderItem/productSelector.vue";

export default {
  name: "SalesOrderEdit",
  components: {
    ProductSelector
  },
  data() {
    return {
      // 是否编辑模式
      isEdit: false,
      // 订单ID
      orderId: null,
      // 客户选项
      partnerOptions: [],
      // 订单明细列表
      orderItemList: [],
      // 订单明细加载状态
      itemsLoading: false,
      // 商品选择器是否可见
      productSelectorVisible: false,
      // 表单参数
      form: {
        orderId: undefined,
        orderNo: undefined,
        deptId: undefined,
        partnerId: undefined,
        orderDate: undefined,
        totalAmount: 0,
        discountAmount: 0,
        actualAmount: 0,
        paymentStatus: 0,
        paymentAmount: 0,
        orderStatus: 0,
        remark: undefined,
        createTime: undefined,
        updateTime: undefined,
        createBy: undefined,
        updateBy: undefined
      },
      // 表单校验规则
      rules: {
        orderNo: [
          { required: true, message: "请输入或生成订单编号", trigger: "blur" }
        ],
        orderDate: [
          { required: true, message: "请选择订单日期", trigger: "blur" }
        ],
        partnerId: [
          { required: true, message: "请选择客户", trigger: "change" }
        ],
        totalAmount: [
          { required: true, message: "请输入订单金额", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    // 总数量
    totalQuantity() {
      return this.orderItemList.reduce((sum, item) => {
        return sum + (parseFloat(item.quantity) || 0);
      }, 0).toFixed(2);
    },
    // 总金额
    totalAmount() {
      return this.orderItemList.reduce((sum, item) => {
        return sum + (item.amount || 0);
      }, 0);
    }
  },
  created() {
    // 获取路由参数
    const orderId = this.$route.params.orderId;
    if (orderId) {
      this.isEdit = true;
      this.orderId = orderId;
      this.getOrderInfo();
    } else {
      this.isEdit = false;
      // 设置默认订单日期为当天
      this.form.orderDate = parseTime(new Date(), '{y}-{m}-{d}');
      // 生成默认订单编号
      this.generateOrderNo();
    }

    // 获取客户选项
    this.getPartnerOptions();
  },
  methods: {
    // 格式化金额显示
    formatAmount,

    // 格式化规格数据
    formatSpecData,

    // 返回列表页
    goBack() {
      // 使用浏览器历史返回
      this.$router.go(-1);
    },

    // 获取订单信息
    async getOrderInfo() {
      try {
        const response = await getSalesOrder(this.orderId);
        if (response.data) {
          const orderData = response.data;

          // 确保日期是字符串格式
          if (orderData.orderDate && !(typeof orderData.orderDate === 'string')) {
            orderData.orderDate = parseTime(new Date(orderData.orderDate), '{y}-{m}-{d}');
          }

          this.form = orderData;

          // 将分转换为元
          this.form.totalAmount = this.form.totalAmount / 100;
          this.form.discountAmount = this.form.discountAmount / 100;
          this.form.actualAmount = this.form.actualAmount / 100;
          this.form.paymentAmount = this.form.paymentAmount / 100;

          // 加载订单明细
          this.getOrderItemList();
        }
      } catch (error) {
        this.$message.error("获取订单信息失败");
        console.error("获取订单信息失败", error);
      }
    },

    // 获取客户选项
    getPartnerOptions() {
      listPartner({ status: 1 }).then(response => {
        this.partnerOptions = response.rows || [];
      });
    },

    // 生成订单编号
    generateOrderNo() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

      this.form.orderNo = `SO${year}${month}${day}${hours}${minutes}${seconds}${random}`;
    },

    // 打开商品选择器
    openProductSelector() {
      // 先关闭对话框
      this.productSelectorVisible = false;

      // 使用setTimeout确保在DOM完全更新后再打开对话框
      setTimeout(() => {
        this.productSelectorVisible = true;
      }, 300);
    },

    // 处理商品选择确认
    handleProductsSelected(products) {
      if (!products || products.length === 0) return;

      // 将选中的商品添加到订单明细列表
      this.orderItemList = [...this.orderItemList, ...products];

      // 更新订单总金额
      this.updateTotalAmount();

      // 显示成功消息
      this.$message.success(`成功添加 ${products.length} 个商品到订单明细`);
    },

    // 更新订单总金额
    updateTotalAmount() {
      // 计算订单总金额（元）
      this.form.totalAmount = this.totalAmount / 100;
      // 重新计算实际金额
      this.calculateActualAmount();
    },

    // 计算实际金额
    calculateActualAmount() {
      // 将金额转换为分
      const totalAmount = this.form.totalAmount * 100;
      const discountAmount = this.form.discountAmount * 100;
      let actualAmount = totalAmount - discountAmount;
      if (actualAmount < 0) actualAmount = 0;
      // 转回元并保留两位小数
      this.form.actualAmount = actualAmount / 100;
    },

    // 处理SKU选择变更
    handleSkuChange(skuId, orderItem) {
      if (!skuId || !orderItem.skuOptions) return;

      const selectedSku = orderItem.skuOptions.find(sku => sku.skuId === skuId);
      if (selectedSku) {
        orderItem.skuCode = selectedSku.skuCode;
        orderItem.specData = selectedSku.specData;
        orderItem.price = selectedSku.price || 0;
        // 更新金额
        orderItem.amount = Math.round(orderItem.price * orderItem.quantity * 100);
        // 更新订单总金额
        this.updateTotalAmount();
      }
    },

    // 处理数量变更
    handleQuantityChange(orderItem, value) {
      orderItem.quantity = value;
      // 更新金额
      orderItem.amount = Math.round(orderItem.price * orderItem.quantity * 100);
      // 更新订单总金额
      this.updateTotalAmount();
    },

    // 处理价格变更
    handlePriceChange(orderItem, value) {
      orderItem.price = value;
      // 更新金额
      orderItem.amount = Math.round(orderItem.price * orderItem.quantity * 100);
      // 更新订单总金额
      this.updateTotalAmount();
    },

    // 移除订单明细
    async removeOrderItem(index) {
      const orderItem = this.orderItemList[index];
      if (orderItem.itemId) {
        try {
          await this.$modal.confirm('确定要删除该订单明细吗？');
          await delSalesOrderItem(orderItem.itemId);
          this.orderItemList.splice(index, 1);
          this.$message.success("删除成功");
          // 更新订单总金额
          this.updateTotalAmount();
        } catch (error) {
          // 用户取消或删除失败
          if (error) {
            console.error("删除订单明细失败", error);
            this.$message.error("删除订单明细失败");
          }
        }
      } else {
        this.orderItemList.splice(index, 1);
        // 更新订单总金额
        this.updateTotalAmount();
      }
    },

    // 获取订单明细列表
    async getOrderItemList() {
      if (!this.orderId) {
        // 如果没有orderId，清空明细列表
        this.orderItemList = [];
        this.itemsLoading = false;
        return;
      }

      this.itemsLoading = true;
      try {
        const response = await listSalesOrderItem({ orderId: this.orderId });
        if (response.rows && response.rows.length > 0) {
          // 处理价格显示（后端存储单位为分，前端显示为元）
          this.orderItemList = response.rows.map(item => {
            return {
              ...item,
              price: item.price ? item.price / 100 : 0,
              // 保留amount为分单位，用于计算
              skuOptions: [] // 初始化为空数组，需要时再加载
            };
          });

          // 为每个明细项加载SKU选项
          for (const item of this.orderItemList) {
            if (item.productId) {
              try {
                const skuResponse = await this.$api.product.listProductSku({ productId: item.productId });
                item.skuOptions = (skuResponse.rows || []).map(sku => ({
                  ...sku,
                  price: sku.price ? sku.price / 100 : 0
                }));
              } catch (error) {
                console.error("获取SKU列表失败", error);
              }
            }
          }

          // 更新订单总金额
          this.updateTotalAmount();
        } else {
          this.orderItemList = [];
        }
      } catch (error) {
        console.error("获取订单明细列表失败", error);
        this.$message.error("获取订单明细列表失败");
      } finally {
        this.itemsLoading = false;
      }
    },

    // 保存订单明细数据
    async saveOrderItemData() {
      // 如果没有orderId，不进行保存操作
      if (!this.orderId) {
        console.warn("无法保存订单明细数据：缺少orderId");
        return [];
      }

      if (this.orderItemList.length === 0) {
        return [];
      }

      // 数据验证
      for (let i = 0; i < this.orderItemList.length; i++) {
        const item = this.orderItemList[i];
        if (!item.productId) {
          this.$message.warning(`第${i + 1}行商品不能为空`);
          return Promise.reject(new Error("商品不能为空"));
        }
        if (!item.skuId) {
          this.$message.warning(`第${i + 1}行SKU不能为空`);
          return Promise.reject(new Error("SKU不能为空"));
        }
        if (!item.quantity || item.quantity <= 0) {
          this.$message.warning(`第${i + 1}行数量必须大于0`);
          return Promise.reject(new Error("数量必须大于0"));
        }
      }

      // 处理数据，转换价格单位（元转分）
      const itemDataList = this.orderItemList.map(item => {
        const itemData = { ...item };
        // 确保每个明细都有orderId
        itemData.orderId = this.orderId;
        itemData.price = Math.round(item.price * 100);
        // amount已经是分单位，不需要转换
        // 移除不需要提交的字段
        delete itemData.skuOptions;
        return itemData;
      });

      // 区分新增和更新
      const newItems = itemDataList.filter(item => !item.itemId);
      const updateItems = itemDataList.filter(item => item.itemId);

      try {
        // 批量新增
        if (newItems.length > 0) {
          await batchAdd(newItems);
        }

        // 批量更新
        if (updateItems.length > 0) {
          await batchEdit(updateItems);
        }

        return this.orderItemList;
      } catch (error) {
        this.$message.error("保存失败：" + (error.message || "未知错误"));
        return Promise.reject(error);
      }
    },

    // 提交表单
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const submitData = { ...this.form };

          // 将金额转换为分
          submitData.totalAmount = Math.round(submitData.totalAmount * 100);
          submitData.discountAmount = Math.round(submitData.discountAmount * 100);
          submitData.actualAmount = Math.round(submitData.actualAmount * 100);
          submitData.paymentAmount = Math.round(submitData.paymentAmount * 100);

          // 保存订单明细
          const saveOrderItems = async () => {
            try {
              await this.saveOrderItemData();
              this.$modal.msgSuccess(this.isEdit ? "修改成功" : "新增成功");
              this.goBack();
            } catch (error) {
              this.$modal.msgError("保存订单明细失败：" + (error.message || "未知错误"));
            }
          };

          if (this.isEdit) {
            updateSalesOrder(submitData).then(() => {
              saveOrderItems();
            }).catch(error => {
              this.$modal.msgError("保存订单失败：" + (error.message || "未知错误"));
            });
          } else {
            addSalesOrder(submitData).then(response => {
              // 设置订单ID，用于保存订单明细
              this.orderId = response.data;
              submitData.orderId = response.data;
              saveOrderItems();
            }).catch(error => {
              this.$modal.msgError("保存订单失败：" + (error.message || "未知错误"));
            });
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-icon {
      margin-right: 5px;
    }
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .mt-10 {
    margin-top: 10px;
  }

  .form-footer {
    text-align: center;
    margin-top: 20px;
  }

  .total-info {
    .total-row {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 10px;

      .label {
        font-weight: bold;
        margin-right: 5px;
      }

      .value {
        min-width: 80px;
        text-align: right;

        &.amount {
          font-size: 16px;
          font-weight: bold;
          color: #f56c6c;
        }
      }

      .ml-20 {
        margin-left: 20px;
      }
    }
  }
}
</style>
