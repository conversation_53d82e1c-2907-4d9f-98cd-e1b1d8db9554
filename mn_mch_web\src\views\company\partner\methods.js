import { listPartner, getPartner, addPartner, update<PERSON>artner, delPartner } from "@/api/company/partner";
import { listCustomerLevel } from "@/api/pricing/price";
import { parseTime } from "@/utils/ruoyi";

export default {
  /** 查询合作伙伴列表 */
  getList() {
    this.loading = true;
    const params = {
      ...this.queryParams
    };
    // 处理日期范围
    if (this.dateRange && this.dateRange.length === 2) {
      params.beginTime = this.dateRange[0];
      params.endTime = this.dateRange[1];
    }

    listPartner(params).then(response => {
      this.partnerList = response.rows;
      this.total = response.total;
      this.loading = false;
    });
  },
  /** 搜索按钮操作 */
  handleQuery() {
    this.queryParams.pageNum = 1;
    this.getList();
  },
  /** 重置按钮操作 */
  resetQuery() {
    this.dateRange = [];
    this.resetForm("queryForm");
    this.handleQuery();
  },
  /** 新增按钮操作 */
  handleAdd() {
    this.reset();
    this.open = true;
    this.title = "添加合作伙伴";
    this.form.operationType = 'add';
  },
  /** 查看详情按钮操作 */
  handleDetail(row) {
    this.detailForm = { ...row };
    this.detailOpen = true;
  },
  /** 删除按钮操作 */
  handleDelete(row) {
    const partnerId = row.partnerId;
    this.$modal.confirm('是否确认删除合作伙伴ID为"' + partnerId + '"的数据项？').then(function () {
      return delPartner(partnerId);
    }).then(() => {
      this.getList();
      this.$modal.msgSuccess("删除成功");
    }).catch(() => { });
  },
  /** 取消按钮 */
  cancel() {
    this.open = false;
    this.reset();
  },
  // 表单重置
  reset() {
    this.form = {
      partnerId: undefined,
      partnerName: undefined,
      partnerCode: undefined,
      contactPerson: undefined,
      contactPhone: undefined,
      contactEmail: undefined,
      address: undefined,
      taxNumber: undefined,
      bankName: undefined,
      bankAccount: undefined,
      customerLevelId: undefined,
      status: 1,
      remark: undefined,
      createTime: undefined,
      updateTime: undefined,
      createBy: undefined,
      updateBy: undefined,
      operationType: undefined
    };
    this.resetForm("form");
  },
  /** 修改按钮操作 */
  handleUpdate(row) {
    this.reset();
    const partnerId = row.partnerId;
    getPartner(partnerId).then(response => {
      this.form = response.data;
      this.form.operationType = 'update';
      this.open = true;
      this.title = "修改合作伙伴信息";
    });
  },
  /** 提交按钮 */
  submitForm() {
    this.$refs["form"].validate(valid => {
      if (valid) {
        const submitData = { ...this.form };

        if (submitData.operationType === 'update') {
          updatePartner(submitData).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
        } else {
          addPartner(submitData).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      }
    });
  },
  /** 获取客户等级列表 */
  getLevelList() {
    listCustomerLevel().then(response => {
      this.levelList = response.rows || [];
    });
  }
};