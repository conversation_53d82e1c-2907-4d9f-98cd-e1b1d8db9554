<template>
  <div class="level-price-manager">
    <div class="manager-header">
      <h4>等级价格设置</h4>
      <el-button type="primary" size="small" @click="handleAdd">
        <i class="el-icon-plus"></i> 添加等级价格
      </el-button>
    </div>

    <div class="tips">
      <el-alert
        title="等级价格说明"
        type="info"
        :closable="false"
        show-icon
      >
        <p>等级价格是针对不同客户等级设置的专属价格，优先级高于基础价格。</p>
        <p>如果不设置等级价格，系统将使用基础价格 × 等级折扣率计算。</p>
      </el-alert>
    </div>

    <el-table :data="levelPriceList" border style="width: 100%">
      <el-table-column label="客户等级" width="150">
        <template slot-scope="scope">
          <el-select 
            v-model="scope.row.levelId" 
            placeholder="选择客户等级"
            style="width: 100%"
            @change="handleLevelChange(scope.row)"
          >
            <el-option
              v-for="level in customerLevels"
              :key="level.levelId"
              :label="level.levelName"
              :value="level.levelId"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="SKU" width="120">
        <template slot-scope="scope">
          <el-select 
            v-model="scope.row.skuId" 
            placeholder="选择SKU"
            style="width: 100%"
            clearable
          >
            <el-option label="全部SKU" :value="null" />
            <el-option
              v-for="sku in skuList"
              :key="sku.skuId"
              :label="getSkuDisplay(sku)"
              :value="sku.skuId"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="等级价格(元)" width="150">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.priceYuan"
            :min="0"
            :precision="2"
            placeholder="等级价格"
            @change="handlePriceChange(scope.row)"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="折扣率" width="120">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.discountRate"
            :min="0"
            :max="1"
            :precision="4"
            placeholder="折扣率"
            @change="handleDiscountRateChange(scope.row)"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="生效日期" width="150">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.effectiveDate"
            type="date"
            placeholder="生效日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="失效日期" width="150">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.expireDate"
            type="date"
            placeholder="失效日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleDelete(scope.$index)"
            style="color: #f56c6c"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="levelPriceList.length === 0" class="empty-state">
      <i class="el-icon-user"></i>
      <p>暂无等级价格，点击"添加等级价格"按钮添加</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "LevelPriceManager",
  props: {
    value: {
      type: Array,
      default: () => []
    },
    skuList: {
      type: Array,
      default: () => []
    },
    hasSpec: {
      type: Boolean,
      default: false
    },
    customerLevels: {
      type: Array,
      default: () => []
    },
    basePriceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      levelPriceList: []
    };
  },
  watch: {
    value: {
      handler(val) {
        this.levelPriceList = val.map(item => ({
          ...item,
          priceYuan: item.price ? (item.price / 100) : 0,
          effectiveDate: this.formatDateFromTimestamp(item.effectiveDate),
          expireDate: this.formatDateFromTimestamp(item.expireDate)
        }));
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /** 添加等级价格 */
    handleAdd() {
      this.levelPriceList.push({
        levelId: null,
        skuId: null,
        price: 0,
        priceYuan: 0,
        discountRate: null,
        effectiveDate: this.getCurrentDate(),
        expireDate: null,
        status: true
      });
      this.emitChange();
    },
    /** 删除等级价格 */
    handleDelete(index) {
      this.levelPriceList.splice(index, 1);
      this.emitChange();
    },
    /** 等级变化处理 */
    handleLevelChange(row) {
      const level = this.customerLevels.find(item => item.levelId === row.levelId);
      if (level && level.discountRate && !row.discountRate) {
        row.discountRate = level.discountRate;
      }
      this.emitChange();
    },
    /** 价格变化处理 */
    handlePriceChange(row) {
      row.price = Math.round(row.priceYuan * 100);

      // 根据价格计算折扣率
      const basePrice = this.getBasePriceForSku(row.skuId);
      if (basePrice > 0 && row.priceYuan > 0) {
        row.discountRate = parseFloat((row.priceYuan / (basePrice / 100)).toFixed(4));
      }

      this.emitChange();
    },
    /** 折扣率变化处理 */
    handleDiscountRateChange(row) {
      // 根据折扣率计算价格
      const basePrice = this.getBasePriceForSku(row.skuId);
      if (basePrice > 0 && row.discountRate > 0) {
        row.priceYuan = parseFloat(((basePrice / 100) * row.discountRate).toFixed(2));
        row.price = Math.round(row.priceYuan * 100);
      }

      this.emitChange();
    },
    /** 获取SKU的基础价格 */
    getBasePriceForSku(skuId) {
      if (!skuId || !this.basePriceList) return 0;

      const basePrice = this.basePriceList.find(item => item.skuId === skuId);
      return basePrice ? basePrice.basePrice : 0;
    },
    /** 获取SKU显示名称 */
    getSkuDisplay(sku) {
      return sku.skuCode || `SKU-${sku.skuId}`;
    },
    /** 获取当前日期 */
    getCurrentDate() {
      const now = new Date();
      return now.getFullYear() + '-' +
             String(now.getMonth() + 1).padStart(2, '0') + '-' +
             String(now.getDate()).padStart(2, '0');
    },
    /** 格式化时间戳为日期字符串 */
    formatDateFromTimestamp(timestamp) {
      if (!timestamp) return null;

      // 如果已经是字符串格式，直接返回
      if (typeof timestamp === 'string') {
        return timestamp;
      }

      // 如果是时间戳，转换为日期字符串
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) return null;

      return date.getFullYear() + '-' +
             String(date.getMonth() + 1).padStart(2, '0') + '-' +
             String(date.getDate()).padStart(2, '0');
    },
    /** 发送变化事件 */
    emitChange() {
      const result = this.levelPriceList.map(item => ({
        ...item,
        price: Math.round((item.priceYuan || 0) * 100)
      }));
      this.$emit('input', result);
    }
  }
};
</script>

<style lang="scss" scoped>
.level-price-manager {
  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h4 {
      margin: 0;
      color: #303133;
    }
  }
  
  .tips {
    margin-bottom: 20px;
    
    p {
      margin: 5px 0;
      font-size: 13px;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #909399;
    
    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
}
</style>
