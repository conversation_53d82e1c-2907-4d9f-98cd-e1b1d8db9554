.app-container {
  .detail-title {
    margin-top: 20px;
    padding-left: 10px;
    font-size: 16px;
    font-weight: bold;
    border-left: 4px solid #409EFF;
  }

  .el-table {
    margin-top: 15px;

    .success-row {
      background-color: rgba(103, 194, 58, 0.1);
    }

    .warning-row {
      background-color: rgba(230, 162, 60, 0.1);
    }
  }

  .text-center {
    text-align: center;
  }
}

.time-info {
  .time-detail {
    font-size: 12px;
    color: #909399;
    margin-top: 3px;
  }
}

// 订单详情样式
.order-detail-header {
  margin-bottom: 10px;

  .order-basic-info {
    width: 100%;

    .info-item {
      font-size: 14px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;

      .label {
        color: #606266;
        margin-right: 8px;
        min-width: 80px;
      }

      .value {
        font-weight: 500;
        color: #303133;
      }
    }

    .order-time {
      color: #606266;
      margin-bottom: 8px;
      font-size: 14px;

      i {
        margin-right: 6px;
        color: #909399;
      }
    }
  }
}

.order-info-section {
  margin-bottom: 25px;

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    position: relative;

    i {
      font-size: 18px;
      margin-right: 8px;
      color: #409EFF;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }

  .order-info-container {
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    min-height: 60px;
  }

  .remark-content {
    padding: 10px;
    line-height: 1.6;
    color: #606266;
  }
}

.float-right {
  float: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb8 {
  margin-bottom: 8px;
}

// 金额显示样式
.amount-info {
  font-weight: bold;

  &.total {
    color: #409EFF;
    font-size: 16px;
  }

  &.discount {
    color: #E6A23C;
  }

  &.actual {
    color: #67C23A;
  }

  &.payment {
    color: #F56C6C;
  }
}

// 订单状态标签样式
.order-status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;

  &.pending {
    background-color: #E6A23C;
    color: #fff;
  }

  &.approved {
    background-color: #67C23A;
    color: #fff;
  }
}

// 付款状态标签样式
.payment-status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;

  &.unpaid {
    background-color: #F56C6C;
    color: #fff;
  }

  &.paid {
    background-color: #67C23A;
    color: #fff;
  }
}


// 添加订单明细组件相关样式
.order-item-section {
  margin-top: 20px;
}

// 确保对话框在全屏模式下有足够的空间
.el-dialog__body {
  padding: 20px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

// 确保卡片之间有足够的间距（已在上面定义）

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

// 金额盒子样式
.amount-box {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 15px;
  text-align: center;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .amount-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 10px;
  }

  .amount-value {
    font-size: 20px;
    font-weight: bold;

    &.total {
      color: #409EFF;
    }

    &.discount {
      color: #E6A23C;
    }

    &.actual {
      color: #67C23A;
    }

    &.payment {
      color: #F56C6C;
    }
  }
}

// 备注盒子样式
.remark-box {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 15px;

  .remark-label {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 10px;

    i {
      margin-right: 5px;
      color: #409EFF;
    }
  }

  .remark-content {
    padding: 10px;
    line-height: 1.6;
    color: #606266;
    background-color: #fff;
    border-radius: 4px;
    min-height: 60px;
  }
}

// 卡片头部样式
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-icon {
    margin-right: 5px;
    color: #409EFF;
  }
}

// 订单操作区域样式
.order-actions {
  padding: 10px 0;

  .action-box {
    background-color: #f8f8f8;
    border-radius: 6px;
    padding: 20px;
    border-left: 4px solid #409EFF;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .action-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 10px;
      color: #303133;

      .status-text {
        font-weight: bold;

        &.pending {
          color: #E6A23C;
        }

        &.approved {
          color: #67C23A;
        }
      }
    }

    .action-desc {
      color: #606266;
      margin-bottom: 20px;
      font-size: 14px;
    }

    .action-buttons {
      display: flex;
      gap: 10px;

      .el-button {
        padding: 10px 20px;
        font-size: 14px;
      }
    }
  }
}

// 价格计算相关样式
.price-container {
  position: relative;

  .price-type-tag {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #409EFF;
    color: white;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    z-index: 10;

    &.tier-price {
      background-color: #E6A23C;
    }

    &.level-price {
      background-color: #67C23A;
    }

    &.base-price {
      background-color: #909399;
    }
  }

  .calculating-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5;

    .el-icon-loading {
      color: #409EFF;
      font-size: 14px;
    }
  }
}

// 价格计算状态样式
.price-calculating {
  .el-input-number {
    opacity: 0.6;
  }
}

// 合计信息样式
.total-info {
  .total-row {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px 0;

    .label {
      font-size: 14px;
      color: #606266;
      margin-right: 8px;
    }

    .value {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-right: 20px;

      &.amount {
        color: #409EFF;
        font-size: 18px;
      }
    }

    .ml-20 {
      margin-left: 20px;
    }
  }
}
