package com.mn.controller;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mn.entity.NyDept;
import com.mn.form.DeptForm;
import com.mn.service.INyDeptService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;
import com.mn.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@RestController
@RequestMapping("/dept")
@Slf4j
public class NyDeptController extends BaseController {

    @Resource
    INyDeptService deptService;

    /**
     * 获取部门列表
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list")
    public AjaxResult list(DeptForm dept) {
        List<NyDept> depts = deptService.listDept(dept);
        return success(depts);
    }

    /**
     * 查询部门列表（排除节点）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId) {
        List<NyDept> depts = deptService.listDept(new DeptForm());
        // log.info("处理前:{}", JSON.toJSONString(depts));
        depts.removeIf(d -> d.getDeptId().intValue() == deptId
                || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
        // log.info("处理后:{}", JSON.toJSONString(depts));
        return success(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Integer deptId) {
        return success(deptService.getById(deptId));
    }

    /**
     * 新增部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody NyDept dept) {
        dept.setCreateBy(getUsername());
        return toAjax(deptService.addDept(dept));
    }

    /**
     * 修改部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @PostMapping("/edit")
    public AjaxResult editDept(@RequestBody NyDept dept) {
        dept.setUpdateBy(getUsername());
        return toAjax(deptService.editDept(dept));
    }

    /**
     * 删除部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @PostMapping("/del")
    public AjaxResult del(Integer deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            return warn("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            return warn("部门存在用户,不允许删除");
        }
        return toAjax(deptService.removeById(deptId));
    }

    /**
     * 获取集团详细信息（包含完整信息）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping("/detail/{deptId}")
    public AjaxResult getDeptDetail(@PathVariable Integer deptId) {
        NyDept dept = deptService.getById(deptId);
        if (dept == null) {
            return error("集团信息不存在");
        }
        return success(dept);
    }

    /**
     * 更新集团状态
     */
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @PostMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody NyDept dept) {
        dept.setUpdateBy(getUsername());
        dept.setUpdateTime(DateUtils.getNowDate());
        return toAjax(deptService.updateById(dept));
    }

}
