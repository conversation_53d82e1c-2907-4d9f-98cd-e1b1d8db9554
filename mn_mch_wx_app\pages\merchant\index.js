// 引入API请求模块
import API from '../../api/request';

Page({
  data: {
    merchantName: '商户名称',
    amount: '',
    remark: '',
    uid: 'f759d4e8d63b4e059e86f00dcd8f6738', // 保存uid
    outPaymentId: '', // 保存订单号
    isSubmitting: false, // 防止重复提交
    needCheckStatus: false, // 新增：标记是否需要检查订单状态
  },
  
  onLoad: function(options) {
    // 打印日志options
    console.log('加载'+JSON.stringify(options));
    
    // 解码URL并提取uid
    if (options.q) {
      const decodedUrl = decodeURIComponent(options.q);
      // 使用正则表达式提取uid参数
      const uidMatch = decodedUrl.match(/[?&]uid=([^&]+)/);
      const uid = uidMatch ? uidMatch[1] : null;
      console.log('用户ID:', uid);
      if (uid) {
        this.setData({ uid: uid });
      }
    }
    
    // 获取商户信息
    this.getMerchantInfo(this.data.uid);
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },
  
  getMerchantInfo: function(uid) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    
    API.getMerchantInfo(uid).then(res => {
      if (res.data) {
        this.setData({
          merchantName: res.data.merchantName
        });
      }
      wx.hideLoading();
    }).catch(err => {
      console.error('获取商户信息失败', err);
      wx.showToast({
        title: '获取商户信息失败',
        icon: 'none'
      });
      wx.hideLoading();
    });
  },
  
  handleInputAmount: function(e) {
    // 限制只能输入数字和小数点，且小数点后最多两位
    let value = e.detail.value;
    if (value) {
      // 如果有多个小数点，只保留第一个
      if (value.indexOf('.') !== value.lastIndexOf('.')) {
        value = value.substr(0, value.lastIndexOf('.'));
      }
      
      // 限制小数点后两位
      if (value.indexOf('.') !== -1) {
        const decimal = value.substr(value.indexOf('.') + 1);
        if (decimal.length > 2) {
          value = value.substr(0, value.indexOf('.') + 3);
        }
      }
    }
    
    this.setData({
      amount: value
    });
  },
  
  handleAddRemark: function() {
    wx.showModal({
      title: '添加备注',
      placeholderText: '请输入备注信息',
      editable: true,
      content: this.data.remark,
      success: (res) => {
        if (res.confirm && res.content) {
          this.setData({
            remark: res.content
          });
        }
      }
    });
  },
  
  handlePayment: function() {
    // 验证金额
    if (!this.data.amount || parseFloat(this.data.amount) <= 0) {
      wx.showToast({
        title: '请输入有效金额',
        icon: 'none'
      });
      return;
    }
    
    // 防止重复点击
    if (this.data.isSubmitting) {
      return;
    }
    
    this.setData({
      isSubmitting: true
    });
    
    // 处理支付逻辑
    wx.showLoading({
      title: '发起支付...',
      mask: true
    });
    
    // 构建支付参数
    const params = {
      uid: this.data.uid,
      amount: Math.round(parseFloat(this.data.amount) * 100), // 转换为分，并四舍五入
      remark: this.data.remark || ''
    };
    
    // 调用支付接口
    API.payByUser(params).then(res => {
      wx.hideLoading();
      
      if (res.code === 0 && res.data && res.data.miniProgram) {
        const mpInfo = res.data.miniProgram;
        
        // 保存订单号
        if (res.data.outPaymentId) {
          this.setData({
            outPaymentId: res.data.outPaymentId,
            needCheckStatus: true // 设置标记，表示需要在返回时检查订单状态
          });
        }
        
        console.log('准备跳转小程序', mpInfo);
        
        // 跳转到其他小程序
        wx.navigateToMiniProgram({
          appId: mpInfo.mpAppid,
          path: mpInfo.mpPath,
          extraData: {},
          envVersion: 'trial', // 根据环境设置版本
          success: (result) => {
            console.log('跳转成功', result);
            // 不再立即查询订单状态，而是等待用户返回
          },
          fail: (err) => {
            console.error('跳转失败', err);
            this.setData({
              isSubmitting: false
            });
            wx.showToast({
              title: '跳转失败: ' + (err.errMsg || '未知错误'),
              icon: 'none',
              duration: 3000
            });
          }
        });
      } else {
        console.error('支付数据格式不正确', res);
        this.setData({
          isSubmitting: false
        });
        wx.showToast({
          title: '获取支付信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      this.setData({
        isSubmitting: false
      });
      console.error('支付失败', err);
      wx.showToast({
        title: '支付失败: ' + (err.message || '未知错误'),
        icon: 'none',
        duration: 3000
      });
    });
  },
  
  // 页面显示时触发
  onShow: function() {
    // 如果需要检查订单状态，说明用户从支付小程序返回
    if (this.data.needCheckStatus && this.data.outPaymentId) {
      console.log('用户从支付小程序返回，开始查询订单状态');
      // 立即显示加载提示
      wx.showLoading({
        title: '正在查询支付结果...',
        mask: true
      });
      this.checkOrderStatus();
      // 重置标记，避免重复查询
      this.setData({
        needCheckStatus: false
      });
    }
  },
  
  // 检查订单状态
  checkOrderStatus: function() {
    // 获取最新的outPaymentId
    const outPaymentId = this.data.outPaymentId;
    if (!outPaymentId) {
      console.error('没有找到订单号，无法查询订单状态');
      wx.hideLoading();
      return;
    }
    
    // 设置一个定时器，每隔几秒查询一次订单状态
    let checkCount = 0;
    const maxChecks = 5; // 增加查询次数
    
    const statusTimer = setInterval(() => {
      checkCount++;
      if (checkCount > maxChecks) {
        clearInterval(statusTimer);
        this.setData({
          isSubmitting: false
        });
        wx.hideLoading(); // 隐藏加载提示
        wx.showModal({
          title: '查询超时',
          content: '订单处理中，暂未查询到订单状态',
          showCancel: false
        });
        return;
      }
      
      API.queryOrder(outPaymentId).then(res => {
        console.log('查询订单状态:', res);
        if (res.code === 0 && res.data) {
          const orderStatus = res.data.status;
          
          if (orderStatus === 'SUCCEEDED') {
            // 订单支付成功
            clearInterval(statusTimer);
            this.setData({
              isSubmitting: false
            });
            wx.hideLoading(); // 隐藏加载提示
            
            // 弹出确认框
            wx.showModal({
              title: '支付成功',
              content: `支付金额：¥${(res.data.amount / 100).toFixed(2)}`,
              showCancel: false,
              success: () => {
                // 重置付款金额和备注
                this.setData({
                  amount: '',
                  remark: ''
                });
              }
            });
            
          } else if (orderStatus === 'CLOSED') {
            // 订单已关闭
            clearInterval(statusTimer);
            this.setData({
              isSubmitting: false
            });
            wx.hideLoading(); // 隐藏加载提示
            
            wx.showModal({
              title: '订单已关闭',
              content: '支付未完成，订单已关闭',
              showCancel: false,
              success: () => {
                // 重置付款金额和备注
                this.setData({
                  amount: '',
                  remark: ''
                });
              }
            });
          }
          // 如果是PROCESSING状态，继续查询
        }
      }).catch(err => {
        console.error('查询订单状态失败', err);
        // 查询失败不中断定时器，继续尝试
      });
    }, 2000); // 每2秒查询一次
  }
});