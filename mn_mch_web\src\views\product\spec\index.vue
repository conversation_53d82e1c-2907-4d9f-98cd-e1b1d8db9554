<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px" size="small">
      <el-form-item label="规格名称" prop="specName">
        <el-input v-model="queryParams.specName" placeholder="请输入规格名称" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker v-model="dateRange" size="small" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增规格</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="specList" border stripe highlight-current-row>
      <el-table-column label="规格名称" align="center" prop="specName" min-width="150">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.specName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="规格值" align="center" prop="specValues" min-width="200">
        <template slot-scope="scope">
          <el-tag v-for="(value, index) in parseSpecValues(scope.row.specValues)" :key="index"
            size="small" style="margin-right: 5px; margin-bottom: 5px;">
            {{ value }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="显示顺序" align="center" prop="sortOrder" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <div class="time-info">
            <div>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</div>
            <div class="time-detail">{{ parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" icon="el-icon-view" circle @click="handleDetail(scope.row)"
            title="查看详情"></el-button>
          <el-button size="mini" type="warning" icon="el-icon-edit" circle @click="handleUpdate(scope.row)"
            title="修改规格"></el-button>
          <el-button size="mini" type="danger" icon="el-icon-delete" circle @click="handleDelete(scope.row)"
            title="删除规格"></el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改规格对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <!-- 基本信息卡片 -->
        <el-card class="box-card mb-20">
          <div slot="header" class="card-header">
            <span><i class="el-icon-document"></i> 基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="规格名称" prop="specName">
                <el-input v-model="form.specName" placeholder="请输入规格名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显示顺序" prop="sortOrder">
                <el-input-number v-model="form.sortOrder" :min="0" :max="999" controls-position="right" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 规格值卡片 -->
        <el-card class="box-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-collection-tag"></i> 规格值</span>
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addSpecValue"
              class="float-right">添加规格值</el-button>
          </div>

          <!-- 规格值表格 -->
          <el-table :data="specValuesList" border size="small" class="mb-10">
            <el-table-column type="index" label="#" width="60" align="center"></el-table-column>
            <el-table-column label="规格值" min-width="220">
              <template slot-scope="scope">
                <el-input v-model="scope.row.value" placeholder="请输入规格值" size="small" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeSpecValue(scope.$index)"
                  v-if="specValuesList.length > 1"></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 规格详情对话框 -->
    <el-dialog title="规格详情" :visible.sync="detailOpen" width="700px" append-to-body>
      <!-- 规格基本信息 -->
      <div class="spec-detail-header">
        <!-- 左侧规格信息 -->
        <div class="spec-basic-info">
          <div class="spec-id">
            <span class="label">规格ID：</span>
            <span class="value">{{ detailForm && detailForm.specId }}</span>
          </div>
          <div class="spec-name">
            <span class="label">规格名称：</span>
            <span class="value">{{ detailForm && detailForm.specName }}</span>
          </div>
          <div class="spec-sort">
            <span class="label">显示顺序：</span>
            <span class="value">{{ detailForm && detailForm.sortOrder }}</span>
          </div>
          <div class="spec-time">
            <i class="el-icon-time"></i>
            <span>创建时间：{{ detailForm && parseTime(detailForm.createTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 规格值信息 -->
      <div class="spec-info-section">
        <div class="section-header">
          <i class="el-icon-collection-tag"></i>
          <span>规格值列表</span>
        </div>
        <div class="spec-values-container">
          <el-tag v-for="(value, index) in detailSpecValues" :key="index"
            size="medium" style="margin-right: 10px; margin-bottom: 10px;">
            {{ value }}
          </el-tag>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" icon="el-icon-close" @click="detailOpen = false">关闭详情</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";
import specMethods from "./methods";

export default {
  name: "ProductSpec",
  components: {
    Pagination: () => import("@/components/Pagination"),
    RightToolbar: () => import("@/components/RightToolbar")
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 规格表格数据
      specList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        specName: undefined
      },
      // 规格值列表
      specValuesList: [
        {
          value: ''
        }
      ],
      // 详情规格值
      detailSpecValues: [],
      // 表单参数
      form: {
        specId: undefined,
        specName: undefined,
        specValues: undefined,
        sortOrder: 0,
        createTime: undefined,
        operationType: undefined // 操作类型：add-新增，update-修改
      },
      // 详情表单
      detailForm: {},
      // 表单校验规则
      rules: {
        specName: [
          { required: true, message: "请输入规格名称", trigger: "blur" },
          { min: 2, max: 50, message: "规格名称长度必须在2到50个字符之间", trigger: "blur" }
        ],
        sortOrder: [
          { required: true, message: "请输入显示顺序", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    ...specMethods,
    parseTime
  }
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>