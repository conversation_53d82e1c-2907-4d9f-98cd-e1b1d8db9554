package com.mn.controller;

import com.mn.entity.ProductSpec;
import com.mn.form.ProductSpecForm;
import com.mn.model.TableDataInfo;
import com.mn.service.IProductSpecService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 商品规格表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@RestController
@RequestMapping("/product-spec")
public class ProductSpecController extends BaseController {

    @Resource
    private IProductSpecService productSpecService;

    /**
     * 获取规格列表
     */
    @PreAuthorize("@ss.hasPermi('product:spec:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductSpecForm form) {
        startPage();
        form.setDeptId(getDeptId());
        List<ProductSpec> list = productSpecService.selectSpecList(form);
        return getDataTable(list);
    }

    /**
     * 根据规格ID获取详细信息
     */
    //@PreAuthorize("@ss.hasPermi('product:spec:query')")
    @GetMapping(value = "/{specId}")
    public AjaxResult getInfo(@PathVariable Long specId) {
        return success(productSpecService.getById(specId));
    }

    /**
     * 新增规格
     */
    //@PreAuthorize("@ss.hasPermi('product:spec:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ProductSpec spec) {
        spec.setCreateTime(DateUtils.getNowDate());
        spec.setDeptId(getDeptId());
        return toAjax(productSpecService.insertSpec(spec));
    }

    /**
     * 修改规格
     */
    //@PreAuthorize("@ss.hasPermi('product:spec:edit')")
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody ProductSpec spec) {
        spec.setUpdateTime(DateUtils.getNowDate());
        return toAjax(productSpecService.updateSpec(spec));
    }

    /**
     * 删除规格
     */
    //@PreAuthorize("@ss.hasPermi('product:spec:remove')")
    @PostMapping("/del")
    public AjaxResult remove(Long specId) {
        return toAjax(productSpecService.deleteBySpecId(specId));
    }
}
