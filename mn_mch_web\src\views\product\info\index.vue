<template>
    <div class="app-container">
        <!-- 搜索区域 -->
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px"
            size="small">
            <el-form-item label="商品名称" prop="productName">
                <el-input v-model="queryParams.productName" placeholder="请输入商品名称" clearable style="width: 200px" />
            </el-form-item>
            <el-form-item label="商品编码" prop="productCode">
                <el-input v-model="queryParams.productCode" placeholder="请输入商品编码" clearable style="width: 200px" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 200px">
                    <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <!-- 操作按钮区 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增商品</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 数据表格 -->
        <el-table v-loading="loading" :data="productList" border stripe highlight-current-row>
            <el-table-column label="商品图片" align="center" width="100">
                <template slot-scope="scope">
                    <el-image style="width: 60px; height: 60px" :src="scope.row.mainImage" fit="cover"
                        :preview-src-list="[scope.row.mainImage]">
                        <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                        </div>
                    </el-image>
                </template>
            </el-table-column>
            <el-table-column label="商品名称" align="center" prop="productName" min-width="150">
                <template slot-scope="scope">
                    <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.productName }}</el-link>
                </template>
            </el-table-column>
            <el-table-column label="商品编码" align="center" prop="productCode" width="120" />
            <!--<el-table-column label="价格" align="center" prop="price" width="100">
                <template slot-scope="scope">
                    <span class="price-text">¥{{ (scope.row.price / 100).toFixed(2) }}</span>
                </template>
            </el-table-column>
            -->
            <el-table-column label="库存" align="center" prop="stock" width="80" />
            <el-table-column label="销量" align="center" prop="sales" width="80" />
            <el-table-column label="状态" align="center" prop="status" width="80">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.status ? 'success' : 'info'" effect="light">
                        {{ scope.row.status ? '上架' : '下架' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" width="160">
                <template slot-scope="scope">
                    <div class="time-info">
                        <div>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</div>
                        <div class="time-detail">{{ parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180" fixed="right">
                <template slot-scope="scope">
                    <el-button size="mini" type="primary" icon="el-icon-view" circle @click="handleDetail(scope.row)"
                        title="查看详情"></el-button>
                    <el-button size="mini" type="warning" icon="el-icon-edit" circle @click="handleUpdate(scope.row)"
                        title="修改商品"></el-button>
                    <el-button size="mini" type="danger" icon="el-icon-delete" circle @click="handleDelete(scope.row)"
                        title="删除商品"></el-button>
                    <el-button size="mini" :type="scope.row.status ? 'info' : 'success'" icon="el-icon-sort" circle
                        @click="handleToggleStatus(scope.row)" :title="scope.row.status ? '下架' : '上架'"></el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改商品对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body fullscreen>
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                <!-- 基本信息卡片 -->
                <el-card class="box-card mb-20">
                    <div slot="header" class="card-header">
                        <span><i class="el-icon-document"></i> 基本信息</span>
                    </div>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="商品主图" prop="mainImage">
                                <el-upload class="avatar-uploader" action="/webapi/common/upload" :headers="getHeaders()"
                                    :show-file-list="false" :on-success="handleImageSuccess" :before-upload="beforeImageUpload">
                                    <img v-if="form.mainImage" :src="form.mainImage" class="avatar">
                                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                </el-upload>
                            </el-form-item>
                        </el-col>
                        <el-col :span="16">
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="商品名称" prop="productName">
                                        <el-input v-model="form.productName" placeholder="请输入商品名称" clearable />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="商品编码" prop="productCode">
                                        <el-input v-model="form.productCode" placeholder="请输入商品编码" clearable />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="商品分类" prop="categoryId">
                                        <treeselect v-model="form.categoryId" :options="categoryOptions"
                                            :normalizer="normalizer" placeholder="选择商品分类" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="商品单位" prop="unit">
                                        <el-input v-model="form.unit" placeholder="请输入商品单位" clearable />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="显示顺序" prop="sortOrder">
                                        <el-input-number v-model="form.sortOrder" :min="0" :max="999" controls-position="right"
                                            style="width: 100%" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="商品简介" prop="productBrief">
                                <el-input v-model="form.productBrief" type="textarea" :rows="3" placeholder="请输入商品简介" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>

                <!-- 商品SKU管理卡片 -->
                <product-sku-edit
                    ref="skuEditor"
                    :productId="form.productId"
                    :productName="form.productName"
                    :productCode="form.productCode"
                />
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-dialog>

        <!-- 商品详情对话框 -->
        <el-dialog title="商品详情" :visible.sync="detailOpen" width="800px" append-to-body>
            <!-- 商品基本信息 -->
            <div class="product-detail-header">
                <div class="product-image">
                    <el-image style="width: 120px; height: 120px" :src="detailForm.mainImage" fit="cover"
                        :preview-src-list="[detailForm.mainImage]">
                        <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                        </div>
                    </el-image>
                </div>
                <div class="product-basic-info">
                    <div class="product-name">{{ detailForm.productName }}</div>
                    <div class="product-code">商品编码：{{ detailForm.productCode }}</div>
                    <div class="product-price">¥{{ detailForm.price ? (detailForm.price / 100).toFixed(2) : '0.00' }}
                    </div>
                    <div class="product-stats">
                        <span>库存：{{ detailForm.stock || 0 }}</span>
                        <span>销量：{{ detailForm.sales || 0 }}</span>
                        <span>单位：{{ detailForm.unit || '-' }}</span>
                    </div>
                    <div class="product-status">
                        <el-tag :type="detailForm.status ? 'success' : 'info'">
                            {{ detailForm.status ? '上架中' : '已下架' }}
                        </el-tag>
                    </div>
                </div>
            </div>

            <!-- 商品详细信息 -->
            <el-tabs type="border-card" class="detail-tabs">
                <el-tab-pane label="基本信息">
                    <div class="detail-item">
                        <span class="detail-label">商品分类：</span>
                        <span class="detail-value">{{ getCategoryName(detailForm.categoryId) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">商品简介：</span>
                        <span class="detail-value">{{ detailForm.productBrief || '暂无简介' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">创建时间：</span>
                        <span class="detail-value">{{ parseTime(detailForm.createTime) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">更新时间：</span>
                        <span class="detail-value">{{ parseTime(detailForm.updateTime) }}</span>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="规格信息" v-if="detailForm.specData">
                    <div class="spec-info-section">
                        <div class="section-header">
                            <i class="el-icon-collection-tag"></i>
                            <span>规格组合</span>
                        </div>
                        <el-table :data="detailSkuList" border size="small" class="mb-10">
                            <el-table-column label="SKU编码" prop="skuCode" min-width="120" align="center" />

                            <!-- 规格值列 - 动态生成 -->
                            <el-table-column
                              v-for="(spec, index) in detailSpecColumns"
                              :key="index"
                              :label="spec.specName"
                              align="center"
                              min-width="120"
                            >
                              <template slot-scope="scope">
                                {{ getSpecValueBySpecId(scope.row, spec.specId) }}
                              </template>
                            </el-table-column>

                            <el-table-column label="价格" width="120" align="center">
                                <template slot-scope="scope">
                                    <span class="price-text">¥{{ (scope.row.price / 100).toFixed(2) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="库存" width="80" align="center" prop="stock" />
                            <el-table-column label="状态" width="80" align="center">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.status ? 'success' : 'info'">
                                        {{ scope.row.status ? '启用' : '禁用' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
            </el-tabs>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" icon="el-icon-close" @click="detailOpen = false">关闭详情</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";
import { getToken } from "@/utils/auth";
import productMethods from "./methods";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import ProductSkuEdit from "@/components/ProductSku/productSkuEdit.vue";

export default {
    name: "ProductInfo",
    components: {
        Treeselect,
        ProductSkuEdit
    },
    data() {
        return {
            // 遮罩层
            loading: false,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 商品表格数据
            productList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 是否显示详情弹出层
            detailOpen: false,
            // 日期范围
            dateRange: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                productName: undefined,
                productCode: undefined,
                status: undefined
            },
            // 状态选项
            statusOptions: [
                { value: true, label: "上架" },
                { value: false, label: "下架" }
            ],
            // 分类选项
            categoryOptions: [],
            // 详情SKU列表
            detailSkuList: [],
            // 详情规格列
            detailSpecColumns: [],

            // 上传头部
            headers: {
                Authorization: getToken()
            },
            // 表单参数
            form: {
                productId: undefined,
                deptId: undefined,
                categoryId: undefined,
                productName: undefined,
                productCode: undefined,
                productBrief: undefined,
                mainImage: undefined,
                specData: undefined,
                price: 0,
                stock: 0,
                sales: 0,
                unit: "件",
                weight: 0,

                status: true,
                sortOrder: 0,
                createTime: undefined,
                updateTime: undefined,
                createBy: undefined,
                updateBy: undefined,
                operationType: undefined // 操作类型：add-新增，update-修改
            },
            // 详情表单
            detailForm: {},
            // 表单校验规则
            rules: {
                productName: [
                    { required: true, message: "请输入商品名称", trigger: "blur" },
                    { min: 2, max: 100, message: "商品名称长度必须在2到100个字符之间", trigger: "blur" }
                ],
                categoryId: [
                    { required: true, message: "请选择商品分类", trigger: "change" }
                ]
            }
        };
    },
    created() {
        this.getList();
        this.getCategoryOptions();
        this.getSpecOptions();
    },
    methods: productMethods // 直接将整个对象赋值给 methods
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>