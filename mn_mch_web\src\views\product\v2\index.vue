<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-row">
      <el-col :span="4" v-for="stat in statistics" :key="stat.key">
        <div class="stat-card">
          <div class="stat-icon" :class="stat.iconClass">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品编码" prop="productCode">
        <el-input
          v-model="queryParams.productCode"
          placeholder="请输入商品编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择商品分类" clearable>
          <el-option
            v-for="category in categoryOptions"
            :key="category.categoryId"
            :label="category.categoryName"
            :value="category.categoryId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商品状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="商品状态" clearable>
          <el-option label="上架" :value="true" />
          <el-option label="下架" :value="false" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['product:v2:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-dropdown @command="handleEditCommand" trigger="click">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            v-hasPermi="['product:v2:edit']"
          >
            修改 <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="{type: 'edit', row: null}">
              <i class="el-icon-edit"></i> 原版编辑
            </el-dropdown-item>
            <el-dropdown-item :command="{type: 'edit-integrated', row: null}">
              <i class="el-icon-magic-stick"></i> 整合版编辑
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['product:v2:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-upload2"
          size="mini"
          :disabled="multiple"
          @click="handleStatusUpdate(true)"
          v-hasPermi="['product:v2:edit']"
        >批量上架</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-download"
          size="mini"
          :disabled="multiple"
          @click="handleStatusUpdate(false)"
          v-hasPermi="['product:v2:edit']"
        >批量下架</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 商品列表 -->
    <el-table v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="商品ID" align="center" prop="productId" width="80" />
      <el-table-column label="商品图片" align="center" width="100">
        <template slot-scope="scope">
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.mainImage"
            :preview-src-list="[scope.row.mainImage]"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column label="商品名称" align="center" prop="productName" :show-overflow-tooltip="true" />
      <el-table-column label="商品编码" align="center" prop="productCode" width="120" />
      <el-table-column label="分类" align="center" prop="categoryName" width="100" />
      <el-table-column label="库存" align="center" prop="stock" width="80" />
      <el-table-column label="销量" align="center" prop="sales" width="80" />
      <el-table-column label="状态" align="center" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-text="上架"
            inactive-text="下架"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['product:v2:query']"
          >详情</el-button>
          <el-dropdown @command="handleEditCommand" trigger="click">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              v-hasPermi="['product:v2:edit']"
            >
              修改 <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="{type: 'edit', row: scope.row}">
                <i class="el-icon-edit"></i> 原版编辑
              </el-dropdown-item>
              <el-dropdown-item :command="{type: 'edit-integrated', row: scope.row}">
                <i class="el-icon-magic-stick"></i> 整合版编辑
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-copy-document"
            @click="handleCopy(scope.row)"
            v-hasPermi="['product:v2:add']"
          >复制</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['product:v2:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 商品详情对话框 -->
    <product-detail-dialog
      :visible.sync="detailVisible"
      :product-id="currentProductId"
    />

    <!-- 商品复制对话框 -->
    <product-copy-dialog
      :visible.sync="copyVisible"
      :product="currentProduct"
      @success="getList"
    />
  </div>
</template>

<script>
import { listProductV2, delProductV2, updateProductV2Status, getProductV2Statistics } from "@/api/product/v2";
import { listCategory } from "@/api/product/category";
import { parseTime } from "@/utils/ruoyi";
import ProductDetailDialog from "./components/ProductDetailDialog";
import ProductCopyDialog from "./components/ProductCopyDialog";

export default {
  name: "ProductV2",
  components: {
    ProductDetailDialog,
    ProductCopyDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品表格数据
      productList: [],
      // 分类选项
      categoryOptions: [],
      // 统计数据
      statistics: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: null,
        productCode: null,
        categoryId: null,
        status: null,

        includePriceInfo: false,
        includeSkuInfo: false
      },
      // 详情对话框
      detailVisible: false,
      currentProductId: null,
      // 复制对话框
      copyVisible: false,
      currentProduct: null
    };
  },
  created() {
    this.getList();
    this.getCategoryList();
    this.getStatistics();
  },
  methods: {
    parseTime,
    /** 查询商品列表 */
    getList() {
      this.loading = true;
      listProductV2(this.queryParams).then(response => {
        // 处理TableDataInfo返回格式
        this.productList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 查询分类列表 */
    getCategoryList() {
      listCategory().then(response => {
        this.categoryOptions = response.rows || [];
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      getProductV2Statistics().then(response => {
        const data = response.data;
        this.statistics = [
          {
            key: 'total',
            label: '总商品数',
            value: data.totalProducts || 0,
            icon: 'el-icon-goods',
            iconClass: 'stat-icon-primary'
          },
          {
            key: 'online',
            label: '上架商品',
            value: data.onlineProducts || 0,
            icon: 'el-icon-upload2',
            iconClass: 'stat-icon-success'
          },
          {
            key: 'offline',
            label: '下架商品',
            value: data.offlineProducts || 0,
            icon: 'el-icon-download',
            iconClass: 'stat-icon-warning'
          },

          {
            key: 'skus',
            label: '总SKU数',
            value: data.totalSkus || 0,
            icon: 'el-icon-menu',
            iconClass: 'stat-icon-purple'
          }
        ];
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.productId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({ path: '/product/v2/add' });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const productId = row.productId || this.ids[0];
      this.$router.push({ path: '/product/v2/edit/' + productId });
    },
    /** 编辑命令处理 */
    handleEditCommand(command) {
      const productId = command.row ? command.row.productId : this.ids[0];

      if (command.type === 'edit') {
        // 跳转到原版编辑页面
        this.$router.push({ path: '/product/v2/edit/' + productId });
      } else if (command.type === 'edit-integrated') {
        // 跳转到整合版编辑页面
        this.$router.push({ path: '/product/v2/edit-integrated/' + productId });
      }
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.currentProductId = row.productId;
      this.detailVisible = true;
    },
    /** 复制按钮操作 */
    handleCopy(row) {
      this.currentProduct = row;
      this.copyVisible = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const productIds = row.productId || this.ids;
      this.$modal.confirm('是否确认删除商品编号为"' + productIds + '"的数据项？').then(function() {
        return delProductV2(productIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status ? "上架" : "下架";
      this.$modal.confirm('确认要"' + text + '""' + row.productName + '"商品吗？').then(function() {
        return updateProductV2Status({
          productIds: [row.productId],
          status: row.status
        });
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
        this.getStatistics();
      }).catch(function() {
        row.status = !row.status;
      });
    },
    /** 批量状态更新 */
    handleStatusUpdate(status) {
      const productIds = this.ids;
      const text = status ? "上架" : "下架";
      this.$modal.confirm('确认要"' + text + '"选中的商品吗？').then(function() {
        return updateProductV2Status({
          productIds: productIds,
          status: status
        });
      }).then(() => {
        this.getList();
        this.getStatistics();
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {});
    }
  }
};
</script>

<style lang="scss" scoped>
.statistics-row {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: #fff;

  &.stat-icon-primary { background: #409eff; }
  &.stat-icon-success { background: #67c23a; }
  &.stat-icon-warning { background: #e6a23c; }
  &.stat-icon-danger { background: #f56c6c; }
  &.stat-icon-info { background: #909399; }
  &.stat-icon-purple { background: #722ed1; }
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}
</style>
