<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>价格计算器</span>
      </div>
      
      <el-form :model="calcForm" ref="calcForm" :rules="calcRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="选择客户" prop="partnerId">
              <el-select v-model="calcForm.partnerId" placeholder="请选择客户" @change="handlePartnerChange">
                <el-option
                  v-for="partner in partnerList"
                  :key="partner.partnerId"
                  :label="partner.partnerName"
                  :value="partner.partnerId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户等级">
              <el-input v-model="customerLevel.levelName" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="商品列表">
          <el-table :data="calcForm.items" style="width: 100%">
            <el-table-column label="商品名称" width="200">
              <template slot-scope="scope">
                <el-select v-model="scope.row.productId" placeholder="选择商品" @change="handleProductChange(scope.$index)">
                  <el-option
                    v-for="product in productList"
                    :key="product.productId"
                    :label="product.productName"
                    :value="product.productId"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="SKU" width="150">
              <template slot-scope="scope">
                <el-select v-model="scope.row.skuId" placeholder="选择SKU">
                  <el-option
                    v-for="sku in scope.row.skuList || []"
                    :key="sku.skuId"
                    :label="sku.skuCode"
                    :value="sku.skuId"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="数量" width="120">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.quantity" :min="1" :precision="0" />
              </template>
            </el-table-column>
            <el-table-column label="基础价格" width="120">
              <template slot-scope="scope">
                <span>¥{{ scope.row.basePrice || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算价格" width="120">
              <template slot-scope="scope">
                <span>¥{{ scope.row.calculatedPrice || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="小计" width="120">
              <template slot-scope="scope">
                <span>¥{{ scope.row.subtotal || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-button size="mini" type="danger" @click="removeItem(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <el-button type="primary" size="mini" @click="addItem" style="margin-top: 10px;">添加商品</el-button>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="calculatePrice">计算价格</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="box-card" style="margin-top: 20px;" v-if="priceResult">
      <div slot="header" class="clearfix">
        <span>价格计算结果</span>
      </div>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="客户名称">{{ priceResult.partnerName }}</el-descriptions-item>
        <el-descriptions-item label="客户等级">{{ priceResult.levelName }}</el-descriptions-item>
        <el-descriptions-item label="商品总数">{{ priceResult.totalItems }}</el-descriptions-item>
        <el-descriptions-item label="商品总量">{{ priceResult.totalQuantity }}</el-descriptions-item>
        <el-descriptions-item label="基础总价">¥{{ priceResult.baseTotal }}</el-descriptions-item>
        <el-descriptions-item label="折扣金额">¥{{ priceResult.discountAmount }}</el-descriptions-item>
        <el-descriptions-item label="最终总价" :span="2">
          <el-tag size="large" type="success">¥{{ priceResult.finalTotal }}</el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <el-table :data="priceResult.items" style="width: 100%; margin-top: 20px;">
        <el-table-column label="商品名称" prop="productName" />
        <el-table-column label="SKU编码" prop="skuCode" />
        <el-table-column label="数量" prop="quantity" />
        <el-table-column label="基础价格" prop="basePrice">
          <template slot-scope="scope">
            ¥{{ scope.row.basePrice }}
          </template>
        </el-table-column>
        <el-table-column label="等级价格" prop="levelPrice">
          <template slot-scope="scope">
            ¥{{ scope.row.levelPrice }}
          </template>
        </el-table-column>
        <el-table-column label="阶梯价格" prop="tierPrice">
          <template slot-scope="scope">
            ¥{{ scope.row.tierPrice || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="最终价格" prop="finalPrice">
          <template slot-scope="scope">
            <el-tag type="success">¥{{ scope.row.finalPrice }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="小计" prop="subtotal">
          <template slot-scope="scope">
            <el-tag type="warning">¥{{ scope.row.subtotal }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { calculatePrice, batchCalculatePrice } from "@/api/pricing/price";
import { listPartnerInfo } from "@/api/company/partner";
import { listProductInfo } from "@/api/product/info";
import { listProductSku } from "@/api/product/sku";

export default {
  name: "PriceCalculator",
  data() {
    return {
      // 计算表单
      calcForm: {
        partnerId: null,
        items: [
          {
            productId: null,
            skuId: null,
            quantity: 1,
            basePrice: 0,
            calculatedPrice: 0,
            subtotal: 0,
            skuList: []
          }
        ]
      },
      // 客户等级信息
      customerLevel: {},
      // 价格计算结果
      priceResult: null,
      // 客户列表
      partnerList: [],
      // 商品列表
      productList: [],
      // 表单校验
      calcRules: {
        partnerId: [
          { required: true, message: "请选择客户", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getPartnerList();
    this.getProductList();
  },
  methods: {
    /** 获取客户列表 */
    getPartnerList() {
      listPartnerInfo().then(response => {
        this.partnerList = response.rows;
      });
    },
    /** 获取商品列表 */
    getProductList() {
      listProductInfo().then(response => {
        this.productList = response.rows;
      });
    },
    /** 客户变化处理 */
    handlePartnerChange(partnerId) {
      const partner = this.partnerList.find(p => p.partnerId === partnerId);
      if (partner) {
        this.customerLevel = {
          levelName: partner.levelName || '普通客户',
          discountRate: partner.discountRate || 1.0
        };
      }
    },
    /** 商品变化处理 */
    handleProductChange(index) {
      const item = this.calcForm.items[index];
      if (item.productId) {
        // 获取商品的SKU列表
        listProductSku({ productId: item.productId }).then(response => {
          this.$set(this.calcForm.items[index], 'skuList', response.rows);
        });
        
        // 获取商品基础价格
        const product = this.productList.find(p => p.productId === item.productId);
        if (product) {
          this.$set(this.calcForm.items[index], 'basePrice', product.basePrice || 0);
        }
      } else {
        this.$set(this.calcForm.items[index], 'skuList', []);
        this.$set(this.calcForm.items[index], 'basePrice', 0);
      }
      this.$set(this.calcForm.items[index], 'skuId', null);
    },
    /** 添加商品项 */
    addItem() {
      this.calcForm.items.push({
        productId: null,
        skuId: null,
        quantity: 1,
        basePrice: 0,
        calculatedPrice: 0,
        subtotal: 0,
        skuList: []
      });
    },
    /** 删除商品项 */
    removeItem(index) {
      if (this.calcForm.items.length > 1) {
        this.calcForm.items.splice(index, 1);
      }
    },
    /** 计算价格 */
    calculatePrice() {
      this.$refs["calcForm"].validate(valid => {
        if (valid) {
          // 验证商品项
          const validItems = this.calcForm.items.filter(item => 
            item.productId && item.skuId && item.quantity > 0
          );
          
          if (validItems.length === 0) {
            this.$modal.msgWarning("请至少添加一个有效的商品项");
            return;
          }
          
          // 构建计算请求
          const calcRequest = {
            partnerId: this.calcForm.partnerId,
            items: validItems.map(item => ({
              productId: item.productId,
              skuId: item.skuId,
              quantity: item.quantity
            }))
          };
          
          // 调用批量价格计算API
          batchCalculatePrice(calcRequest).then(response => {
            this.priceResult = response.data;
            this.$modal.msgSuccess("价格计算完成");
          });
        }
      });
    },
    /** 重置表单 */
    resetForm() {
      this.calcForm = {
        partnerId: null,
        items: [
          {
            productId: null,
            skuId: null,
            quantity: 1,
            basePrice: 0,
            calculatedPrice: 0,
            subtotal: 0,
            skuList: []
          }
        ]
      };
      this.customerLevel = {};
      this.priceResult = null;
      this.$refs["calcForm"].resetFields();
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
</style>
