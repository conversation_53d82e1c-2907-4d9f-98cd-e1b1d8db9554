package com.mn.controller;

import com.mn.entity.ProductSku;
import com.mn.form.ProductSkuForm;
import com.mn.model.TableDataInfo;
import com.mn.service.IProductSkuService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 商品SKU表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@RestController
@RequestMapping("/product-sku")
public class ProductSkuController extends BaseController {

    @Resource
    private IProductSkuService productSkuService;

    /**
     * 获取SKU列表
     */
    //@PreAuthorize("@ss.hasPermi('product:sku:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductSkuForm form) {
        startPage();
        form.setDeptId(getDeptId());
        List<ProductSku> list = productSkuService.selectSkuList(form);
        return getDataTable(list);
    }

    /**
     * 根据商品ID获取SKU列表
     */
    //@PreAuthorize("@ss.hasPermi('product:sku:list')")
    @GetMapping("/listByProductId/{productId}")
    public AjaxResult listByProductId(@PathVariable Long productId) {
        List<ProductSku> list = productSkuService.selectSkuByProductId(productId);
        return success(list);
    }

    /**
     * 根据SKU ID获取详细信息
     */
    //@PreAuthorize("@ss.hasPermi('product:sku:query')")
    @GetMapping(value = "/{skuId}")
    public AjaxResult getInfo(@PathVariable Long skuId) {
        return success(productSkuService.getById(skuId));
    }

    /**
     * 新增SKU
     */
    //@PreAuthorize("@ss.hasPermi('product:sku:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ProductSku sku) {
        sku.setCreateTime(DateUtils.getNowDate());
        return toAjax(productSkuService.insertSku(sku));
    }

    /**
     * 修改SKU
     */
    //@PreAuthorize("@ss.hasPermi('product:sku:edit')")
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody ProductSku sku) {
        sku.setUpdateTime(DateUtils.getNowDate());
        return toAjax(productSkuService.updateSku(sku));
    }

    /**
     * 删除SKU
     */
    //@PreAuthorize("@ss.hasPermi('product:sku:remove')")
    @PostMapping("/del")
    public AjaxResult remove(Long skuId) {
        return toAjax(productSkuService.deleteBySkuId(skuId));
    }

    /**
     * 批量新增SKU
     */
    //@PreAuthorize("@ss.hasPermi('product:sku:add')")
    @PostMapping("/batchAdd")
    public AjaxResult batchAdd(@Validated @RequestBody List<ProductSku> skuList) {
        for (ProductSku sku : skuList) {
            sku.setCreateTime(DateUtils.getNowDate());
        }
        return toAjax(productSkuService.batchInsertSku(skuList));
    }

    /**
     * 批量修改SKU
     */
    //@PreAuthorize("@ss.hasPermi('product:sku:edit')")
    @PostMapping("/batchEdit")
    public AjaxResult batchEdit(@Validated @RequestBody List<ProductSku> skuList) {
        for (ProductSku sku : skuList) {
            sku.setUpdateTime(DateUtils.getNowDate());
        }
        return toAjax(productSkuService.batchUpdateSku(skuList));
    }
}
