<template>
	<div class="app-container">
		<el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
			label-width="68px">
			<el-form-item label="用户名称" prop="userName">
				<el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 240px"
					@keyup.enter.native="handleQuery" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
				<el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>

		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增员工</el-button>
			</el-col>
			<right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
		</el-row>

		<el-table v-loading="loading" :data="userList">
			<el-table-column label="用户名" align="center" prop="userName" />
			<el-table-column label="用户昵称" align="center" prop="nickName" />
			<el-table-column label="角色" align="center">
				<template slot-scope="scope">
					<el-tag v-for="role in scope.row.roles" :key="role.roleId" size="mini" style="margin-right: 5px;">
						{{ role.roleName }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="状态" align="center">
				<template slot-scope="scope">
					<el-switch
						v-model="scope.row.enableFlag"
						:active-value="1"
						:inactive-value="0"
						@change="handleStatusChange(scope.row)">
					</el-switch>
				</template>
			</el-table-column>
			<el-table-column label="创建时间" align="center" prop="createTime" width="160">
				<template slot-scope="scope">
					<span>{{ parseTime(scope.row.createTime) }}</span>
				</template>
			</el-table-column>
			<el-table-column label="操作" align="center" width="200">
				<template slot-scope="scope" v-if="scope.row.userId !== 1">
					<el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
					<el-button size="mini" type="text" icon="el-icon-setting" @click="handleMerchantPermission(scope.row)">商户权限</el-button>
					<el-button size="mini" type="text" icon="el-icon-key" @click="handleResetPwd(scope.row)">重置密码</el-button>
				</template>
			</el-table-column>
		</el-table>

		<pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum"
			:limit.sync="queryParams.pageSize" @pagination="getList" />

		<!-- 添加或修改员工对话框 -->
		<el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
			<el-form ref="form" :model="form" :rules="rules" label-width="80px">
				<el-row>
					<el-col :span="12">
						<el-form-item label="用户昵称" prop="nickName">
							<el-input v-model="form.nickName" placeholder="请输入用户昵称" maxlength="30" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item v-if="form.userId == undefined" label="用户名" prop="userName">
							<el-input v-model="form.userName" placeholder="请输入用户名" maxlength="30" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
							<el-input v-model="form.password" placeholder="请输入用户密码" type="password" maxlength="20"
								show-password />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="角色">
							<el-select v-model="form.roleIds" multiple placeholder="请选择角色">
								<el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName"
									:value="item.roleId"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submitForm">确 定</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>

		<!-- 商户权限管理对话框 -->
		<el-dialog title="商户权限管理" :visible.sync="merchantPermissionDialog.open" width="600px" append-to-body>
			<el-form ref="merchantPermissionForm" :model="merchantPermissionDialog.form" label-width="120px">
				<el-form-item label="用户名称">
					<span>{{ merchantPermissionDialog.form.userName }}</span>
				</el-form-item>
				<el-form-item label="权限类型" prop="permissionType">
					<el-select v-model="merchantPermissionDialog.form.permissionType" placeholder="请选择权限类型">
						<el-option label="只读" value="read"></el-option>
						<el-option label="读写" value="write"></el-option>
						<el-option label="管理" value="admin"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="可管理商户">
					<el-transfer
						v-model="merchantPermissionDialog.form.merchantIds"
						:data="merchantOptions"
						:titles="['可选商户', '已授权商户']"
						:button-texts="['移除', '添加']"
						:format="{
							noChecked: '${total}',
							hasChecked: '${checked}/${total}'
						}"
						filterable>
					</el-transfer>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submitMerchantPermission">确 定</el-button>
				<el-button @click="merchantPermissionDialog.open = false">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
	import {
		listDeptUser,
		addCompanyUser,
		updateCompanyUser,
		getCompanyUser,
		resetUserPwd,
		changeUserStatus,
		getDeptRoles,
		getUserMerchantPermissions,
		setUserMerchantPermissions
	} from "@/api/system/user";

	export default {
		name: "DeptUser",
		data() {
			return {
				// 遮罩层
				loading: true,
				// 显示搜索条件
				showSearch: true,
				// 总条数
				total: 0,
				// 用户表格数据
				userList: [],
				// 弹出层标题
				title: "",
				// 是否显示弹出层
				open: false,
				// 角色选项
				roleOptions: [],
				// 表单参数
				form: {},
				// 商户权限管理对话框
				merchantPermissionDialog: {
					open: false,
					form: {}
				},
				// 商户选项
				merchantOptions: [],
				// 查询参数
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					userName: undefined
				},
				// 表单校验
				rules: {
					userName: [{
						required: true,
						message: "用户名称不能为空",
						trigger: "blur"
					}],
					nickName: [{
						required: true,
						message: "用户昵称不能为空",
						trigger: "blur"
					}],
					password: [{
						required: true,
						message: "用户密码不能为空",
						trigger: "blur"
					}]
				}
			};
		},
		created() {
			this.getList();
			this.getRoleOptions();
		},
		methods: {
			/** 查询用户列表 */
			getList() {
				this.loading = true;
				listDeptUser(this.queryParams).then(response => {
					this.userList = response.rows;
					this.total = response.total;
					this.loading = false;
				});
			},
			/** 获取角色选项 */
			getRoleOptions() {
				getDeptRoles().then(response => {
					this.roleOptions = response.data;
				});
			},
			// 用户状态修改
			handleStatusChange(row) {
				let text = row.enableFlag === 0 ? "启用" : "停用";
				this.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗？').then(function() {
					return changeUserStatus(row.userId, row.enableFlag);
				}).then(() => {
					this.$modal.msgSuccess(text + "成功");
				}).catch(function() {
					row.enableFlag = row.enableFlag === 0 ? 1 : 0;
				});
			},
			// 取消按钮
			cancel() {
				this.open = false;
				this.reset();
			},
			// 表单重置
			reset() {
				this.form = {
					userId: undefined,
					userName: undefined,
					nickName: undefined,
					password: undefined,
					roleIds: []
				};
				this.resetForm("form");
			},
			/** 搜索按钮操作 */
			handleQuery() {
				this.queryParams.pageNum = 1;
				this.getList();
			},
			/** 重置按钮操作 */
			resetQuery() {
				this.resetForm("queryForm");
				this.handleQuery();
			},
			/** 新增按钮操作 */
			handleAdd() {
				this.reset();
				this.open = true;
				this.title = "添加员工";
			},
			/** 修改按钮操作 */
			handleUpdate(row) {
				this.reset();
				const userId = row.userId;
				getCompanyUser(userId).then(response => {
					this.form = response.data;
					this.$set(this.form, "roleIds", response.roleIds);
					this.open = true;
					this.title = "修改员工";
					this.form.password = "";
				});
			},
			/** 重置密码按钮操作 */
			handleResetPwd(row) {
				this.$prompt('请输入"' + row.userName + '"的新密码', "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					closeOnClickModal: false,
					inputPattern: /^.{5,20}$/,
					inputErrorMessage: "用户密码长度必须介于 5 和 20 之间"
				}).then(({
					value
				}) => {
					resetUserPwd(row.userId, value).then(response => {
						this.$modal.msgSuccess("修改成功，新密码是：" + value);
					});
				}).catch(() => {});
			},
			/** 提交按钮 */
			submitForm: function() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						if (this.form.userId != undefined) {
							updateCompanyUser(this.form).then(response => {
								this.$modal.msgSuccess("修改成功");
								this.open = false;
								this.getList();
							});
						} else {
							addCompanyUser(this.form).then(response => {
								this.$modal.msgSuccess("新增成功");
								this.open = false;
								this.getList();
							});
						}
					}
				});
			},
			// 商户权限管理
			handleMerchantPermission(row) {
				this.merchantPermissionDialog.form = {
					userId: row.userId,
					userName: row.userName,
					nickName: row.nickName,
					permissionType: 'read',
					merchantIds: []
				};
				// 获取商户列表
				this.getMerchantOptions();
				// 获取用户当前的商户权限
				this.getUserMerchantPermissions(row.userId);
				this.merchantPermissionDialog.open = true;
			},
			// 获取商户选项
			getMerchantOptions() {
				// 这里应该调用API获取商户列表
				// 暂时使用模拟数据
				this.merchantOptions = [
					{ key: 1, label: '测试商户1' },
					{ key: 2, label: '测试商户2' },
					{ key: 3, label: '测试商户3' }
				];
			},
			// 获取用户商户权限
			getUserMerchantPermissions(userId) {
				getUserMerchantPermissions(userId).then(response => {
					this.merchantPermissionDialog.form.merchantIds = response.data || [];
				});
			},
			// 提交商户权限设置
			submitMerchantPermission() {
				this.$refs["merchantPermissionForm"].validate(valid => {
					if (valid) {
						setUserMerchantPermissions(this.merchantPermissionDialog.form).then(response => {
							this.$modal.msgSuccess("设置成功");
							this.merchantPermissionDialog.open = false;
							this.getList();
						});
					}
				});
			}
		}
	};
</script>
