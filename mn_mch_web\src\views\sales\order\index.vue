<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="mb-20">
      <div slot="header" class="card-header">
        <span><i class="el-icon-search header-icon"></i>搜索条件</span>
        <div class="header-buttons">
          <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button type="text" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        </div>
      </div>
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px" size="small">
        <el-form-item label="订单编号" prop="orderNo">
          <el-input v-model="queryParams.orderNo" placeholder="请输入订单编号" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="客户名称" prop="partnerName">
          <el-input v-model="queryParams.partnerName" placeholder="请输入客户名称" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="订单日期">
          <el-date-picker v-model="dateRange" size="small" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="订单状态" prop="orderStatus">
          <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable style="width: 200px">
            <el-option label="未审核" :value="0" />
            <el-option label="已审核" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="付款状态" prop="paymentStatus">
          <el-select v-model="queryParams.paymentStatus" placeholder="请选择付款状态" clearable style="width: 200px">
            <el-option label="未付款" :value="0" />
            <el-option label="已付款" :value="1" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区 -->
    <el-card class="mb-20">
      <div slot="header" class="card-header">
        <span><i class="el-icon-s-order header-icon"></i>销售订单列表</span>
        <div class="header-buttons">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增订单</el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        ref="table"
        v-loading="loading"
        :data="orderList"
        border
        stripe
        highlight-current-row
        :row-class-name="tableRowClassName"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="订单编号" align="center" prop="orderNo" min-width="150">
          <template slot-scope="scope">
            <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.orderNo }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="客户名称" align="center" prop="partnerName" min-width="150" />
        <el-table-column label="订单日期" align="center" prop="orderDate" width="100">
          <template slot-scope="scope">
            {{ parseTime(scope.row.orderDate, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>
        <el-table-column label="订单金额" align="center" prop="totalAmount" min-width="120">
          <template slot-scope="scope">
            <span class="amount-info total">{{ formatAmount(scope.row.totalAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="应付金额" align="center" prop="actualAmount" min-width="120">
          <template slot-scope="scope">
            <span class="amount-info actual">{{ formatAmount(scope.row.actualAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已付金额" align="center" prop="paymentAmount" min-width="120">
          <template slot-scope="scope">
            <span class="amount-info payment">{{ formatAmount(scope.row.paymentAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" prop="orderStatus" width="100">
          <template slot-scope="scope">
            <span class="order-status-tag" :class="scope.row.orderStatus === 1 ? 'approved' : 'pending'">
              {{ scope.row.orderStatus === 1 ? '已审核' : '未审核' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="付款状态" align="center" prop="paymentStatus" width="100">
          <template slot-scope="scope">
            <span class="payment-status-tag" :class="scope.row.paymentStatus === 1 ? 'paid' : 'unpaid'">
              {{ scope.row.paymentStatus === 1 ? '已付款' : '未付款' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="160">
          <template slot-scope="scope">
            <div class="time-info">
              <div>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</div>
              <div class="time-detail">{{ parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="320" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" icon="el-icon-view" @click="handleDetail(scope.row)">
              详情
            </el-button>

            <!-- 未审核状态的操作按钮 -->
            <template v-if="scope.row.orderStatus === 0">
              <el-button size="mini" type="warning" icon="el-icon-edit" @click="handleUpdate(scope.row)">
                编辑
              </el-button>
              <el-button size="mini" type="success" icon="el-icon-check" @click="handleApproveOrder(scope.row)">
                审核
              </el-button>
              <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)">
                <el-button size="mini" type="info">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="delete">
                    <i class="el-icon-delete"></i> 删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>

            <!-- 已审核状态的操作按钮 -->
            <template v-else>
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-close"
                @click="handleCancelApproval(scope.row)"
                v-if="scope.row.paymentStatus === 0">
                取消审核
              </el-button>
              <el-button
                size="mini"
                type="warning"
                icon="el-icon-money"
                @click="handleGeneratePaymentForOrder(scope.row)"
                v-if="scope.row.paymentStatus === 0">
                收款单
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改销售订单对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1000px"
      append-to-body
      fullscreen
      class="fullscreen-dialog"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="small">
        <!-- 基本信息卡片 -->
        <el-card class="box-card mb-20" shadow="hover">
          <div slot="header" class="card-header">
            <span><i class="el-icon-document header-icon"></i>订单基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="订单编号" prop="orderNo">
                <el-input
                  v-model="form.orderNo"
                  placeholder="请输入或自动生成订单编号"
                  :disabled="form.operationType === 'update'"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-refresh"
                    @click="generateOrderNo"
                    v-if="form.operationType !== 'update'"
                  >生成</el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单日期" prop="orderDate">
                <el-date-picker
                  v-model="form.orderDate"
                  type="date"
                  placeholder="选择日期"
                  style="width: 100%"
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="客户" prop="partnerId">
                <el-input
                  v-model="selectedPartnerName"
                  placeholder="请选择客户"
                  readonly
                  style="width: 100%"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-search"
                    @click="openPartnerSelector"
                  >选择客户</el-button>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 销售订单明细管理卡片 -->
        <el-card class="box-card mb-20" shadow="hover">
          <div slot="header" class="card-header">
            <span><i class="el-icon-shopping-cart-full header-icon"></i>订单明细</span>
            <div class="header-buttons">
              <el-button
                type="warning"
                plain
                icon="el-icon-refresh"
                size="mini"
                @click="recalculateAllPrices"
                :disabled="!form.partnerId || orderItemList.length === 0">
                重新计算价格
              </el-button>
              <el-button type="success" plain icon="el-icon-shopping-cart-2" size="mini" @click="openProductSelector">
                选择商品
              </el-button>
            </div>
          </div>

          <!-- 订单明细列表 -->
          <el-table
            v-loading="itemsLoading"
            :data="orderItemList"
            border
            size="small"
            stripe
            highlight-current-row
            class="mt-10"
            style="width: 100%"
          >
            <el-table-column label="商品名称" align="center" prop="productName" min-width="150">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.productName"
                  placeholder="请输入商品名称"
                  size="mini"
                  :disabled="true"
                />
              </template>
            </el-table-column>

            <el-table-column label="商品编码" align="center" prop="productCode" min-width="120">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.productCode"
                  placeholder="请输入商品编码"
                  size="mini"
                  :disabled="true"
                />
              </template>
            </el-table-column>

            <el-table-column label="SKU编码" align="center" prop="skuCode" min-width="120">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.skuId"
                  filterable
                  placeholder="请选择SKU"
                  size="mini"
                  @change="(val) => handleSkuChange(val, scope.row)"
                >
                  <el-option
                    v-for="item in scope.row.skuOptions || []"
                    :key="item.skuId"
                    :label="item.skuCode"
                    :value="item.skuId"
                  >
                    <span style="float: left">{{ item?.skuCode }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">
                      {{ formatSpecData(item.specData) }}
                    </span>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="规格" align="center" prop="specData" min-width="150">
              <template slot-scope="scope">
                <span>{{ formatSpecData(scope.row.specData) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="单位" align="center" width="80">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.unit"
                  placeholder="单位"
                  size="mini"
                />
              </template>
            </el-table-column>

            <el-table-column label="数量" align="center" width="100">
              <template slot-scope="scope">
                <el-input-number
                  v-model="scope.row.quantity"
                  :precision="2"
                  :step="1"
                  :min="0.01"
                  controls-position="right"
                  size="mini"
                  @change="(val) => handleQuantityChange(scope.row, val)"
                />
              </template>
            </el-table-column>

            <el-table-column label="单价(元)" align="center" width="120">
              <template slot-scope="scope">
                <div class="price-container">
                  <el-input-number
                    v-model="scope.row.price"
                    :precision="2"
                    :step="0.01"
                    :min="0"
                    controls-position="right"
                    size="mini"
                    @change="(val) => handlePriceChange(scope.row, val)"
                    :disabled="scope.row.priceCalculating"
                  />
                  <div v-if="scope.row.priceType && scope.row.priceType !== 'BASE_PRICE'"
                       class="price-type-tag">
                    {{ getPriceTypeText(scope.row.priceType) }}
                  </div>
                  <div v-if="scope.row.priceCalculating" class="calculating-overlay">
                    <i class="el-icon-loading"></i>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="金额(元)" align="center" width="100">
              <template slot-scope="scope">
                <span>{{ formatAmount(scope.row.amount) }}</span>
              </template>
            </el-table-column>



            <el-table-column label="操作" align="center" width="100">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  icon="el-icon-refresh"
                  circle
                  size="mini"
                  @click="calculateItemPrice(scope.row)"
                  title="重新计算价格"
                  :loading="scope.row.priceCalculating"
                ></el-button>
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  size="mini"
                  @click="removeOrderItem(scope.$index)"
                  title="删除"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 合计信息 -->
          <div class="total-info mt-10" v-if="orderItemList.length > 0">
            <el-divider content-position="left">合计信息</el-divider>
            <div class="total-row">
              <span class="label">总数量:</span>
              <span class="value">{{ totalQuantity }}</span>
              <span class="label ml-20">总金额:</span>
              <span class="value amount">{{ formatAmount(totalAmount) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 金额信息和备注卡片 -->
        <el-card class="box-card mb-20" shadow="hover">
          <div slot="header" class="card-header">
            <span><i class="el-icon-money header-icon"></i>收款信息与备注</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="订单金额" prop="totalAmount">
                <el-input-number
                  v-model="form.totalAmount"
                  :precision="2"
                  :step="100"
                  :min="0"
                  style="width: 100%"
                  controls-position="right"
                  :disabled="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="优惠金额" prop="discountAmount">
                <el-input-number
                  v-model="form.discountAmount"
                  :precision="2"
                  :step="10"
                  :min="0"
                  style="width: 100%"
                  @change="calculateActualAmount"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="应付金额" prop="actualAmount">
                <el-input-number
                  v-model="form.actualAmount"
                  :precision="2"
                  :step="100"
                  :min="0"
                  style="width: 100%"
                  :disabled="true"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入备注信息"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">保 存</el-button>
      </div>
    </el-dialog>

    <!-- 商品选择器 -->
    <product-selector
      v-if="productSelectorVisible"
      :visible.sync="productSelectorVisible"
      @confirm="handleProductsSelected"
      :z-index="3100"
      append-to-body
      destroy-on-close
    />

    <!-- 客户选择器 -->
    <partner-selector
      v-if="partnerSelectorVisible"
      :visible.sync="partnerSelectorVisible"
      @confirm="handlePartnerSelected"
      :z-index="3200"
      append-to-body
      destroy-on-close
    />

    <!-- 销售订单详情对话框 -->
    <el-dialog
      title="销售订单详情"
      :visible.sync="detailOpen"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <!-- 基本信息卡片 -->
      <el-card class="box-card mb-20" shadow="hover">
        <div slot="header" class="card-header">
          <span><i class="el-icon-document header-icon"></i>订单基本信息</span>
        </div>

        <div class="order-detail-header">
          <div class="order-basic-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">订单编号：</span>
                  <span class="value">{{ detailForm?.orderNo }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">订单日期：</span>
                  <span class="value">{{ formatDate(detailForm.orderDate) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">客户名称：</span>
                  <span class="value">{{ detailForm.partnerName || '暂无' }}</span>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="mt-10">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">订单状态：</span>
                  <span class="order-status-tag" :class="detailForm.orderStatus === 1 ? 'approved' : 'pending'">
                    {{ detailForm.orderStatus === 1 ? '已审核' : '未审核' }}
                  </span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">付款状态：</span>
                  <span class="payment-status-tag" :class="detailForm.paymentStatus === 1 ? 'paid' : 'unpaid'">
                    {{ detailForm.paymentStatus === 1 ? '已付款' : '未付款' }}
                  </span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">创建时间：</span>
                  <span class="value">{{ formatDateTime(detailForm.createTime) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-card>

      <!-- 金额信息卡片 -->
      <el-card class="box-card mb-20" shadow="hover">
        <div slot="header" class="card-header">
          <span><i class="el-icon-money header-icon"></i>收款信息</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="6">
            <div class="amount-box">
              <div class="amount-label">订单金额</div>
              <div class="amount-value total">{{ formatAmount(detailForm.totalAmount) }} 元</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="amount-box">
              <div class="amount-label">优惠金额</div>
              <div class="amount-value discount">{{ formatAmount(detailForm.discountAmount) }} 元</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="amount-box">
              <div class="amount-label">应付金额</div>
              <div class="amount-value actual">{{ formatAmount(detailForm.actualAmount) }} 元</div>
            </div>
          </el-col>
          <el-col :span="6" v-if="detailForm.paymentStatus === 1">
            <div class="amount-box">
              <div class="amount-label">已付金额</div>
              <div class="amount-value payment">{{ formatAmount(detailForm.paymentAmount) }} 元</div>
            </div>
          </el-col>
        </el-row>

        <!-- 备注信息 -->
        <el-row v-if="detailForm.remark" class="mt-20">
          <el-col :span="24">
            <div class="remark-box">
              <div class="remark-label"><i class="el-icon-notebook-2"></i> 备注信息</div>
              <div class="remark-content">{{ detailForm?.remark }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 订单明细卡片 -->
      <el-card class="box-card" shadow="hover">
        <div slot="header" class="card-header">
          <span><i class="el-icon-shopping-cart-full header-icon"></i>订单明细</span>
        </div>

        <!-- 订单明细列表 -->
        <el-table
          :data="detailItems"
          border
          stripe
          size="small"
          v-loading="detailItemsLoading"
        >
          <el-table-column label="商品名称" prop="productName" min-width="150" />
          <el-table-column label="商品编码" prop="productCode" min-width="120" />
          <el-table-column label="SKU编码" prop="skuCode" min-width="120" />
          <el-table-column label="规格" min-width="150">
            <template slot-scope="scope">
              {{ formatSpecData(scope.row.specData) }}
            </template>
          </el-table-column>
          <el-table-column label="单位" prop="unit" width="80" align="center" />
          <el-table-column label="数量" prop="quantity" width="80" align="center" />
          <el-table-column label="单价(元)" width="100" align="center">
            <template slot-scope="scope">
              {{ formatAmount(scope.row.price) }}
            </template>
          </el-table-column>
          <el-table-column label="金额(元)" width="100" align="center">
            <template slot-scope="scope">
              {{ formatAmount(scope.row.amount) }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 明细合计 -->
        <div class="total-info mt-10" v-if="detailItems.length > 0">
          <div class="total-row">
            <span class="label">总数量:</span>
            <span class="value">{{ calculateTotalQuantity(detailItems) }}</span>
            <span class="label ml-20">总金额:</span>
            <span class="value amount">{{ formatAmount(calculateTotalAmount(detailItems)) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 订单操作卡片 -->
      <el-card class="box-card mb-20" shadow="hover" v-if="detailForm.orderStatus === 0 || (detailForm.orderStatus === 1 && detailForm.paymentStatus === 0)">
        <div slot="header" class="card-header">
          <span><i class="el-icon-s-tools header-icon"></i>订单操作</span>
        </div>

        <div class="order-actions">
          <!-- 未审核状态的操作 -->
          <el-row v-if="detailForm.orderStatus === 0">
            <el-col :span="24">
              <div class="action-box">
                <div class="action-title">当前状态：<span class="status-text pending">未审核</span></div>
                <div class="action-desc">您可以对此订单进行编辑或审核操作</div>
                <div class="action-buttons">
                  <el-button type="warning" icon="el-icon-edit" @click="handleUpdate(detailForm)">编辑订单</el-button>
                  <el-button type="success" icon="el-icon-check" @click="handleApproveOrder(detailForm)">审核订单</el-button>
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 已审核但未付款状态的操作 -->
          <el-row v-else-if="detailForm.orderStatus === 1 && detailForm.paymentStatus === 0">
            <el-col :span="24">
              <div class="action-box">
                <div class="action-title">当前状态：<span class="status-text approved">已审核</span></div>
                <div class="action-desc">此订单已审核但尚未付款，您可以取消审核</div>
                <div class="action-buttons">
                  <el-button type="danger" icon="el-icon-close" @click="handleCancelApproval(detailForm)">取消审核</el-button>
                  <el-button type="warning" icon="el-icon-money" @click="handleGeneratePaymentForOrder(detailForm)">生成收款单</el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="detailOpen = false">关闭详情</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ProductSelector from "@/views/sales/orderItem/productSelector.vue";
import { formatAmount, formatSpecData } from "@/views/sales/orderItem/utils";
import orderMethods from "./methods";
import PartnerSelector from "./partnerSelector.vue";

export default {
  name: "SalesOrder",
  components: {
    ProductSelector,
    PartnerSelector
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 销售订单表格数据
      orderList: [],
      // 多选选中数据
      multipleSelection: [],
      // 客户选项
      partnerOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 日期范围
      dateRange: [],
      // 详情页订单明细
      detailItems: [],
      // 详情页订单明细加载状态
      detailItemsLoading: false,
      // 订单明细列表
      orderItemList: [],
      // 订单明细加载状态
      itemsLoading: false,
      // 商品选择器是否可见
      productSelectorVisible: false,
      // 客户选择器是否可见
      partnerSelectorVisible: false,
      // 选中的客户名称（用于显示）
      selectedPartnerName: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: undefined,
        partnerName: undefined,
        orderStatus: undefined,
        paymentStatus: undefined
      },
      // 表单参数
      form: {
        orderId: undefined,
        orderNo: undefined,
        deptId: undefined,
        partnerId: undefined,
        orderDate: undefined,
        totalAmount: 0,
        discountAmount: 0,
        actualAmount: 0,
        paymentStatus: 0,
        paymentAmount: 0,
        orderStatus: 0,
        remark: undefined,
        createTime: undefined,
        updateTime: undefined,
        createBy: undefined,
        updateBy: undefined,
        operationType: undefined // 操作类型：add-新增，update-修改
      },
      // 详情表单
      detailForm: {},
      // 表单校验规则
      rules: {
        orderNo: [
          { required: true, message: "请输入或生成订单编号", trigger: "blur" }
        ],
        orderDate: [
          { required: true, message: "请选择订单日期", trigger: "blur" }
        ],
        partnerId: [
          { required: true, message: "请选择客户", trigger: "change" }
        ],
        totalAmount: [
          { required: true, message: "请输入订单金额", trigger: "blur" }
        ],
        orderStatus: [
          { required: true, message: "请选择订单状态", trigger: "change" }
        ],
        paymentStatus: [
          { required: true, message: "请选择付款状态", trigger: "change" }
        ]
      }
    };
  },
  computed: {
    // 总数量
    totalQuantity() {
      return this.orderItemList.reduce((sum, item) => {
        return sum + (parseFloat(item.quantity) || 0);
      }, 0).toFixed(2);
    },
    // 总金额
    totalAmount() {
      return this.orderItemList.reduce((sum, item) => {
        return sum + (item.amount || 0);
      }, 0);
    }
  },
  created() {
    this.getList();
    this.getPartnerOptions();
  },
  methods: {
    ...orderMethods,

    // 格式化金额显示
    formatAmount,

    // 格式化规格数据
    formatSpecData,

    // 打开客户选择器
    openPartnerSelector() {
      this.partnerSelectorVisible = true;
    },

    // 获取价格类型显示文本
    getPriceTypeText(priceType) {
      const typeMap = {
        'TIER_PRICE': '阶梯价',
        'LEVEL_PRICE': '等级价',
        'BASE_PRICE': '基础价'
      };
      return typeMap[priceType] || '基础价';
    },

    // 处理客户选择确认
    async handlePartnerSelected(partner) {
      const oldPartnerId = this.form.partnerId;
      this.form.partnerId = partner.partnerId;
      this.selectedPartnerName = partner.partnerName;
      this.partnerSelectorVisible = false;

      console.log('选择的客户:', partner);

      // 如果客户发生变更且有订单明细，重新计算价格
      if (oldPartnerId !== partner.partnerId && this.orderItemList.length > 0) {
        this.$confirm('客户已变更，是否重新计算所有商品价格？', '提示', {
          confirmButtonText: '重新计算',
          cancelButtonText: '保持原价',
          type: 'warning'
        }).then(() => {
          this.recalculateAllPrices();
        }).catch(() => {
          // 用户选择保持原价，不做任何操作
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>