<template>
  <div class="base-price-manager">
    <el-card class="price-card">
      <div slot="header" class="card-header">
        <span><i class="el-icon-coin"></i> 基础价格设置</span>
        <div class="header-buttons">
          <el-button type="primary" size="small" @click="handleAdd" v-if="!hasSpec">
            <i class="el-icon-plus"></i> 添加价格
          </el-button>
          <el-button type="success" size="small" @click="syncFromSkus" v-if="hasSpec && skuList.length > 0">
            <i class="el-icon-refresh"></i> 同步SKU价格
          </el-button>
        </div>
      </div>

      <el-alert
        v-if="hasSpec"
        title="基础价格将基于SKU进行设置，每个SKU对应一个基础价格记录"
        type="info"
        :closable="false"
        show-icon
        class="mb-15"
      />

      <el-table :data="priceList" border stripe size="small" style="width: 100%">
        <el-table-column label="SKU信息" min-width="200">
          <template slot-scope="scope">
            <div v-if="hasSpec" class="sku-info">
              <div class="sku-code">{{ getSkuDisplay(scope.row.skuId) }}</div>
              <div class="sku-spec">{{ getSkuSpecDisplay(scope.row.skuId) }}</div>
            </div>
            <span v-else class="default-sku">默认SKU</span>
          </template>
        </el-table-column>
        <el-table-column label="基础价格(元)" width="140" align="center">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.basePriceYuan"
              :min="0"
              :precision="2"
              placeholder="基础价格"
              @change="handlePriceChange(scope.row, 'basePrice')"
              controls-position="right"
              size="mini"
              style="width: 100%"
            />
          </template>
        </el-table-column>

        <el-table-column label="生效日期" width="140" align="center">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.effectiveDate"
              type="date"
              placeholder="生效日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              size="mini"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="失效日期" width="140" align="center">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.expireDate"
              type="date"
              placeholder="失效日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              size="mini"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" align="center" v-if="!hasSpec">
          <template slot-scope="scope">
            <el-button
              type="danger"
              icon="el-icon-delete"
              circle
              size="mini"
              @click="handleDelete(scope.$index)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量设置 -->
      <div class="batch-setting" v-if="priceList.length > 1">
        <el-divider content-position="left">批量设置</el-divider>
        <el-form :inline="true" class="batch-form" size="mini">
          <el-form-item label="基础价格">
            <el-input-number
              v-model="batchSettings.basePrice"
              :min="0"
              :precision="2"
              controls-position="right"
              size="mini"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="mini" @click="applyBatchSettings">应用到所有</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 空状态 -->
      <div v-if="priceList.length === 0" class="empty-state">
        <i class="el-icon-coin"></i>
        <p v-if="hasSpec">请先在SKU信息页面添加SKU，然后点击"同步SKU价格"</p>
        <p v-else>暂无价格信息，请点击"添加价格"按钮添加</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "BasePriceManager",
  props: {
    value: {
      type: Array,
      default: () => []
    },
    skuList: {
      type: Array,
      default: () => []
    },
    hasSpec: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      priceList: [],
      // 批量设置
      batchSettings: {
        basePrice: 0
      }
    };
  },
  watch: {
    value: {
      handler(val) {
        this.priceList = val.map(item => ({
          ...item,
          basePriceYuan: item.basePrice ? (item.basePrice / 100) : 0,
          effectiveDate: this.formatDateFromTimestamp(item.effectiveDate),
          expireDate: this.formatDateFromTimestamp(item.expireDate)
        }));
      },
      immediate: true,
      deep: true
    },
    skuList: {
      handler() {
        this.initPriceList();
      },
      immediate: true
    },
    hasSpec: {
      handler() {
        this.initPriceList();
      },
      immediate: true
    }
  },
  methods: {
    /** 初始化价格列表 */
    initPriceList() {
      if (this.priceList.length === 0) {
        if (this.hasSpec && this.skuList.length > 0) {
          // 有规格时，为每个SKU创建价格
          this.skuList.forEach(sku => {
            this.addPriceForSku(sku.skuId);
          });
        } else {
          // 无规格时，创建默认价格
          this.addPriceForSku(null);
        }
      }
    },
    /** 为SKU添加价格 */
    addPriceForSku(skuId) {
      const exists = this.priceList.some(item => item.skuId === skuId);
      if (!exists) {
        this.priceList.push({
          skuId: skuId,
          basePrice: 0,
          basePriceYuan: 0,
          effectiveDate: this.getCurrentDate(),
          expireDate: null,
          status: true
        });
        this.emitChange();
      }
    },
    /** 添加价格 */
    handleAdd() {
      if (this.hasSpec && this.skuList.length > 0) {
        // 检查是否还有SKU没有设置价格
        const unsetSkus = this.skuList.filter(sku => 
          !this.priceList.some(price => price.skuId === sku.skuId)
        );
        
        if (unsetSkus.length > 0) {
          this.addPriceForSku(unsetSkus[0].skuId);
        } else {
          this.$message.warning('所有SKU都已设置价格');
        }
      } else {
        this.addPriceForSku(null);
      }
    },
    /** 删除价格 */
    handleDelete(index) {
      this.priceList.splice(index, 1);
      this.emitChange();
    },
    /** 价格变化处理 */
    handlePriceChange(row, type) {
      if (type === 'basePrice') {
        row.basePrice = Math.round(row.basePriceYuan * 100);
      }
      this.emitChange();
    },
    /** 同步SKU价格 */
    syncFromSkus() {
      if (!this.hasSpec || this.skuList.length === 0) {
        this.$message.warning('没有可同步的SKU');
        return;
      }

      // 清空现有价格列表
      this.priceList = [];

      // 为每个SKU创建价格记录
      this.skuList.forEach(sku => {
        this.priceList.push({
          skuId: sku.skuId,
          basePrice: 0,
          basePriceYuan: 0,
          effectiveDate: this.getCurrentDate(),
          expireDate: null,
          status: true
        });
      });

      this.emitChange();
      this.$message.success(`已同步 ${this.skuList.length} 个SKU的价格设置`);
    },

    /** 应用批量设置 */
    applyBatchSettings() {
      if (this.priceList.length === 0) {
        return;
      }

      this.priceList.forEach(price => {
        if (this.batchSettings.basePrice > 0) {
          price.basePriceYuan = this.batchSettings.basePrice;
          price.basePrice = Math.round(this.batchSettings.basePrice * 100);
        }
      });

      this.$message.success("批量设置已应用");
      this.emitChange();
    },

    /** 获取SKU显示名称 */
    getSkuDisplay(skuId) {
      const sku = this.skuList.find(item => item.skuId === skuId);
      return sku ? (sku.skuCode || `SKU-${skuId}`) : `SKU-${skuId}`;
    },

    /** 获取SKU规格显示 */
    getSkuSpecDisplay(skuId) {
      const sku = this.skuList.find(item => item.skuId === skuId);
      if (!sku || !sku.specData) return '';

      try {
        const specData = JSON.parse(sku.specData);
        if (Array.isArray(specData)) {
          return specData.map(spec => `${spec.specName}: ${spec.value}`).join(', ');
        }
        return '';
      } catch (e) {
        return '';
      }
    },
    /** 获取当前日期 */
    getCurrentDate() {
      const now = new Date();
      return now.getFullYear() + '-' +
             String(now.getMonth() + 1).padStart(2, '0') + '-' +
             String(now.getDate()).padStart(2, '0');
    },
    /** 格式化时间戳为日期字符串 */
    formatDateFromTimestamp(timestamp) {
      if (!timestamp) return null;

      // 如果已经是字符串格式，直接返回
      if (typeof timestamp === 'string') {
        return timestamp;
      }

      // 如果是时间戳，转换为日期字符串
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) return null;

      return date.getFullYear() + '-' +
             String(date.getMonth() + 1).padStart(2, '0') + '-' +
             String(date.getDate()).padStart(2, '0');
    },
    /** 发送变化事件 */
    emitChange() {
      const result = this.priceList.map(item => ({
        ...item,
        basePrice: Math.round((item.basePriceYuan || 0) * 100)
      }));
      this.$emit('input', result);
    }
  }
};
</script>

<style lang="scss" scoped>
.base-price-manager {
  .mb-15 {
    margin-bottom: 15px;
  }

  .price-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-weight: 600;
      color: #303133;
    }
  }

  .header-buttons {
    display: flex;
    gap: 8px;
  }

  .sku-info {
    .sku-code {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
    }

    .sku-spec {
      font-size: 12px;
      color: #909399;
      line-height: 1.2;
    }
  }

  .default-sku {
    font-weight: 600;
    color: #303133;
  }

  .batch-setting {
    margin-top: 20px;
    padding-top: 15px;
  }

  .batch-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .el-form-item {
      margin-bottom: 10px;
      margin-right: 0;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  // 调整表格内部元素的间距
  ::v-deep .el-table {
    font-size: 12px;

    .el-input-number {
      width: 100%;
    }

    .el-input__inner {
      padding-left: 5px;
      padding-right: 5px;
    }
  }
}
</style>
