package com.mn.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mn.entity.NyRole;
import com.mn.form.RoleForm;
import com.mn.model.TableDataInfo;
import com.mn.service.INyRoleService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@RestController
@RequestMapping("/role")
public class NyRoleController extends BaseController {

    @Resource
    INyRoleService roleService;

    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/list")
    public TableDataInfo list(RoleForm form) {
        startPage();
        List<NyRole> list = roleService.selectRoleList(form);
        return getDataTable(list);
    }

    /**
     * 根据角色编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping(value = "/{roleId}")
    public AjaxResult getInfo(@PathVariable Integer roleId) {
        return success(roleService.getById(roleId));
    }

    /**
     * 新增角色
     */
    @PreAuthorize("@ss.hasPermi('system:role:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody NyRole role) {
        role.setCreateBy(getUsername());
        role.setCreateTime(DateUtils.getNowDate());
        return toAjax(roleService.insertRole(role));
    }

    /**
     * 修改保存角色
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody NyRole role) {
        role.setUpdateBy(getUsername());
        role.setUpdateTime(DateUtils.getNowDate());
        return toAjax(roleService.updateRole(role));
    }

    @PreAuthorize("@ss.hasPermi('system:role:remove')")
    @PostMapping("/del")
    public AjaxResult remove(Integer roleId) {
        return toAjax(roleService.delByRoleId(roleId));
    }

}
