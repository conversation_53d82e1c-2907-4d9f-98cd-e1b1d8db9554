package com.mn.controller;

import com.mn.entity.PartnerInfo;
import com.mn.form.PartnerInfoForm;
import com.mn.model.TableDataInfo;
import com.mn.service.IPartnerInfoService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 供应商/客户信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@RestController
@RequestMapping("/partner-info")
public class PartnerInfoController extends BaseController {

    @Resource
    private IPartnerInfoService partnerInfoService;

    /**
     * 获取供应商/客户信息列表
     */
    @PreAuthorize("@ss.hasPermi('partner:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(PartnerInfoForm form) {
        startPage();
        form.setDeptId(getDeptId());
        List<PartnerInfo> list = partnerInfoService.selectPartnerInfoList(form);
        return getDataTable(list);
    }

    /**
     * 根据ID获取详细信息
     */
    //@PreAuthorize("@ss.hasPermi('partner:info:query')")
    @GetMapping(value = "/{partnerId}")
    public AjaxResult getInfo(@PathVariable Long partnerId) {
        return success(partnerInfoService.getById(partnerId));
    }

    /**
     * 新增供应商/客户信息
     */
    //@PreAuthorize("@ss.hasPermi('partner:info:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PartnerInfo partnerInfo) {
        partnerInfo.setCreateBy(getUsername());
        partnerInfo.setCreateTime(DateUtils.getNowDate());
        partnerInfo.setDeptId(getDeptId());
        return toAjax(partnerInfoService.insertPartnerInfo(partnerInfo));
    }

    /**
     * 修改供应商/客户信息
     */
    //@PreAuthorize("@ss.hasPermi('partner:info:edit')")
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PartnerInfo partnerInfo) {
        partnerInfo.setUpdateBy(getUsername());
        partnerInfo.setUpdateTime(DateUtils.getNowDate());
        return toAjax(partnerInfoService.updatePartnerInfo(partnerInfo));
    }

    /**
     * 删除供应商/客户信息
     */
    //@PreAuthorize("@ss.hasPermi('partner:info:remove')")
    @PostMapping("/del")
    public AjaxResult remove(Long partnerId) {
        return toAjax(partnerInfoService.deleteByPartnerId(partnerId));
    }
}
