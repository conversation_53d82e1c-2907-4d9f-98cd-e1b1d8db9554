
const API = require('../../api/request').default;

Page({
  data: {
    // 统计数据
    todayOrders: 0,
    monthAmount: '0.0',
    monthOrders: 0,
    unpaidOrders: 0,

    // 最新动态
    newsList: [
      {
        id: 1,
        title: '工厂新上架手机配件系列产品，欢迎选购',
        time: '2小时前'
      },
      {
        id: 2,
        title: '您的订单#MN202400156已发货，预计明日到达',
        time: '4小时前'
      },
      {
        id: 3,
        title: '春季促销活动开始，部分产品享受批量优惠',
        time: '6小时前'
      },
      {
        id: 4,
        title: '系统将于今晚22:00-24:00进行维护升级',
        time: '1天前'
      }
    ]
  },

  // 页面加载
  onLoad: function() {
    this.loadDashboardData();
  },

  // 页面显示时刷新数据
  onShow: function() {
    this.loadDashboardData();
  },

  // 加载仪表板数据
  loadDashboardData() {
    console.log('开始加载首页统计数据');

    API.getDashboardStats()
      .then(res => {
        console.log('首页统计数据获取成功:', res);

        // API模块返回的是完整响应：{code: 0, data: {...}, msg: "成功"}
        if (res && res.code === 0 && res.data) {
          const data = res.data;
          this.setData({
            todayOrders: data.todayOrders || 0,
            monthAmount: data.monthAmount || '0.0',
            monthOrders: data.monthOrders || 0,
            unpaidOrders: data.unpaidOrders || 0
          });
          console.log('首页统计数据更新完成');
        } else {
          console.error('统计数据格式异常:', res);
          wx.showToast({
            title: res?.msg || '获取统计数据失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取统计数据失败:', err);
        wx.showToast({
          title: '获取统计数据失败',
          icon: 'none'
        });
      });
  },

  // 快捷功能导航
  navigateToProductList() {
    wx.switchTab({
      url: '/pages/product/list/index'
    });
  },

  navigateToOrderList() {
    wx.switchTab({
      url: '/pages/order/index'
    });
  },

  navigateToInventory() {
    wx.navigateTo({
      url: '/pages/inventory/index'
    });
  },

  navigateToFinance() {
    wx.navigateTo({
      url: '/pages/finance/index'
    });
  },

  navigateToPrice() {
    wx.navigateTo({
      url: '/pages/price/index'
    });
  },

  navigateToReport() {
    wx.navigateTo({
      url: '/pages/report/index'
    });
  },

  navigateToService() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  },

  navigateToNotice() {
    wx.navigateTo({
      url: '/pages/notice/index'
    });
  },


});
