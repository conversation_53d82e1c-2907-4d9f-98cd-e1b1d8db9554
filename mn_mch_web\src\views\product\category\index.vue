<template>
	<div class="app-container">
		<!-- 搜索区域 -->
		<el-card class="search-card" shadow="never">
			<el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
				<el-form-item label="分类名称" prop="categoryName">
					<el-input v-model="queryParams.categoryName" placeholder="请输入分类名称" clearable
						@keyup.enter.native="handleQuery">
						<i slot="prefix" class="el-input__icon el-icon-search"></i>
					</el-input>
				</el-form-item>
				<el-form-item label="状态" prop="status">
					<el-select v-model="queryParams.status" placeholder="分类状态" clearable>
						<el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<!-- 操作按钮区 -->
		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" >新增分类</el-button>
			</el-col>
			<el-col :span="1.5">
				<el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
			</el-col>
			<right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
		</el-row>

		<!-- 分类树形表格 -->
		<el-card shadow="never">
			<el-table v-if="refreshTable" v-loading="loading" :data="categoryList" row-key="categoryId"
				:default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
				:header-cell-style="{ background: '#f5f7fa' }">
				<el-table-column prop="categoryName" label="分类名称" min-width="260">
					<template slot-scope="scope">
						<el-link type="primary" :underline="false" @click="handleUpdate(scope.row)">
							{{ scope.row.categoryName }}
						</el-link>
					</template>
				</el-table-column>
				<el-table-column prop="icon" label="分类图标" width="100">
					<template slot-scope="scope">
						<i :class="scope.row.icon"></i>
					</template>
				</el-table-column>
				<el-table-column prop="sortOrder" label="显示顺序" width="100" />
				<el-table-column prop="status" label="状态" width="100">
					<template slot-scope="scope">
						<el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
							{{ scope.row.status === 1 ? '启用' : '禁用' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" align="center" prop="createTime" width="180">
					<template slot-scope="scope">
						<span>{{ parseTime(scope.row.createTime) }}</span>
					</template>
				</el-table-column>
				<!-- 操作列 -->
				<el-table-column label="操作" align="center" width="120">
				  <template slot-scope="scope">
				    <el-dropdown trigger="click">
				      <span class="el-dropdown-link">
				        <el-button type="text">
				          更多操作<i class="el-icon-arrow-down el-icon--right"></i>
				        </el-button>
				      </span>
				      <el-dropdown-menu slot="dropdown">
				        <el-dropdown-item
				          @click.native="handleUpdate(scope.row)">
				          <i class="el-icon-edit"></i> 修改
				        </el-dropdown-item>
				        <el-dropdown-item 
				          @click.native="handleAdd(scope.row)">
				          <i class="el-icon-plus"></i> 新增子分类
				        </el-dropdown-item>
				        <el-dropdown-item v-if="scope.row.parentId != 0" 
				          @click.native="handleDelete(scope.row)">
				          <i class="el-icon-delete"></i> 删除
				        </el-dropdown-item>
				      </el-dropdown-menu>
				    </el-dropdown>
				  </template>
				</el-table-column>
			</el-table>
		</el-card>

		<!-- 添加或修改分类对话框 -->
		<el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
			<el-form ref="form" :model="form" :rules="rules" label-width="120px">
				<el-row>
					<el-col :span="24" v-if="form.parentId !== 0">
						<el-form-item label="上级分类" prop="parentId">
							<treeselect v-model="form.parentId" :options="categoryOptions" :normalizer="normalizer"
								placeholder="选择上级分类" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="24">
						<el-form-item label="分类名称" prop="categoryName">
							<el-input v-model="form.categoryName" placeholder="请输入分类名称" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="显示顺序" prop="sortOrder">
							<el-input-number v-model="form.sortOrder" :min="0" :max="999" controls-position="right" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="状态" prop="status">
							<el-radio-group v-model="form.status">
								<el-radio :label="1">启用</el-radio>
								<el-radio :label="0">禁用</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submitForm">确 定</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
		

	</div>
</template>

<script>
import {
	listCategory,
	getCategory,
	delCategory,
	addCategory,
	updateCategory
} from "@/api/product/category";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";

export default {
	name: "Category",
	components: {
		Treeselect,
		IconSelect
	},
	data() {
		return {
			// 遮罩层
			loading: true,
			// 显示搜索条件
			showSearch: true,
			// 表格树数据
			categoryList: [],
			// 分类树选项
			categoryOptions: [],
			// 弹出层标题
			title: "",
			// 是否显示弹出层
			open: false,
			// 是否展开，默认全部展开
			isExpandAll: true,
			// 重新渲染表格状态
			refreshTable: true,
			// 状态数据字典
			statusOptions: [
				{ label: '启用', value: 1 },
				{ label: '禁用', value: 0 }
			],
			// 查询参数
			queryParams: {
				categoryName: undefined,
				status: undefined
			},
			// 表单参数
			form: {
				categoryId: undefined,
				parentId: 0,
				categoryName: undefined,
				sortOrder: 0,
				icon: '',
				status: 1
			},
			// 表单校验规则
			rules: {
				categoryName: [{ required: true, message: "分类名称不能为空", trigger: "blur" }],
				sortOrder: [{ required: true, message: "显示顺序不能为空", trigger: "blur" }]
			}
		};
	},
	created() {
		this.getList();
	},
	methods: {
		/** 查询分类列表 */
		getList() {
			this.loading = true;
			listCategory(this.queryParams).then(response => {
				this.categoryList = this.handleTree(response.data, "categoryId", "parentId");
				this.loading = false;
			});
		},
		/** 转换分类数据结构 */
		normalizer(node) {
			if (node.children && !node.children.length) {
				delete node.children;
			}
			return {
				id: node.categoryId,
				label: node.categoryName,
				children: node.children
			};
		},
		// 取消按钮
		cancel() {
			this.open = false;
			this.reset();
		},
		// 表单重置
		reset() {
			this.form = {
				categoryId: undefined,
				parentId: 0,
				categoryName: undefined,
				sortOrder: 0,
				icon: '',
				status: 1
			};
			this.resetForm("form");
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.getList();
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.resetForm("queryForm");
			this.handleQuery();
		},
		/** 新增按钮操作 */
		handleAdd(row) {
			this.reset();
			if (row != undefined) {
				this.form.parentId = row.categoryId;
			}
			this.open = true;
			this.title = "添加分类";
			listCategory().then(response => {
				this.categoryOptions = this.handleTree(response.data, "categoryId", "parentId");
			});
		},
		/** 展开/折叠操作 */
		toggleExpandAll() {
			this.refreshTable = false;
			this.isExpandAll = !this.isExpandAll;
			this.$nextTick(() => {
				this.refreshTable = true;
			});
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset();
			getCategory(row.categoryId).then(response => {
				this.form = response.data;
				this.open = true;
				this.title = "修改分类";
				listCategory().then(response => {
					this.categoryOptions = this.handleTree(response.data, "categoryId", "parentId");
				});
			});
		},
		/** 提交按钮 */
		submitForm: function () {
			this.$refs["form"].validate(valid => {
				if (valid) {
					if (this.form.categoryId != undefined) {
						updateCategory(this.form).then(response => {
							this.$modal.msgSuccess("修改成功");
							this.open = false;
							this.getList();
						});
					} else {
						addCategory(this.form).then(response => {
							this.$modal.msgSuccess("新增成功");
							this.open = false;
							this.getList();
						});
					}
				}
			});
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			this.$modal.confirm('是否确认删除名称为"' + row.categoryName + '"的数据项？').then(function () {
				return delCategory(row.categoryId);
			}).then(() => {
				this.getList();
				this.$modal.msgSuccess("删除成功");
			}).catch(() => { });
		},
		/** 选择图标 */
		selectIcon() {
			this.$refs.iconSelect.reset();
			this.$refs.iconSelect.show();
		},
		/** 图标选择回调 */
		selected(name) {
			this.form.icon = name;
		}
	}
};
</script>

<style lang="scss" scoped>
.app-container {
  .search-card {
    margin-bottom: 15px;

    .el-form {
      margin-bottom: -18px;
    }
  }

  .el-card {
    .el-table {
      margin: 15px 0;
    }
  }

  .el-tag {
    margin-right: 5px;
  }

  .fixed-width {
    .el-button--mini {
      padding: 5px 8px;
    }
  }

  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
    
    .el-button {
      padding: 0;
      font-size: 14px;
    }
  }

  .el-dropdown-menu {
    .el-dropdown-item {
      i {
        margin-right: 5px;
      }
    }
  }
}
</style>