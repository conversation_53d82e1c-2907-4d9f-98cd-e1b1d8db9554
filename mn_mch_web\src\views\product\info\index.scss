.app-container {
  .detail-title {
    margin-top: 20px;
    padding-left: 10px;
    font-size: 16px;
    font-weight: bold;
    border-left: 4px solid #409EFF;
  }

  .el-table {
    margin-top: 15px;
  }

  .text-center {
    text-align: center;
  }
}

// 全屏对话框样式优化
.el-dialog__wrapper {
  &.is-fullscreen {
    .el-dialog {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      height: 100%;
      overflow: auto;

      .el-dialog__body {
        padding: 10px 20px;
        height: calc(100% - 110px);
        overflow-y: auto;
      }

      .el-dialog__footer {
        padding: 10px 20px;
        border-top: 1px solid #e4e7ed;
      }
    }
  }
}

.time-info {
  .time-detail {
    font-size: 12px;
    color: #909399;
    margin-top: 3px;
  }
}

// 商品详情样式
.product-detail-header {
  display: flex;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;

  .product-image {
    margin-right: 20px;

    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 120px;
      height: 120px;
      background-color: #f5f7fa;
      color: #909399;
      font-size: 30px;
    }
  }

  .product-basic-info {
    flex: 1;

    .product-name {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 10px;
    }

    .product-code {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
    }

    .product-price {
      font-size: 22px;
      font-weight: 600;
      color: #f56c6c;
      margin-bottom: 15px;
    }

    .product-stats {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 15px;

      span {
        margin-right: 20px;
        color: #606266;
        font-size: 14px;
        margin-bottom: 5px;
      }
    }
  }
}

.detail-tabs {
  margin-bottom: 20px;

  .detail-item {
    margin-bottom: 15px;
    line-height: 24px;

    .detail-label {
      color: #606266;
      margin-right: 8px;
    }

    .detail-value {
      color: #303133;
      font-weight: 500;
    }
  }
}

.spec-info-section {
  margin-bottom: 25px;

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    position: relative;

    i {
      font-size: 18px;
      margin-right: 8px;
      color: #409EFF;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
}

.spec-container {
  margin-top: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.spec-combinations-table {
  margin-top: 15px;
}

.price-text {
  color: #f56c6c;
  font-weight: 500;
}

.float-right {
  float: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-10 {
  margin-bottom: 10px;
}

// 图片上传样式
.avatar-uploader {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #409EFF;
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }

  .avatar {
    width: 120px;
    height: 120px;
    display: block;
  }
}