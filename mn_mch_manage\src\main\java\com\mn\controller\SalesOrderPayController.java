package com.mn.controller;

import com.mn.entity.SalesOrderPay;
import com.mn.form.SalesOrderPayForm;
import com.mn.model.TableDataInfo;
import com.mn.service.ISalesOrderPayService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@RestController
@RequestMapping("/sales-order-pay")
public class SalesOrderPayController extends BaseController {

    @Resource
    private ISalesOrderPayService salesOrderPayService;

    /**
     * 获取销售订单支付列表
     */
    @PreAuthorize("@ss.hasPermi('sales:order:pay:list')")
    @GetMapping("/list")
    public TableDataInfo list(SalesOrderPayForm form) {
        startPage();
        form.setDeptId(getDeptId());
        List<SalesOrderPay> list = salesOrderPayService.selectSalesOrderPayList(form);
        return getDataTable(list);
    }

    /**
     * 根据ID获取详细信息
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:pay:query')")
    @GetMapping(value = "/{payId}")
    public AjaxResult getInfo(@PathVariable Long payId) {
        return success(salesOrderPayService.getById(payId));
    }

    /**
     * 新增销售订单支付
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:pay:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody SalesOrderPay salesOrderPay) {
        salesOrderPay.setCreateTime(DateUtils.getNowDate());
        return toAjax(salesOrderPayService.insertSalesOrderPay(salesOrderPay));
    }

    /**
     * 修改销售订单支付
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:pay:edit')")
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody SalesOrderPay salesOrderPay) {
        return toAjax(salesOrderPayService.updateSalesOrderPay(salesOrderPay));
    }

    /**
     * 删除销售订单支付
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:pay:remove')")
    @PostMapping("/del")
    public AjaxResult remove(Long payId) {
        return toAjax(salesOrderPayService.deleteByPayId(payId));
    }
}
