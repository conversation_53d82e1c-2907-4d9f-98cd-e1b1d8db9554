.payment-slip-container {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-icon {
      margin-right: 5px;
      color: #409EFF;
    }
  }

  .payment-slip {
    background-color: #fff;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    font-size: 14px;
    max-width: 800px;
    margin: 0 auto;

    // 收款单标题和时间
    .payment-slip-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      font-size: 12px;
      color: #606266;

      .header-left {
        text-align: left;
      }

      .header-right {
        text-align: right;
      }
    }

    // 主标题
    .main-title {
      text-align: center;
      margin-bottom: 10px;

      h2 {
        font-size: 20px;
        font-weight: bold;
        margin: 0;
        color: #303133;
      }
    }

    // 订单编号
    .order-no-section {
      text-align: center;
      margin-bottom: 15px;

      .order-no {
        font-size: 14px;
        font-weight: bold;
      }
    }

    // 信息区域通用样式
    .info-section {
      margin-bottom: 15px;
      border: 1px solid #EBEEF5;
      border-radius: 4px;
      overflow: hidden;

      .section-title {
        background-color: #f5f7fa;
        padding: 8px 15px;
        font-weight: bold;
        color: #303133;
        border-bottom: 1px solid #EBEEF5;
        font-size: 14px;
      }

      .info-content {
        padding: 10px 15px;

        .info-row {
          display: flex;
          flex-wrap: wrap;

          .info-item {
            flex: 1;
            min-width: 200px;
            margin-bottom: 5px;
            display: flex;
            align-items: center;

            .label {
              font-weight: bold;
              color: #606266;
              min-width: 70px;
            }

            .value {
              color: #303133;
            }

            .status-tag {
              padding: 2px 6px;
              border-radius: 3px;
              font-size: 12px;

              &.approved {
                background-color: #67C23A;
                color: #fff;
              }

              &.pending {
                background-color: #E6A23C;
                color: #fff;
              }
            }
          }
        }
      }

      // 金额信息
      .payment-summary {
        padding: 10px 15px;

        .payment-table {
          width: 100%;
          border-collapse: collapse;

          td {
            padding: 8px;
          }

          .payment-label {
            font-weight: bold;
            color: #606266;
            white-space: nowrap;
          }

          .payment-value {
            color: #303133;
            padding-right: 20px;

            &.highlight {
              color: #F56C6C;
              font-weight: bold;
            }
          }
        }
      }

      // 备注内容
      .remark-content {
        padding: 10px 15px;
        color: #606266;
        line-height: 1.5;
      }
    }

    // 订单明细表格
    .order-items-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 12px;

      th, td {
        border: 1px solid #EBEEF5;
        padding: 8px;
        text-align: left;
      }

      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: bold;
      }

      tfoot {
        font-weight: bold;

        .text-right {
          text-align: right;
        }
      }
    }

    // 支付区域
    .payment-section {
      margin-bottom: 15px;
      border: 1px solid #EBEEF5;
      border-radius: 4px;
      overflow: hidden;

      .section-title {
        background-color: #f5f7fa;
        padding: 8px 15px;
        font-weight: bold;
        color: #303133;
        border-bottom: 1px solid #EBEEF5;
        font-size: 14px;
      }

      .qrcode-container {
        display: flex;
        overflow: hidden;

        .qrcode-wrapper {
          width: 220px;
          padding: 15px;
          text-align: center;
          border-left: 1px solid #EBEEF5;

          .qrcode {
            margin: 0 auto;
            width: 180px;
            height: 180px;
          }

          .qrcode-tip {
            margin-top: 10px;
            font-size: 12px;
            color: #606266;
          }
        }

        .payment-instructions {
          flex: 1;
          padding: 0;

          .instructions-title {
            background-color: #f8f8f8;
            padding: 8px 15px;
            font-weight: bold;
            color: #303133;
            border-bottom: 1px solid #EBEEF5;
            font-size: 14px;
          }

          .instruction-list {
            padding: 10px 15px 10px 35px;
            margin: 0;

            li {
              margin-bottom: 8px;
              color: #606266;
              font-size: 12px;
            }

            .highlight {
              font-weight: bold;
              color: #F56C6C;
            }
          }
        }
      }
    }
  }
}

// 打印样式
@media print {
  body {
    margin: 0;
    padding: 0;
  }

  .app-container {
    padding: 0 !important;
  }

  .el-card {
    border: none !important;
    box-shadow: none !important;
  }

  .card-header, .header-buttons, .payment-slip-header, .header-right {
    display: none !important;
  }

  .payment-slip {
    padding: 0 !important;
    box-shadow: none !important;
  }
}
