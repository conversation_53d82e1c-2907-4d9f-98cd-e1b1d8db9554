package com.mn.dao;

import java.util.List;

import org.springframework.data.repository.query.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.entity.SalesOrderItem;
import com.mn.form.SalesOrderItemForm;

/**
 * <p>
 * 销售单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface SalesOrderItemMapper extends BaseMapper<SalesOrderItem> {

    List<SalesOrderItem> selectSalesOrderItemList(SalesOrderItemForm form);

    /**
     * 根据订单ID查询订单明细列表
     */
    List<SalesOrderItem> selectByOrderId(@Param("orderId") Long orderId);
}
