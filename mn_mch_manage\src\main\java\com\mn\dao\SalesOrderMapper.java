package com.mn.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.entity.SalesOrder;
import com.mn.form.SalesOrderForm;

/**
 * <p>
 * 销售单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface SalesOrderMapper extends BaseMapper<SalesOrder> {

    List<SalesOrder> selectSalesOrderList(SalesOrderForm form);

    SalesOrder getByOrderNo(String orderNo);

    /**
     * 根据商户ID分页查询订单列表
     */
    List<SalesOrder> selectOrdersByDeptId(@Param("deptId") Integer deptId,
            @Param("offset") Integer offset,
            @Param("pageSize") Integer pageSize);

    /**
     * 获取今日订单数量
     */
    int getTodayOrderCount(@Param("deptId") Integer deptId);

    /**
     * 获取本月采购金额（分）
     */
    Long getMonthPurchaseAmount(@Param("deptId") Integer deptId);

    /**
     * 获取本月订单数量
     */
    int getMonthOrderCount(@Param("deptId") Integer deptId);

    /**
     * 获取待付款订单数量
     */
    int getUnpaidOrderCount(@Param("deptId") Integer deptId);

    /**
     * 获取销售统计总览
     */
    Map<String, Object> getStatisticsOverview(@Param("deptId") Integer deptId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    /**
     * 获取按时间维度的销售统计
     */
    List<Map<String, Object>> getStatisticsByTime(@Param("deptId") Integer deptId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("timeType") String timeType);

    /**
     * 获取按商品维度的销售统计
     */
    List<Map<String, Object>> getStatisticsByProduct(@Param("deptId") Integer deptId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("limit") Integer limit);

    /**
     * 获取按客户维度的销售统计
     */
    List<Map<String, Object>> getStatisticsByCustomer(@Param("deptId") Integer deptId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("limit") Integer limit);

    /**
     * 获取支付状态统计
     */
    List<Map<String, Object>> getPaymentStatusStatistics(@Param("deptId") Integer deptId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    // ==================== 财务报表相关方法 ====================

    /**
     * 获取财务报表数据
     */
    Map<String, Object> getFinanceReport(@Param("deptId") Integer deptId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("reportType") String reportType,
            @Param("partnerId") Integer partnerId);

    /**
     * 获取收款明细报表
     */
    List<Map<String, Object>> getPaymentDetails(@Param("deptId") Integer deptId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("partnerId") Integer partnerId,
            @Param("orderNo") String orderNo);

    /**
     * 获取应收账款报表
     */
    List<Map<String, Object>> getReceivables(@Param("deptId") Integer deptId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("partnerId") Integer partnerId);

    /**
     * 获取月度财务汇总
     */
    List<Map<String, Object>> getMonthlySummary(@Param("deptId") Integer deptId,
            @Param("year") String year);
}
