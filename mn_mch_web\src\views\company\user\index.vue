<template>
    <div class="app-container">
        <el-row :gutter="20">
            <!-- 用户数据，调整为占据整行 -->
            <el-col :span="24">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                    label-width="68px">
                    <el-form-item label="用户名称" prop="userName">
                        <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 240px"
                            @keyup.enter.native="handleQuery" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
                <!-- 其他内容保持不变 -->
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="el-icon-plus" size="mini"
                            @click="handleAdd">新增</el-button>
                    </el-col>
                    <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns">
                    </right-toolbar>
                </el-row>

                <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange" border stripe>
                    <el-table-column type="selection" width="45" align="center" />
                    <el-table-column label="用户名" align="center" key="userName" prop="userName" min-width="120" show-overflow-tooltip />
                    <el-table-column label="姓名" align="center" key="nickName" prop="nickName" min-width="100" show-overflow-tooltip />
                    <el-table-column label="企业名" align="center" key="merchantName" prop="merchantName" min-width="150" show-overflow-tooltip />
                    <el-table-column label="状态" align="center" key="enableFlag" width="80">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.enableFlag === 1 ? 'success' : 'info'" size="mini">
                                {{ formatStatus(scope.row.enableFlag) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" align="center" prop="createTime" width="150">
                        <template slot-scope="scope">
                            <span>{{ parseTime(scope.row.createTime) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="180" fixed="right">
                        <template slot-scope="scope">
                            <el-tooltip content="修改" placement="top" :enterable="false">
                                <el-button size="mini" type="primary" icon="el-icon-edit" circle
                                    @click="handleUpdate(scope.row)"></el-button>
                            </el-tooltip>
                            <el-tooltip content="注销" placement="top" :enterable="false" v-if="scope.row.enableFlag == 1">
                                <el-button size="mini" type="danger" icon="el-icon-delete" circle
                                    @click="handleDelete(scope.row)"></el-button>
                            </el-tooltip>
                            <el-tooltip content="更多操作" placement="top" :enterable="false">
                                <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" trigger="click">
                                    <el-button size="mini" type="info" icon="el-icon-more" circle></el-button>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item command="handleResetPwd" icon="el-icon-key">重置密码</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" />
            </el-col>
        </el-row>

        <!-- 添加或修改用户配置对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
            <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="small">
                <!-- 基本信息分组 -->
                <div class="form-section">
                    <div class="section-title">基本信息</div>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="姓名" prop="nickName">
                                <el-input v-model="form.nickName" placeholder="请输入姓名" maxlength="30" clearable />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="form.userId == undefined && optType == 'add'">
                            <el-form-item label="用户名" prop="userName">
                                <el-input v-model="form.userName" placeholder="请输入用户名" maxlength="30" clearable />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" v-if="form.userId == undefined && optType == 'add'">
                        <el-col :span="12">
                            <el-form-item label="用户密码" prop="password">
                                <el-input v-model="form.password" placeholder="请输入用户密码" type="password" maxlength="20"
                                    show-password>
                                    <template slot="append">
                                        <el-button @click="generatePassword" type="primary" icon="el-icon-refresh" title="生成随机密码"></el-button>
                                    </template>
                                </el-input>
                                <div class="form-tip">密码长度5-20位，建议使用字母、数字和特殊字符的组合</div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                
                <!-- 角色分组 -->
                <div class="form-section">
                    <div class="section-title">角色设置</div>
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="角色" prop="roleIds">
                                <el-select v-model="form.roleIds" multiple placeholder="请选择角色" style="width: 100%"
                                    collapse-tags>
                                    <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName"
                                        :value="item.roleId"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    companyUserList,
    companyGetInfo,
    companyAdd,
    companyEdit,
    resetUserPwd,
    changeUserStatus
} from "@/api/company/companyUser";
import { deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
    name: "CompanyUser",
    components: { Treeselect },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户表格数据
            userList: null,
            // 弹出层标题
            title: "",
            // 部门树选项
            deptOptions: undefined,
            // 是否显示弹出层
            open: false,
            // 提交按钮加载状态
            submitLoading: false,
            // 部门名称
            merchantName: undefined,
            // 默认密码
            initPassword: undefined,
            // 日期范围
            dateRange: [],
            // 角色选项
            roleOptions: [],
            // 表单参数
            optType: undefined,
            form: {},
            defaultProps: {
                children: "children",
                label: "label"
            },
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userName: undefined,
                merchantName: undefined,
                deptId: undefined
            },
            // 列信息
            columns: [
                { key: 0, label: `用户编号`, visible: true },
                { key: 1, label: `用户名称`, visible: true },
                { key: 2, label: `用户姓名`, visible: true },
                { key: 3, label: `企业`, visible: true },
                { key: 4, label: `状态`, visible: true },
                { key: 5, label: `创建时间`, visible: true }
            ],
            // 表单校验
            rules: {
                userName: [
                    { required: true, message: "用户名称不能为空", trigger: "blur" },
                    { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
                ],
                nickName: [
                    { required: true, message: "用户姓名不能为空", trigger: "blur" }
                ],
                password: [
                    { required: true, message: "用户密码不能为空", trigger: "blur" },
                    { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
                ]
            }
        };
    },
    watch: {
        merchantName(val) {
            this.$refs.tree.filter(val);
        }
    },
    created() {
        this.getList();
        //this.getDeptTree();
    },
    methods: {
        formatStatus(status) {
            if (status === 0) {
                return '注销'
            } else if (status === 1) {
                return '正常'
            } else {
                return ''
            }
        },
        /** 查询用户列表 */
        getList() {
            this.loading = true;
            companyUserList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
                this.userList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        /** 查询部门下拉树结构 */
        getDeptTree() {
            deptTreeSelect().then(response => {
                this.deptOptions = response.data;
            });
        },
        // 筛选节点
        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;
        },
        // 节点单击事件
        handleNodeClick(data) {
            this.queryParams.deptId = data.id;
            this.handleQuery();
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                userId: undefined,
                deptId: undefined,
                userName: undefined,
                nickName: undefined,
                password: undefined,
                status: "0",
                roleIds: []
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.dateRange = [];
            this.resetForm("queryForm");
            this.queryParams.deptId = undefined;
            this.$refs.tree.setCurrentKey(null);
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.userId);
            this.single = selection.length != 1;
            this.multiple = !selection.length;
        },
        // 更多操作触发
        handleCommand(command, row) {
            switch (command) {
                case "handleResetPwd":
                    this.handleResetPwd(row);
                    break;
                default:
                    break;
            }
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            companyGetInfo().then(response => {
                this.roleOptions = response.roles;
                this.open = true;
                this.title = "添加用户";
                this.form.password = this.initPassword;
                this.optType = 'add';
            });
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const userId = row.userId || this.ids;
            companyGetInfo(userId).then(response => {
                this.form = response.data;
                this.roleOptions = response.roles;
                this.$set(this.form, "roleIds", response.roleIds);
                this.open = true;
                this.title = "修改用户";
                this.form.password = "";
                this.optType = 'edit';
            });
        },
        /** 重置密码按钮操作 */
        handleResetPwd(row) {
            this.$prompt('请输入"' + row.userName + '"的新密码', "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                closeOnClickModal: false,
                inputPattern: /^.{5,20}$/,
                inputErrorMessage: "用户密码长度必须介于 5 和 20 之间"
            }).then(({ value }) => {
                resetUserPwd(row.userId, value).then(response => {
                    this.$modal.msgSuccess("修改成功，新密码是：" + value);
                });
            }).catch(() => { });
        },
        /** 生成随机密码 */
        generatePassword() {
            const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
            const specialChars = '!@#$%^&*()_+';
            let password = '';
            
            // 生成8位基本字符
            for (let i = 0; i < 8; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            
            // 添加1-2个特殊字符
            for (let i = 0; i < 2; i++) {
                const pos = Math.floor(Math.random() * (password.length + 1));
                const char = specialChars.charAt(Math.floor(Math.random() * specialChars.length));
                password = password.slice(0, pos) + char + password.slice(pos);
            }
            
            this.form.password = password;
        },
        /** 提交按钮 */
        submitForm: function () {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    this.submitLoading = true;
                    if (this.form.userId != undefined) {
                        companyEdit(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    } else {
                        companyAdd(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const userIds = row.userId || this.ids;
            this.$modal.confirm('是否确认注销用户编号为"' + userIds + '"的数据项？').then(function () {
                return changeUserStatus(userIds, 0);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("注销成功");
            }).catch(() => { });
        }
    }
};
</script>

<style lang="scss" scoped>
.app-container {
    .head-container {
        padding: 10px;
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
        margin-bottom: 20px;
    }

    .el-tree {
        margin-top: 10px;
    }

    .el-table {
        .el-dropdown {
            margin-left: 5px;
        }
    }
}

/* 表单分组样式 */
.form-section {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px dashed #ebeef5;
    
    &:last-child {
        border-bottom: none;
    }
    
    .section-title {
        font-size: 14px;
        font-weight: bold;
        color: #409EFF;
        margin-bottom: 15px;
        position: relative;
        padding-left: 10px;
        
        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 14px;
            background-color: #409EFF;
            border-radius: 1px;
        }
    }
}

.form-tip {
    font-size: 12px;
    color: #909399;
    line-height: 1.2;
    padding-top: 4px;
}
</style>