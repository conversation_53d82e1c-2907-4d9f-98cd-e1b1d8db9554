/*
 Navicat Premium Data Transfer

 Source Server         : 美诺优选映射
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : localhost:13306
 Source Schema         : mn_pay

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 20/06/2025 11:06:59
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for meloon_user
-- ----------------------------
DROP TABLE IF EXISTS `meloon_user`;
CREATE TABLE `meloon_user`  (
  `open_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `nick_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `mobile` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像url',
  `status` int(1) NULL DEFAULT NULL COMMENT '激活状态: 1=已激活，2=已禁用',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`open_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of meloon_user
-- ----------------------------
INSERT INTO `meloon_user` VALUES ('oHhgC5ctzTscyPAKPHOAn-w43HNI', NULL, NULL, '', 1, NULL, '2025-04-22 14:32:15');
INSERT INTO `meloon_user` VALUES ('oHhgC5Q21vLN4y2k6c6c9mucQ4Hs', NULL, NULL, '', 1, NULL, '2025-04-16 09:39:17');
INSERT INTO `meloon_user` VALUES ('oHhgC5W7ks5mOPVGSB2WNh5Itl1s', NULL, NULL, '', 1, NULL, '2025-04-17 11:24:45');
INSERT INTO `meloon_user` VALUES ('oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', NULL, NULL, '', 1, NULL, '2025-03-13 17:21:09');

-- ----------------------------
-- Table structure for merchant_channel
-- ----------------------------
DROP TABLE IF EXISTS `merchant_channel`;
CREATE TABLE `merchant_channel`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dept_id` int(11) NOT NULL COMMENT '集团ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `channel_id` int(11) NOT NULL COMMENT '支付渠道ID',
  `channel_merchant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道商户号',
  `channel_app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道应用ID',
  `channel_app_secret` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道应用密钥',
  `channel_private_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '渠道私钥',
  `channel_public_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '渠道公钥',
  `channel_cert_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道证书路径',
  `custom_fee_rate` decimal(6, 4) NULL DEFAULT NULL COMMENT '自定义手续费率',
  `status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'PENDING' COMMENT '状态(PENDING-待审核,ACTIVE-启用,INACTIVE-禁用,REJECTED-拒绝)',
  `apply_time` datetime(0) NULL DEFAULT NULL COMMENT '申请时间',
  `approve_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `approve_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核人',
  `reject_reason` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `config_json` json NULL COMMENT '扩展配置JSON',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_merchant_channel`(`merchant_id`, `channel_id`) USING BTREE,
  INDEX `idx_dept_id`(`dept_id`) USING BTREE,
  INDEX `idx_merchant_id`(`merchant_id`) USING BTREE,
  INDEX `idx_channel_id`(`channel_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户支付渠道关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of merchant_channel
-- ----------------------------

-- ----------------------------
-- Table structure for merchant_info
-- ----------------------------
DROP TABLE IF EXISTS `merchant_info`;
CREATE TABLE `merchant_info`  (
  `merchant_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商户ID',
  `dept_id` int(11) NOT NULL COMMENT '所属集团ID',
  `merchant_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户类型：O-小微，B-企业，C-个体',
  `merchant_short_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户简称',
  `merchant_full_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司名称或客户名称',
  `industry_category` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行业类别',
  `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
  `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `legal_person_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业法人姓名',
  `legal_person_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业法人手机号码',
  `contact_email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `legal_person_id_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业法人证件类型',
  `legal_person_id_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业法人证件号',
  `id_card_validity_start` date NULL DEFAULT NULL COMMENT '身份证有效期开始日期',
  `id_card_validity_end` date NULL DEFAULT NULL COMMENT '身份证有效期结束日期',
  `id_card_front_file_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证正面照片文件ID',
  `id_card_back_file_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证背面照片文件ID',
  `business_license_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营业执照号',
  `unified_social_credit_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `business_license_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营业执照图片地址',
  `business_license_validity_start` date NULL DEFAULT NULL COMMENT '营业执照有效期开始日期',
  `business_license_validity_end` date NULL DEFAULT NULL COMMENT '营业执照有效期结束日期',
  `bank_account_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行账户名',
  `bank_account_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行账号',
  `bank_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户银行名称',
  `bank_branch_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户支行名称',
  `bank_branch_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户行联行号',
  `settlement_cycle` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'T1' COMMENT '结算周期(T0,T1,T7,T30)',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'PROCESSING' COMMENT '入驻状态：PROCESSING-处理中，SUCCESS-成功，FAILED-失败',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`merchant_id`) USING BTREE,
  INDEX `idx_dept_id`(`dept_id`) USING BTREE,
  INDEX `idx_merchant_type`(`merchant_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_unified_social_credit_code`(`unified_social_credit_code`) USING BTREE,
  INDEX `idx_bank_account_number`(`bank_account_number`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of merchant_info
-- ----------------------------
INSERT INTO `merchant_info` VALUES (1, 1, 'B', '微企付测试商户', '微企付测试商户', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '************', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'T1', 'SUCCESS', '从订单数据自动创建，企业ID：************', '2025-04-11 14:35:45', '2025-06-05 15:22:47', 'system', NULL);

-- ----------------------------
-- Table structure for merchant_order
-- ----------------------------
DROP TABLE IF EXISTS `merchant_order`;
CREATE TABLE `merchant_order`  (
  `order_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单金额',
  `amount` bigint(20) NOT NULL COMMENT '支付金额(分)',
  `payee_ent_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收款商户企业ID',
  `payee_ent_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收款商户名称',
  `create_time` datetime(0) NOT NULL COMMENT '平台订单生成时间',
  `goods_info` json NULL COMMENT '商品信息JSON',
  `status` int(1) NOT NULL DEFAULT 0 COMMENT '订单状态\r\n0: 待支付\r\n1:支付成功\r\n2:订单关闭\r\n3:退款\r\n4:支付中',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `create_user_id` int(11) NULL DEFAULT NULL COMMENT '创建人ID',
  `open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`order_id`) USING BTREE,
  INDEX `merchant_order_user_id`(`create_user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of merchant_order
-- ----------------------------
INSERT INTO `merchant_order` VALUES ('****************', 7500, '************', '微企付测试商户', '2025-04-11 14:35:45', '[{\"good_img\": \"http://mall.meloon.cn/img/3.jpg\", \"good_name\": \"适用华为mate60pro手机膜mate60水凝膜mate60pro\", \"good_amount\": 7500, \"good_number\": 1}]', 1, '2025-04-11 14:36:20', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 22500, '************', '微企付测试商户', '2025-04-14 17:19:25', '[{\"good_img\": \"http://mall.meloon.cn/img/3.jpg\", \"good_name\": \"适用华为mate60pro手机膜mate60水凝膜mate60pro\", \"good_amount\": 7500, \"good_number\": 3}]', 1, '2025-04-14 17:20:02', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 920, '************', '微企付测试商户', '2025-04-14 17:20:58', '[{\"good_img\": \"http://mall.meloon.cn/img/5.jpg\", \"good_name\": \"适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s\", \"good_amount\": 230, \"good_number\": 4}]', 4, '2025-04-14 17:20:25', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 1797, '************', '微企付测试商户', '2025-04-15 13:57:49', '[{\"good_img\": \"http://mall.meloon.cn/img/2.jpg\", \"good_name\": \"适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜\", \"good_amount\": 599, \"good_number\": 3}]', 1, '2025-04-15 13:59:13', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 230, '************', '微企付测试商户', '2025-04-16 09:39:55', '[{\"good_img\": \"http://mall.meloon.cn/img/5.jpg\", \"good_name\": \"适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s\", \"good_amount\": 230, \"good_number\": 1}]', 4, '2025-04-16 09:39:22', NULL, NULL);
INSERT INTO `merchant_order` VALUES ('2025041609101051', 599, '************', '微企付测试商户', '2025-04-16 09:40:28', '[{\"good_img\": \"http://mall.meloon.cn/img/2.jpg\", \"good_name\": \"适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜\", \"good_amount\": 599, \"good_number\": 1}]', 4, '2025-04-16 09:39:55', NULL, NULL);
INSERT INTO `merchant_order` VALUES ('2025041610101053', 920, '************', '微企付测试商户', '2025-04-16 10:39:15', '[{\"good_img\": \"http://mall.meloon.cn/img/5.jpg\", \"good_name\": \"适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s\", \"good_amount\": 230, \"good_number\": 4}]', 4, '2025-04-16 10:38:42', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025041711101055', 230, '************', '微企付测试商户', '2025-04-17 11:07:02', '[{\"good_img\": \"http://mall.meloon.cn/img/5.jpg\", \"good_name\": \"适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s\", \"good_amount\": 230, \"good_number\": 1}]', 4, '2025-04-17 11:06:28', NULL, 'oHhgC5Q21vLN4y2k6c6c9mucQ4Hs');
INSERT INTO `merchant_order` VALUES ('2025041711101057', 3438, '************', '微企付测试商户', '2025-04-17 11:22:12', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 3}]', 4, '2025-04-17 11:21:39', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025041711101059', 3438, '************', '微企付测试商户', '2025-04-17 11:25:09', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 3}]', 4, '2025-04-17 11:24:36', NULL, 'oHhgC5W7ks5mOPVGSB2WNh5Itl1s');
INSERT INTO `merchant_order` VALUES ('2025041711101061', 460, '************', '微企付测试商户', '2025-04-17 11:25:41', '[{\"good_img\": \"http://mall.meloon.cn/img/5.jpg\", \"good_name\": \"适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s\", \"good_amount\": 230, \"good_number\": 2}]', 4, '2025-04-17 11:25:08', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025041711101063', 1146, '************', '微企付测试商户', '2025-04-17 11:26:35', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', 4, '2025-04-17 11:26:01', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 100, '************', '微企付测试商户', '2025-04-21 15:26:45', '[{\"good_name\": \"标准商品\", \"good_amount\": 100, \"good_number\": 1}]', 4, '2025-04-21 15:26:02', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 111, '************', '微企付测试商户', '2025-04-22 09:54:54', '[{\"good_name\": \"标准商品\", \"good_amount\": 111, \"good_number\": 1}]', 1, '2025-04-22 09:57:35', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 100, '************', '微企付测试商户', '2025-04-22 09:04:08', '[{\"good_name\": \"标准商品\", \"good_amount\": 100, \"good_number\": 1}]', 4, '2025-04-22 09:03:31', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025042209101097', 100, '************', '微企付测试商户', '2025-04-22 09:09:16', '[{\"good_name\": \"标准商品\", \"good_amount\": 100, \"good_number\": 1}]', 4, '2025-04-22 09:08:40', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 1000, '************', '微企付测试商户', '2025-04-22 09:19:41', '[{\"good_name\": \"标准商品\", \"good_amount\": 1000, \"good_number\": 1}]', 4, '2025-04-22 09:19:05', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 100, '************', '微企付测试商户', '2025-04-22 10:04:22', '[{\"good_name\": \"标准商品\", \"good_amount\": 100, \"good_number\": 1}]', 1, '2025-04-22 10:04:43', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 200, '************', '微企付测试商户', '2025-04-22 10:13:03', '[{\"good_name\": \"标准商品\", \"good_amount\": 200, \"good_number\": 1}]', 1, '2025-04-22 10:13:23', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 1100, '************', '微企付测试商户', '2025-04-22 10:16:41', '[{\"good_name\": \"标准商品\", \"good_amount\": 1100, \"good_number\": 1}]', 1, '2025-04-22 10:16:59', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 1100, '************', '微企付测试商户', '2025-04-22 10:19:38', '[{\"good_name\": \"标准商品\", \"good_amount\": 1100, \"good_number\": 1}]', 1, '2025-04-22 10:19:58', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 3300, '************', '微企付测试商户', '2025-04-22 10:20:04', '[{\"good_name\": \"标准商品\", \"good_amount\": 3300, \"good_number\": 1}]', 4, '2025-04-22 10:19:30', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 7700, '************', '微企付测试商户', '2025-04-22 11:40:34', '[{\"good_name\": \"标准商品\", \"good_amount\": 7700, \"good_number\": 1}]', 1, '2025-04-22 11:46:48', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 7700, '************', '微企付测试商户', '2025-04-22 11:42:06', '[{\"good_name\": \"标准商品\", \"good_amount\": 7700, \"good_number\": 1}]', 1, '2025-04-22 11:46:48', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 3300, '************', '微企付测试商户', '2025-04-22 11:48:34', '[{\"good_name\": \"标准商品\", \"good_amount\": 3300, \"good_number\": 1}]', 1, '2025-04-22 11:49:09', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 88800, '************', '微企付测试商户', '2025-04-22 14:00:03', '[{\"good_name\": \"标准商品\", \"good_amount\": 88800, \"good_number\": 1}]', 1, '2025-04-22 14:00:31', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 7400, '************', '微企付测试商户', '2025-04-22 14:02:40', '[{\"good_name\": \"标准商品\", \"good_amount\": 7400, \"good_number\": 1}]', 1, '2025-04-22 14:03:00', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 20000, '************', '微企付测试商户', '2025-04-22 14:20:07', '[{\"good_name\": \"标准商品\", \"good_amount\": 20000, \"good_number\": 1}]', 1, '2025-04-22 14:20:46', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 12300, '************', '微企付测试商户', '2025-04-22 14:21:42', '[{\"good_name\": \"标准商品\", \"good_amount\": 12300, \"good_number\": 1}]', 1, '2025-04-22 14:22:02', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 122, '************', '微企付测试商户', '2025-04-22 14:30:01', '[{\"good_name\": \"标准商品\", \"good_amount\": 122, \"good_number\": 1}]', 4, '2025-04-22 14:29:27', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025042214101093', 1146, '************', '微企付测试商户', '2025-04-22 14:32:19', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', 4, '2025-04-22 14:31:45', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI');
INSERT INTO `merchant_order` VALUES ('2025042214101095', 125649, '************', '微企付测试商户', '2025-04-22 14:35:18', '[{\"good_name\": \"标准商品\", \"good_amount\": 125649, \"good_number\": 1}]', 4, '2025-04-22 14:34:44', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI');
INSERT INTO `merchant_order` VALUES ('****************', 15863, '************', '微企付测试商户', '2025-04-22 15:02:01', '[{\"good_name\": \"标准商品\", \"good_amount\": 15863, \"good_number\": 1}]', 4, '2025-04-22 15:01:27', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI');
INSERT INTO `merchant_order` VALUES ('****************', 2174, '************', '微企付测试商户', '2025-04-22 15:02:39', '[{\"good_name\": \"标准商品\", \"good_amount\": 2174, \"good_number\": 1}]', 1, '2025-04-22 15:02:57', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 5846, '************', '微企付测试商户', '2025-04-22 15:03:39', '[{\"good_name\": \"标准商品\", \"good_amount\": 5846, \"good_number\": 1}]', 1, '2025-04-22 15:03:59', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 9900, '************', '微企付测试商户', '2025-04-22 15:14:28', '[{\"good_name\": \"标准商品\", \"good_amount\": 9900, \"good_number\": 1}]', 1, '2025-04-22 15:15:18', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 1367, '************', '微企付测试商户', '2025-04-22 15:50:44', '[{\"good_name\": \"标准商品\", \"good_amount\": 1367, \"good_number\": 1}]', 4, '2025-04-22 15:50:10', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI');
INSERT INTO `merchant_order` VALUES ('****************', 100, '************', '微企付测试商户', '2025-04-30 14:32:36', '[{\"good_name\": \"标准商品\", \"good_amount\": 100, \"good_number\": 1}]', 1, '2025-04-30 14:33:30', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 7700, '************', '微企付测试商户', '2025-04-30 14:37:51', '[{\"good_name\": \"标准商品\", \"good_amount\": 7700, \"good_number\": 1}]', 1, '2025-04-30 14:38:12', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 9900, '************', '微企付测试商户', '2025-04-30 15:15:38', '[{\"good_name\": \"标准商品\", \"good_amount\": 9900, \"good_number\": 1}]', 1, '2025-04-30 15:16:02', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 14300, '************', '微企付测试商户', '2025-05-06 09:11:15', '[{\"good_name\": \"标准商品\", \"good_amount\": 14300, \"good_number\": 1}]', 4, '2025-05-06 09:10:35', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI');
INSERT INTO `merchant_order` VALUES ('****************', 6600, '************', '微企付测试商户', '2025-05-06 09:11:49', '[{\"good_name\": \"标准商品\", \"good_amount\": 6600, \"good_number\": 1}]', 1, '2025-05-06 09:12:34', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 9900, '************', '微企付测试商户', '2025-05-06 10:11:24', '[{\"good_name\": \"标准商品\", \"good_amount\": 9900, \"good_number\": 1}]', 1, '2025-05-06 10:11:46', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('****************', 1300, '************', '微企付测试商户', '2025-05-06 11:55:31', '[{\"good_name\": \"标准商品\", \"good_amount\": 1300, \"good_number\": 1}]', 4, '2025-05-06 11:54:50', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI');
INSERT INTO `merchant_order` VALUES ('2025050917101121', 1146, '************', '微企付测试商户', '2025-05-09 17:01:01', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', 4, '2025-05-09 17:00:20', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025050918101123', 5500, '************', '微企付测试商户', '2025-05-09 18:29:48', '[{\"good_name\": \"标准商品\", \"good_amount\": 5500, \"good_number\": 1}]', 4, '2025-05-09 18:29:07', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025050918101125', 3300, '************', '微企付测试商户', '2025-05-09 18:31:05', '[{\"good_name\": \"标准商品\", \"good_amount\": 3300, \"good_number\": 1}]', 4, '2025-05-09 18:30:25', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025052415101128', 1146, '************', '微企付测试商户', '2025-05-24 15:51:41', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', 4, '2025-05-24 15:50:59', NULL, 'oHhgC5Q21vLN4y2k6c6c9mucQ4Hs');
INSERT INTO `merchant_order` VALUES ('2025053011101158', 1146, '************', '微企付测试商户', '2025-05-30 11:41:12', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', 4, '2025-05-30 11:40:29', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025053011101162', 1146, '************', '微企付测试商户', '2025-05-30 11:52:50', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', 4, '2025-05-30 11:52:07', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025060317101167', 5990, '************', '微企付测试商户', '2025-06-03 17:27:03', '[{\"good_img\": \"http://mall.meloon.cn/img/2.jpg\", \"good_name\": \"适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜\", \"good_amount\": 599, \"good_number\": 10}]', 4, '2025-06-03 17:26:15', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk');
INSERT INTO `merchant_order` VALUES ('2025************', 1146, '************', '微企付测试商户', '2025-06-05 08:40:07', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', 4, '2025-06-05 08:39:18', NULL, 'oHhgC5bdeWcRlZNGYAV4J2vbAd6c');

-- ----------------------------
-- Table structure for merchant_order_msg
-- ----------------------------
DROP TABLE IF EXISTS `merchant_order_msg`;
CREATE TABLE `merchant_order_msg`  (
  `msg_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `out_payment_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台支付单号',
  `create_time` datetime(0) NOT NULL COMMENT '成时间',
  `msg_type` int(3) NOT NULL DEFAULT 1 COMMENT '消息类型,1:支付成功',
  PRIMARY KEY (`msg_id`) USING BTREE,
  INDEX `msg_out_payment_id`(`out_payment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of merchant_order_msg
-- ----------------------------
INSERT INTO `merchant_order_msg` VALUES (17, '****************', '2025-04-22 11:46:47', 1);
INSERT INTO `merchant_order_msg` VALUES (18, '****************', '2025-04-22 11:46:48', 1);
INSERT INTO `merchant_order_msg` VALUES (19, '****************', '2025-04-22 11:49:09', 1);
INSERT INTO `merchant_order_msg` VALUES (20, '****************', '2025-04-22 14:00:31', 1);
INSERT INTO `merchant_order_msg` VALUES (21, '****************', '2025-04-22 14:03:00', 1);
INSERT INTO `merchant_order_msg` VALUES (22, '****************', '2025-04-22 14:20:45', 1);
INSERT INTO `merchant_order_msg` VALUES (23, '****************', '2025-04-22 14:22:02', 1);
INSERT INTO `merchant_order_msg` VALUES (24, '****************', '2025-04-22 15:02:57', 1);
INSERT INTO `merchant_order_msg` VALUES (25, '****************', '2025-04-22 15:03:59', 1);
INSERT INTO `merchant_order_msg` VALUES (26, '****************', '2025-04-22 15:15:18', 1);
INSERT INTO `merchant_order_msg` VALUES (27, '****************', '2025-04-30 14:33:30', 1);
INSERT INTO `merchant_order_msg` VALUES (28, '****************', '2025-04-30 14:38:11', 1);
INSERT INTO `merchant_order_msg` VALUES (29, '****************', '2025-04-30 15:16:01', 1);
INSERT INTO `merchant_order_msg` VALUES (30, '****************', '2025-05-06 09:12:34', 1);
INSERT INTO `merchant_order_msg` VALUES (31, '****************', '2025-05-06 10:11:46', 1);

-- ----------------------------
-- Table structure for merchant_pay_order
-- ----------------------------
DROP TABLE IF EXISTS `merchant_pay_order`;
CREATE TABLE `merchant_pay_order`  (
  `out_payment_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台支付单号',
  `amount` bigint(20) NOT NULL COMMENT '支付金额(分)',
  `currency` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `expire_time` datetime(0) NOT NULL COMMENT '过期时间',
  `memo` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附言',
  `attachment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单附加信息',
  `payee_ent_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收款商户企业ID',
  `merchant_id` bigint(20) NULL DEFAULT NULL COMMENT '商户ID',
  `payee_ent_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收款商户名称',
  `create_time` datetime(0) NOT NULL COMMENT '平台订单生成时间',
  `goods_info` json NULL COMMENT '商品信息JSON',
  `payer_options` json NULL COMMENT '付款方信息JSON',
  `channel_options` json NULL COMMENT '渠道选项JSON',
  `profit_allocation_flag` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'NO_PROFIT_ALLOCATION' COMMENT '分账标识',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'CREATED' COMMENT '订单状态\r\nPROCESSING 支付处理中\r\nBANK_ACCEPTED 提交银行成功\r\nSUCCEEDED 支付成功\r\nCLOSED 支付关单',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `payment_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微企付支付单号',
  `fee_amount` bigint(20) NULL DEFAULT NULL COMMENT '总手续费金额，单位：分',
  `mse_fee_amount` bigint(20) NULL DEFAULT NULL COMMENT '微企付手续费金额, 单位：分',
  `wxp_fee_amount` bigint(20) NULL DEFAULT NULL COMMENT '微信手续费金额, 单位：分',
  `close_reason` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关单原因',
  `close_time` datetime(0) NULL DEFAULT NULL COMMENT '关单成功时间',
  `out_refund_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台退款单号',
  `refund_amount` bigint(20) NULL DEFAULT NULL COMMENT '退款金额，单位：分',
  `refund_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款原因',
  `refund_time` datetime(0) NULL DEFAULT NULL COMMENT '退款时间',
  `refund_channel` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款渠道:ORIGINAL 原路退款,OTHER_BALANCE 退到卖家账户余额,OTHER_BANKCARD 退到买家其他银行卡',
  `refund_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款状态:ACCEPTED 退款受理成功\r\nABNORMAL 退款异常\r\nSUCCEEDED 退款成功\r\nMERCHANT_SUCCEEDED 退回商户余额成功\r\nFAILED 退款失败',
  `create_user_id` int(11) NOT NULL COMMENT '创建人ID',
  `order_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单金额',
  `payment_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '二维码支付:qrcode-pay\r\n小程序支付:mp-pay\r\nH5支付:h5-pay\r\nAPP支付:app-pay\r\nPC支付:pc-pay',
  `pay_time` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付完成时间',
  `open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `pay_channel` int(2) NOT NULL DEFAULT 1 COMMENT '1：微企付 2：银盛',
  PRIMARY KEY (`out_payment_id`) USING BTREE,
  UNIQUE INDEX `uk_out_payment_id`(`out_payment_id`) USING BTREE,
  INDEX `idx_payee_ent_id`(`payee_ent_id`) USING BTREE,
  INDEX `mpo_user_id`(`create_user_id`) USING BTREE,
  INDEX `mpo_order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of merchant_pay_order
-- ----------------------------
INSERT INTO `merchant_pay_order` VALUES ('****************', 7500, 'CNY', '2025-04-11 15:05:45', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-11 14:35:45', '[{\"good_img\": \"http://mall.meloon.cn/img/3.jpg\", \"good_name\": \"适用华为mate60pro手机膜mate60水凝膜mate60pro\", \"good_amount\": 7500, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0051\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504113505800051', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-11 14:36:14', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 22500, 'CNY', '2025-04-14 17:49:25', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-14 17:19:25', '[{\"good_img\": \"http://mall.meloon.cn/img/3.jpg\", \"good_name\": \"适用华为mate60pro手机膜mate60水凝膜mate60pro\", \"good_amount\": 7500, \"good_number\": 3}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0052\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504143505850052', 23, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-14 17:19:46', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 920, 'CNY', '2025-04-14 17:50:58', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-14 17:20:58', '[{\"good_img\": \"http://mall.meloon.cn/img/5.jpg\", \"good_name\": \"适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s\", \"good_amount\": 230, \"good_number\": 4}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504143505850054', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 1797, 'CNY', '2025-04-15 14:27:49', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-15 13:57:49', '[{\"good_img\": \"http://mall.meloon.cn/img/2.jpg\", \"good_name\": \"适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜\", \"good_amount\": 599, \"good_number\": 3}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0037\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504153505860037', 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-15 13:58:55', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 230, 'CNY', '2025-04-16 10:09:55', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-16 09:39:55', '[{\"good_img\": \"http://mall.meloon.cn/img/5.jpg\", \"good_name\": \"适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s\", \"good_amount\": 230, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504163505880013', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, NULL, 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 599, 'CNY', '2025-04-16 10:10:28', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-16 09:40:28', '[{\"good_img\": \"http://mall.meloon.cn/img/2.jpg\", \"good_name\": \"适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜\", \"good_amount\": 599, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504163505880014', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025041609101051', 'mp-pay', NULL, NULL, 1);
INSERT INTO `merchant_pay_order` VALUES ('2025041610101054', 920, 'CNY', '2025-04-16 11:09:15', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-16 10:39:15', '[{\"good_img\": \"http://mall.meloon.cn/img/5.jpg\", \"good_name\": \"适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s\", \"good_amount\": 230, \"good_number\": 4}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504163505872004', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025041610101053', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025041711101056', 230, 'CNY', '2025-04-17 11:37:02', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-17 11:07:02', '[{\"good_img\": \"http://mall.meloon.cn/img/5.jpg\", \"good_name\": \"适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s\", \"good_amount\": 230, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504173505900163', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025041711101055', 'mp-pay', NULL, 'oHhgC5Q21vLN4y2k6c6c9mucQ4Hs', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025041711101058', 3438, 'CNY', '2025-04-17 11:52:12', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-17 11:22:12', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 3}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504173505900166', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025041711101057', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025041711101060', 3438, 'CNY', '2025-04-17 11:55:09', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-17 11:25:09', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 3}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504173505900169', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025041711101059', 'mp-pay', NULL, 'oHhgC5W7ks5mOPVGSB2WNh5Itl1s', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025041711101062', 460, 'CNY', '2025-04-17 11:55:41', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-17 11:25:41', '[{\"good_img\": \"http://mall.meloon.cn/img/5.jpg\", \"good_name\": \"适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s\", \"good_amount\": 230, \"good_number\": 2}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504173505900170', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025041711101061', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025041711101064', 1146, 'CNY', '2025-04-17 11:56:35', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-17 11:26:35', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504173505900171', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025041711101063', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025042115101094', 100, 'CNY', '2025-04-21 15:31:45', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-21 15:26:45', '[{\"good_name\": \"标准商品\", \"good_amount\": 100, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504213505970075', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 111, 'CNY', '2025-04-22 09:59:54', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 09:54:54', '[{\"good_name\": \"标准商品\", \"good_amount\": 111, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0004\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980004', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 09:55:56', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 100, 'CNY', '2025-04-22 09:09:08', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 09:04:08', '[{\"good_name\": \"标准商品\", \"good_amount\": 100, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504223505972016', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 100, 'CNY', '2025-04-22 09:14:16', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 09:09:16', '[{\"good_name\": \"标准商品\", \"good_amount\": 100, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504223505972017', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025042209101097', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025042209101100', 1000, 'CNY', '2025-04-22 09:24:41', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 09:19:41', '[{\"good_name\": \"标准商品\", \"good_amount\": 1000, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504223505972021', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 100, 'CNY', '2025-04-22 10:09:22', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 10:04:22', '[{\"good_name\": \"标准商品\", \"good_amount\": 100, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0005\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980005', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 10:04:41', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 200, 'CNY', '2025-04-22 10:18:03', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 10:13:03', '[{\"good_name\": \"标准商品\", \"good_amount\": 200, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0006\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980006', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 10:13:21', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 1100, 'CNY', '2025-04-22 10:21:41', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 10:16:41', '[{\"good_name\": \"标准商品\", \"good_amount\": 1100, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0007\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980007', 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 10:16:57', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 1100, 'CNY', '2025-04-22 10:24:38', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 10:19:38', '[{\"good_name\": \"标准商品\", \"good_amount\": 1100, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0009\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980009', 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 10:19:56', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 3300, 'CNY', '2025-04-22 10:25:04', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 10:20:04', '[{\"good_name\": \"标准商品\", \"good_amount\": 3300, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980010', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 7700, 'CNY', '2025-04-22 11:45:34', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 11:40:34', '[{\"good_name\": \"标准商品\", \"good_amount\": 7700, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0032\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980032', 8, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 11:40:56', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 7700, 'CNY', '2025-04-22 11:47:06', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 11:42:06', '[{\"good_name\": \"标准商品\", \"good_amount\": 7700, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0033\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980033', 8, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 11:42:26', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 3300, 'CNY', '2025-04-22 11:53:34', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 11:48:34', '[{\"good_name\": \"标准商品\", \"good_amount\": 3300, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0037\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980037', 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 11:49:07', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 88800, 'CNY', '2025-04-22 14:05:03', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 14:00:03', '[{\"good_name\": \"标准商品\", \"good_amount\": 88800, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0059\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980059', 89, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 14:00:28', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 7400, 'CNY', '2025-04-22 14:07:40', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 14:02:40', '[{\"good_name\": \"标准商品\", \"good_amount\": 7400, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0061\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980061', 7, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 14:02:57', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 20000, 'CNY', '2025-04-22 14:25:07', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 14:20:07', '[{\"good_name\": \"标准商品\", \"good_amount\": 20000, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0062\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980062', 20, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 14:20:43', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 12300, 'CNY', '2025-04-22 14:26:42', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 14:21:42', '[{\"good_name\": \"标准商品\", \"good_amount\": 12300, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0065\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980065', 12, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 14:22:00', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 122, 'CNY', '2025-04-22 14:35:01', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 14:30:01', '[{\"good_name\": \"标准商品\", \"good_amount\": 122, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980068', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 1146, 'CNY', '2025-04-22 15:02:19', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 14:32:19', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980071', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025042214101093', 'mp-pay', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025042214101096', 125649, 'CNY', '2025-04-22 14:40:18', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 14:35:18', '[{\"good_name\": \"标准商品\", \"good_amount\": 125649, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980075', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025042214101095', 'mp-pay', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025042215101098', 15863, 'CNY', '2025-04-22 15:07:01', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 15:02:01', '[{\"good_name\": \"标准商品\", \"good_amount\": 15863, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980082', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 2174, 'CNY', '2025-04-22 15:07:39', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 15:02:39', '[{\"good_name\": \"标准商品\", \"good_amount\": 2174, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0083\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980083', 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 15:02:56', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 5846, 'CNY', '2025-04-22 15:08:39', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 15:03:39', '[{\"good_name\": \"标准商品\", \"good_amount\": 5846, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0084\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980084', 6, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 15:03:57', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 9900, 'CNY', '2025-04-22 15:19:28', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 15:14:28', '[{\"good_name\": \"标准商品\", \"good_amount\": 9900, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0089\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980089', 10, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-22 15:15:14', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 1367, 'CNY', '2025-04-22 15:55:44', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-22 15:50:44', '[{\"good_name\": \"标准商品\", \"good_amount\": 1367, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202504223505980096', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 100, 'CNY', '2025-04-30 14:37:36', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-30 14:32:36', '[{\"good_name\": \"标准商品\", \"good_amount\": 100, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0033\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504303506120033', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-30 14:33:29', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 7700, 'CNY', '2025-04-30 14:42:51', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-30 14:37:51', '[{\"good_name\": \"标准商品\", \"good_amount\": 7700, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0037\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504303506120037', 8, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-30 14:38:10', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 9900, 'CNY', '2025-04-30 15:20:38', NULL, NULL, '************', 1, '微企付测试商户', '2025-04-30 15:15:38', '[{\"good_name\": \"标准商品\", \"good_amount\": 9900, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0041\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202504303506120041', 10, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-04-30 15:15:59', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 14300, 'CNY', '2025-05-06 09:16:15', NULL, NULL, '************', 1, '微企付测试商户', '2025-05-06 09:11:15', '[{\"good_name\": \"标准商品\", \"good_amount\": 14300, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202505063506200014', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 6600, 'CNY', '2025-05-06 09:16:49', NULL, NULL, '************', 1, '微企付测试商户', '2025-05-06 09:11:49', '[{\"good_name\": \"标准商品\", \"good_amount\": 6600, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0015\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202505063506200015', 7, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-05-06 09:12:29', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 9900, 'CNY', '2025-05-06 10:16:24', NULL, NULL, '************', 1, '微企付测试商户', '2025-05-06 10:11:24', '[{\"good_name\": \"标准商品\", \"good_amount\": 9900, \"good_number\": 1}]', '{\"payerId\": \"ARUBAIojliHVhpD1PtIJQIGMFJ2VNu23O7en1KIgoBuoqNRNbrVKOQ\", \"payerAcctLast4\": \"0031\", \"payerBankSname\": \"BOSH\"}', NULL, 'NO_PROFIT_ALLOCATION', 'SUCCEEDED', '2025-06-05 15:22:47', 'APPTMS000100202505063506200031', 10, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', '2025-05-06 10:11:42', 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 1300, 'CNY', '2025-05-06 12:00:31', NULL, NULL, '************', 1, '微企付测试商户', '2025-05-06 11:55:31', '[{\"good_name\": \"标准商品\", \"good_amount\": 1300, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202505063506200092', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '****************', 'mp-pay', NULL, 'oHhgC5ctzTscyPAKPHOAn-w43HNI', 1);
INSERT INTO `merchant_pay_order` VALUES ('****************', 1146, 'CNY', '2025-05-09 17:31:01', NULL, NULL, '************', 1, '微企付测试商户', '2025-05-09 17:01:01', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202505093506230084', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025050917101121', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025050918101124', 5500, 'CNY', '2025-05-09 18:34:48', NULL, NULL, '************', 1, '微企付测试商户', '2025-05-09 18:29:48', '[{\"good_name\": \"标准商品\", \"good_amount\": 5500, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202505093506230100', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025050918101123', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025050918101126', 3300, 'CNY', '2025-05-09 18:36:05', NULL, NULL, '************', 1, '微企付测试商户', '2025-05-09 18:31:05', '[{\"good_name\": \"标准商品\", \"good_amount\": 3300, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'CLOSED', '2025-06-05 15:22:47', 'APPTMS000100202505093506230101', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025050918101125', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025052415101129', 1146, 'CNY', '2025-05-24 16:21:41', NULL, NULL, '************', 1, '微企付测试商户', '2025-05-24 15:51:41', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'PROCESSING', '2025-06-05 15:22:47', 'APPTMS000100202505243506490015', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025052415101128', 'mp-pay', NULL, 'oHhgC5Q21vLN4y2k6c6c9mucQ4Hs', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025053011101159', 1146, 'CNY', '2025-05-30 12:11:12', NULL, NULL, '************', 1, '微企付测试商户', '2025-05-30 11:41:12', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'PROCESSING', '2025-06-05 15:22:47', 'APPTMS000100202505303506590042', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025053011101158', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025053011101163', 1146, 'CNY', '2025-05-30 12:22:50', NULL, NULL, '************', 1, '微企付测试商户', '2025-05-30 11:52:50', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'PROCESSING', '2025-06-05 15:22:47', 'APPTMS000100202505303506590045', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025053011101162', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025060317101168', 5990, 'CNY', '2025-06-03 17:57:03', NULL, NULL, '************', 1, '微企付测试商户', '2025-06-03 17:27:03', '[{\"good_img\": \"http://mall.meloon.cn/img/2.jpg\", \"good_name\": \"适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜\", \"good_amount\": 599, \"good_number\": 10}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'PROCESSING', '2025-06-05 15:22:47', 'APPTMS000100202506033506660125', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025060317101167', 'mp-pay', NULL, 'oHhgC5Ztv6nnKcz0Xjv7SykOlsyk', 1);
INSERT INTO `merchant_pay_order` VALUES ('2025060508101173', 1146, 'CNY', '2025-06-05 09:10:07', NULL, NULL, '************', 1, '微企付测试商户', '2025-06-05 08:40:07', '[{\"good_img\": \"http://mall.meloon.cn/img/1.jpg\", \"good_name\": \"苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓\", \"good_amount\": 1146, \"good_number\": 1}]', NULL, NULL, 'NO_PROFIT_ALLOCATION', 'PROCESSING', '2025-06-05 15:22:47', 'APPTMS000100202506053506692014', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025************', 'mp-pay', NULL, 'oHhgC5bdeWcRlZNGYAV4J2vbAd6c', 1);

-- ----------------------------
-- Table structure for merchant_settlement_card
-- ----------------------------
DROP TABLE IF EXISTS `merchant_settlement_card`;
CREATE TABLE `merchant_settlement_card`  (
  `card_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '结算卡ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `card_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算卡名称',
  `bank_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算银行账号',
  `account_holder` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算银行开户人',
  `account_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算银行账户类型：corporate-对公账户，personal-对私账户',
  `card_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结算银行卡类型：debit-借记卡，unit-单位结算卡',
  `bank_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算银行开户行名称',
  `bank_category` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行行别，如：中国银行',
  `bank_province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户行所在省份',
  `bank_city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户行所在城市',
  `reserved_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行预留手机号',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否默认结算卡：0-否，1-是',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`card_id`) USING BTREE,
  INDEX `idx_merchant_id`(`merchant_id`) USING BTREE,
  INDEX `idx_is_default`(`is_default`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户结算卡表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of merchant_settlement_card
-- ----------------------------
INSERT INTO `merchant_settlement_card` VALUES (1, 1, '微企付测试商户默认结算卡', '待补充银行账号', '待补充开户人', 'corporate', NULL, '待补充开户行', '待补充银行类别', '待补充省份', '待补充城市', '待补充手机号', 1, 1, '2025-06-05 15:28:13', '2025-06-05 15:28:13', 'system', NULL);

-- ----------------------------
-- Table structure for ny_dept
-- ----------------------------
DROP TABLE IF EXISTS `ny_dept`;
CREATE TABLE `ny_dept`  (
  `dept_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '集团ID',
  `enable_flag` int(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `parent_id` int(11) NULL DEFAULT NULL COMMENT '父级集团ID',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `ancestors` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `group_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '集团名称',
  `contact_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Contact Name',
  `contact_mobile` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Contact Mobile',
  `contact_email` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Contact Email',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'PROCESSING' COMMENT '状态：PROCESSING-处理中，SUCCESS-成功，FAILED-失败',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `uid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '集团UID',
  `merchant_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `business_register_type` int(1) NULL DEFAULT 1,
  `unified_social_credit_code` int(1) NULL DEFAULT 1,
  `merchant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`dept_id`) USING BTREE,
  INDEX `ny_dept_uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '集团/企业表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ny_dept
-- ----------------------------
INSERT INTO `ny_dept` VALUES (1, 1, NULL, '', NULL, '', NULL, '0', '美诺科技', NULL, NULL, NULL, 'SUCCESS', NULL, NULL, NULL, 1, 1, NULL);
INSERT INTO `ny_dept` VALUES (9, 1, 1, 'admin', '2025-03-17 10:03:36', 'admin', '2025-06-10 14:47:17', '0,1', '明意湖集团', '曾文政', '***********', '<EMAIL>', 'SUCCESS', 'qq', 'f759d4e8d63b4e059e86f00dcd8f6738', '微企付测试商户', 1, 1, '************');

-- ----------------------------
-- Table structure for ny_menu
-- ----------------------------
DROP TABLE IF EXISTS `ny_menu`;
CREATE TABLE `ny_menu`  (
  `menu_id` int(11) NOT NULL AUTO_INCREMENT,
  `menu_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '菜单名',
  `parent_id` int(11) NULL DEFAULT NULL,
  `order_num` int(4) NULL DEFAULT NULL COMMENT '显示顺序',
  `path` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求地址',
  `component` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路由地址',
  `menu_type` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `perms` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `enable_flag` int(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4011 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ny_menu
-- ----------------------------
INSERT INTO `ny_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, 'M', NULL, 'system', 1, '', NULL, 'admin', '2025-03-17 09:51:11');
INSERT INTO `ny_menu` VALUES (100, '用户管理', 1, 2, 'user', 'system/user/index', 'C', 'system:user:list', 'user', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (101, '角色管理', 1, 3, 'role', 'system/role/index', 'C', 'system:role:list', 'peoples', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (102, '菜单管理', 1, 4, 'menu', 'system/menu/index', 'C', 'system:menu:list', 'tree-table', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (103, '集团管理', 1, 1, 'dept', 'system/dept/index', 'C', 'system:dept:list', 'tree', 1, '', NULL, 'admin', '2025-03-18 10:54:14');
INSERT INTO `ny_menu` VALUES (1000, '用户查询', 100, 1, NULL, NULL, 'F', 'system:user:query', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1001, '用户新增', 100, 2, NULL, NULL, 'F', 'system:user:add', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1002, '用户修改', 100, 3, NULL, NULL, 'F', 'system:user:edit', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1003, '用户删除', 100, 4, NULL, NULL, 'F', 'system:user:remove', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1006, '重置密码', 100, 5, NULL, NULL, 'F', 'system:user:resetPwd', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1007, '角色查询', 101, 1, NULL, NULL, 'F', 'system:role:query', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1008, '角色新增', 101, 2, NULL, NULL, 'F', 'system:role:add', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1009, '角色修改', 101, 3, NULL, NULL, 'F', 'system:role:edit', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1010, '角色删除', 101, 4, NULL, NULL, 'F', 'system:role:remove', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1012, '菜单查询', 102, 1, NULL, NULL, 'F', 'system:menu:query', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1013, '菜单新增', 102, 2, NULL, NULL, 'F', 'system:menu:add', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1014, '菜单修改', 102, 3, NULL, NULL, 'F', 'system:menu:edit', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1015, '菜单删除', 102, 4, NULL, NULL, 'F', 'system:menu:remove', '#', 1, '', NULL, '', NULL);
INSERT INTO `ny_menu` VALUES (1017, '企业新增', 103, 1, NULL, NULL, 'F', 'system:dept:query', '#', 1, '', NULL, 'admin', '2025-03-18 10:54:21');
INSERT INTO `ny_menu` VALUES (1018, '企业修改', 103, 2, NULL, NULL, 'F', 'system:dept:edit', '#', 1, '', NULL, 'admin', '2025-03-18 10:54:27');
INSERT INTO `ny_menu` VALUES (1019, '企业删除', 103, 3, NULL, NULL, 'F', 'system:dept:remove', '#', 1, '', NULL, 'admin', '2025-03-18 10:54:34');
INSERT INTO `ny_menu` VALUES (2039, '员工管理', 4001, 3, 'company/user', 'company/user/index', 'C', 'companyUser:list', 'user', 1, 'admin', '2025-03-17 15:47:34', 'admin', '2025-06-10 10:50:56');
INSERT INTO `ny_menu` VALUES (2048, '报表查询', 0, 6, 'report', NULL, 'M', NULL, 'chart', 1, 'admin', '2025-05-19 08:37:16', 'admin', '2025-06-10 11:44:50');
INSERT INTO `ny_menu` VALUES (2049, '客户管理', 4001, 4, 'company/partner', 'company/partner/index', 'C', 'partner:info:list', 'people', 1, 'admin', '2025-05-19 08:40:28', 'admin', '2025-06-10 11:40:29');
INSERT INTO `ny_menu` VALUES (2051, '支付统计', 2048, 2, 'report/salesCount', 'report/salesCount/index', 'C', 'report:payment:count', 'monitor', 1, 'admin', '2025-05-19 08:45:27', '', NULL);
INSERT INTO `ny_menu` VALUES (3001, '商户信息', 4004, 3, 'company/merchant', 'company/merchant/index', 'C', 'merchant:info:list', 'shop', 1, 'system', '2025-06-09 09:35:07', 'admin', '2025-06-10 10:53:57');
INSERT INTO `ny_menu` VALUES (3002, '支付渠道', 1, 5, 'company/channel', 'company/channel/index', 'M', 'merchant:channel:list', 'money', 1, 'system', '2025-06-09 09:35:07', 'admin', '2025-06-10 11:15:02');
INSERT INTO `ny_menu` VALUES (3003, '智慧路由策略', 1, 6, 'company/route', 'company/route/index', 'C', 'payment:route:list', 'guide', 1, 'system', '2025-06-09 09:35:07', 'admin', '2025-06-10 11:16:15');
INSERT INTO `ny_menu` VALUES (3011, '商户查询', 3001, 1, NULL, NULL, 'F', 'merchant:info:query', '#', 1, 'system', '2025-06-09 09:35:07', '', NULL);
INSERT INTO `ny_menu` VALUES (3012, '商户新增', 3001, 2, NULL, NULL, 'F', 'merchant:info:add', '#', 1, 'system', '2025-06-09 09:35:07', '', NULL);
INSERT INTO `ny_menu` VALUES (3013, '商户修改', 3001, 3, NULL, NULL, 'F', 'merchant:info:edit', '#', 1, 'system', '2025-06-09 09:35:07', '', NULL);
INSERT INTO `ny_menu` VALUES (3014, '商户删除', 3001, 4, NULL, NULL, 'F', 'merchant:info:remove', '#', 1, 'system', '2025-06-09 09:35:07', '', NULL);
INSERT INTO `ny_menu` VALUES (3015, '商户审核', 3001, 5, NULL, NULL, 'F', 'merchant:info:approve', '#', 1, 'system', '2025-06-09 09:35:07', '', NULL);
INSERT INTO `ny_menu` VALUES (4001, '集团管理', 0, 2, 'company', NULL, 'M', NULL, 'peoples', 1, 'admin', '2025-06-10 10:07:26', 'admin', '2025-06-10 11:40:59');
INSERT INTO `ny_menu` VALUES (4004, '商户管理', 0, 3, 'mch', NULL, 'M', NULL, 'server', 1, 'admin', '2025-06-10 10:53:07', '', NULL);
INSERT INTO `ny_menu` VALUES (4005, '商品管理', 0, 4, 'product', NULL, 'M', NULL, 'dict', 1, 'admin', '2025-06-10 11:25:01', '', NULL);
INSERT INTO `ny_menu` VALUES (4006, '商品类型', 4005, 1, 'product/category', 'product/category/index', 'C', 'product:category:list', 'date', 1, 'admin', '2025-06-10 11:26:08', '', NULL);
INSERT INTO `ny_menu` VALUES (4007, '销售管理', 0, 5, 'order', NULL, 'M', NULL, 'money', 1, 'admin', '2025-06-10 11:44:41', '', NULL);
INSERT INTO `ny_menu` VALUES (4008, '销售订单', 4007, 1, 'sales/order', 'sales/order/index', 'C', 'sales:order:list', 'shopping', 1, 'admin', '2025-06-10 11:48:01', 'admin', '2025-06-10 15:02:23');
INSERT INTO `ny_menu` VALUES (4009, '商品规格', 4005, 2, 'product/spec', 'product/spec/index', 'C', 'product:spec:list', 'checkbox', 1, 'admin', '2025-06-10 14:54:36', '', NULL);
INSERT INTO `ny_menu` VALUES (4010, '商品信息', 4005, 3, 'product/info', 'product/info/index', 'C', 'product:info:list', 'cascader', 1, 'admin', '2025-06-10 14:55:18', '', NULL);

-- ----------------------------
-- Table structure for ny_role
-- ----------------------------
DROP TABLE IF EXISTS `ny_role`;
CREATE TABLE `ny_role`  (
  `role_id` int(11) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `role_key` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `role_sort` int(4) NULL DEFAULT 0,
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `enable_flag` int(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `is_company` int(1) NULL DEFAULT 0 COMMENT '是否允许集团选择',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ny_role
-- ----------------------------
INSERT INTO `ny_role` VALUES (1, '超级管理员', 'admin', 1, '', '2023-07-06 11:00:51', 'admin', '2025-06-10 14:02:48', 1, 0);
INSERT INTO `ny_role` VALUES (4, '集团管理员', 'company_admin', 2, '', NULL, 'admin', '2025-06-10 14:55:40', 1, 1);
INSERT INTO `ny_role` VALUES (9, '集团财务', NULL, 3, 'admin', '2025-06-10 14:46:01', '', NULL, 1, 1);

-- ----------------------------
-- Table structure for ny_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `ny_role_menu`;
CREATE TABLE `ny_role_menu`  (
  `role_id` int(11) NOT NULL,
  `menu_id` int(11) NOT NULL,
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ny_role_menu
-- ----------------------------
INSERT INTO `ny_role_menu` VALUES (1, 1);
INSERT INTO `ny_role_menu` VALUES (1, 100);
INSERT INTO `ny_role_menu` VALUES (1, 101);
INSERT INTO `ny_role_menu` VALUES (1, 102);
INSERT INTO `ny_role_menu` VALUES (1, 103);
INSERT INTO `ny_role_menu` VALUES (1, 1000);
INSERT INTO `ny_role_menu` VALUES (1, 1001);
INSERT INTO `ny_role_menu` VALUES (1, 1002);
INSERT INTO `ny_role_menu` VALUES (1, 1003);
INSERT INTO `ny_role_menu` VALUES (1, 1006);
INSERT INTO `ny_role_menu` VALUES (1, 1007);
INSERT INTO `ny_role_menu` VALUES (1, 1008);
INSERT INTO `ny_role_menu` VALUES (1, 1009);
INSERT INTO `ny_role_menu` VALUES (1, 1010);
INSERT INTO `ny_role_menu` VALUES (1, 1012);
INSERT INTO `ny_role_menu` VALUES (1, 1013);
INSERT INTO `ny_role_menu` VALUES (1, 1014);
INSERT INTO `ny_role_menu` VALUES (1, 1015);
INSERT INTO `ny_role_menu` VALUES (1, 1017);
INSERT INTO `ny_role_menu` VALUES (1, 1018);
INSERT INTO `ny_role_menu` VALUES (1, 1019);
INSERT INTO `ny_role_menu` VALUES (1, 2039);
INSERT INTO `ny_role_menu` VALUES (1, 2048);
INSERT INTO `ny_role_menu` VALUES (1, 2049);
INSERT INTO `ny_role_menu` VALUES (1, 2051);
INSERT INTO `ny_role_menu` VALUES (1, 3001);
INSERT INTO `ny_role_menu` VALUES (1, 3002);
INSERT INTO `ny_role_menu` VALUES (1, 3003);
INSERT INTO `ny_role_menu` VALUES (1, 3011);
INSERT INTO `ny_role_menu` VALUES (1, 3012);
INSERT INTO `ny_role_menu` VALUES (1, 3013);
INSERT INTO `ny_role_menu` VALUES (1, 3014);
INSERT INTO `ny_role_menu` VALUES (1, 3015);
INSERT INTO `ny_role_menu` VALUES (1, 4001);
INSERT INTO `ny_role_menu` VALUES (1, 4004);
INSERT INTO `ny_role_menu` VALUES (1, 4005);
INSERT INTO `ny_role_menu` VALUES (1, 4006);
INSERT INTO `ny_role_menu` VALUES (1, 4007);
INSERT INTO `ny_role_menu` VALUES (1, 4008);
INSERT INTO `ny_role_menu` VALUES (4, 2039);
INSERT INTO `ny_role_menu` VALUES (4, 2048);
INSERT INTO `ny_role_menu` VALUES (4, 2049);
INSERT INTO `ny_role_menu` VALUES (4, 2051);
INSERT INTO `ny_role_menu` VALUES (4, 3001);
INSERT INTO `ny_role_menu` VALUES (4, 4001);
INSERT INTO `ny_role_menu` VALUES (4, 4004);
INSERT INTO `ny_role_menu` VALUES (4, 4005);
INSERT INTO `ny_role_menu` VALUES (4, 4006);
INSERT INTO `ny_role_menu` VALUES (4, 4007);
INSERT INTO `ny_role_menu` VALUES (4, 4008);
INSERT INTO `ny_role_menu` VALUES (4, 4009);
INSERT INTO `ny_role_menu` VALUES (4, 4010);
INSERT INTO `ny_role_menu` VALUES (9, 2048);
INSERT INTO `ny_role_menu` VALUES (9, 2049);
INSERT INTO `ny_role_menu` VALUES (9, 2051);
INSERT INTO `ny_role_menu` VALUES (9, 4001);

-- ----------------------------
-- Table structure for ny_user
-- ----------------------------
DROP TABLE IF EXISTS `ny_user`;
CREATE TABLE `ny_user`  (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `dept_id` int(11) NULL DEFAULT NULL,
  `is_dept_admin` int(1) NULL DEFAULT 0 COMMENT '是否为集团管理员(1-是, 0-否)',
  `user_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `nick_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `password` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `enable_flag` int(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ny_user
-- ----------------------------
INSERT INTO `ny_user` VALUES (1, 1, 0, 'admin', '系统管理员', '$2a$10$fvXApvgu68YdKSGI3tvO9OmsrkrJ1v3ZcldfU5XgbWuMYB3JCwUHS', 1, '', '2023-07-03 16:51:58', '', '2023-07-03 16:52:02');
INSERT INTO `ny_user` VALUES (2, 9, 1, 'mingyihu', '明意湖', '$2a$10$2HAQz.2o0ztG3A.SRh7.zuplwc6jfj1QzALVbwiDoHmz4WyWeSzYi', 1, '', '2023-07-06 11:34:03', 'zz', '2025-03-18 15:12:26');

-- ----------------------------
-- Table structure for ny_user_role
-- ----------------------------
DROP TABLE IF EXISTS `ny_user_role`;
CREATE TABLE `ny_user_role`  (
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ny_user_role
-- ----------------------------
INSERT INTO `ny_user_role` VALUES (1, 1);
INSERT INTO `ny_user_role` VALUES (2, 4);

-- ----------------------------
-- Table structure for partner_info
-- ----------------------------
DROP TABLE IF EXISTS `partner_info`;
CREATE TABLE `partner_info`  (
  `partner_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '合作伙伴ID',
  `dept_id` int(11) NOT NULL COMMENT '所属机构ID',
  `partner_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '合作伙伴名称',
  `partner_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作伙伴编码',
  `partner_type` tinyint(1) NOT NULL COMMENT '类型：1-供应商，2-客户',
  `contact_person` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `tax_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税号',
  `bank_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行账号',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`partner_id`) USING BTREE,
  INDEX `idx_dept_id`(`dept_id`) USING BTREE,
  INDEX `idx_partner_name`(`partner_name`) USING BTREE,
  INDEX `idx_partner_code`(`partner_code`) USING BTREE,
  INDEX `idx_partner_type`(`partner_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商/客户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of partner_info
-- ----------------------------
INSERT INTO `partner_info` VALUES (1, 9, '曾文政', '123123', 2, '曾文政', '***********', '<EMAIL>', '长沙市望城区时代倾城', '123123', '建行', '6211234565412', 1, '这是客户的备注', '2025-05-19 09:30:10', '2025-05-19 09:31:18', 'mingyihu', 'mingyihu');
INSERT INTO `partner_info` VALUES (2, 9, '张三', NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, '2025-05-19 09:35:37', '2025-05-19 09:34:52', 'mingyihu', NULL);
INSERT INTO `partner_info` VALUES (3, 9, '王五刀锋旗舰店', 'C0001', 2, '大刀', '18912345678', '<EMAIL>', NULL, NULL, NULL, NULL, 1, NULL, '2025-05-29 10:03:13', '2025-05-29 10:05:54', 'mingyihu', 'mingyihu');
INSERT INTO `partner_info` VALUES (4, 9, '李四', NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, '2025-05-30 10:02:08', '2025-05-30 10:01:24', 'mingyihu', NULL);
INSERT INTO `partner_info` VALUES (5, 9, '问问', NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, '2025-06-10 15:20:50', '2025-06-10 15:19:59', 'mingyihu', NULL);
INSERT INTO `partner_info` VALUES (6, 9, '微信小程序', NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, '2025-06-13 09:45:52', '2025-06-13 09:45:02', 'mingyihu', NULL);

-- ----------------------------
-- Table structure for payment_channel
-- ----------------------------
DROP TABLE IF EXISTS `payment_channel`;
CREATE TABLE `payment_channel`  (
  `channel_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '渠道ID',
  `channel_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道编码(WECHAT_PAY,ALIPAY,WEIQIFU,UNIONPAY等)',
  `channel_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道名称',
  `channel_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道类型(WECHAT,ALIPAY,BANK,THIRD_PARTY)',
  `api_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'API接口地址',
  `merchant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台商户号',
  `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用ID',
  `app_secret` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用密钥',
  `private_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '私钥',
  `public_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '公钥',
  `cert_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证书路径',
  `fee_rate` decimal(6, 4) NULL DEFAULT 0.0060 COMMENT '手续费率',
  `min_amount` bigint(20) NULL DEFAULT 1 COMMENT '最小金额(分)',
  `max_amount` bigint(20) NULL DEFAULT ********* COMMENT '最大金额(分)',
  `daily_limit` bigint(20) NULL DEFAULT NULL COMMENT '日限额(分)',
  `monthly_limit` bigint(20) NULL DEFAULT NULL COMMENT '月限额(分)',
  `support_scenes` json NULL COMMENT '支持场景JSON[\"PC_WEB\",\"MOBILE_WEB\",\"WECHAT_SCAN\",\"APP\"]',
  `status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'ACTIVE' COMMENT '状态(ACTIVE-启用,INACTIVE-禁用,MAINTENANCE-维护)',
  `risk_level` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'LOW' COMMENT '风险等级(LOW,MEDIUM,HIGH)',
  `priority` int(3) NULL DEFAULT 50 COMMENT '优先级(数字越小优先级越高)',
  `config_json` json NULL COMMENT '扩展配置JSON',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`channel_id`) USING BTREE,
  UNIQUE INDEX `uk_channel_code`(`channel_code`) USING BTREE,
  INDEX `idx_channel_type`(`channel_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付渠道基础信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of payment_channel
-- ----------------------------
INSERT INTO `payment_channel` VALUES (1, 'WECHAT_PAY', '微信支付', 'WECHAT', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0.0060, 1, *********, NULL, NULL, '[\"PC_WEB\", \"MOBILE_WEB\", \"WECHAT_SCAN\", \"APP\", \"MINI_PROGRAM\"]', 'ACTIVE', 'LOW', 10, NULL, '2025-06-09 09:35:07', '2025-06-09 09:35:07', 'system', NULL);
INSERT INTO `payment_channel` VALUES (2, 'ALIPAY', '支付宝', 'ALIPAY', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0.0060, 1, *********, NULL, NULL, '[\"PC_WEB\", \"MOBILE_WEB\", \"ALIPAY_SCAN\", \"APP\", \"MINI_PROGRAM\"]', 'ACTIVE', 'LOW', 20, NULL, '2025-06-09 09:35:07', '2025-06-09 09:35:07', 'system', NULL);
INSERT INTO `payment_channel` VALUES (3, 'WEIQIFU', '微企付', 'THIRD_PARTY', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0.0038, 1, *********, NULL, NULL, '[\"PC_WEB\", \"MOBILE_WEB\", \"WECHAT_SCAN\", \"APP\", \"MINI_PROGRAM\"]', 'ACTIVE', 'LOW', 5, NULL, '2025-06-09 09:35:07', '2025-06-09 09:35:07', 'system', NULL);
INSERT INTO `payment_channel` VALUES (4, 'UNIONPAY', '银联支付', 'BANK', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0.0050, 1, *********, NULL, NULL, '[\"PC_WEB\", \"MOBILE_WEB\", \"UNIONPAY_SCAN\", \"APP\"]', 'ACTIVE', 'LOW', 30, NULL, '2025-06-09 09:35:07', '2025-06-09 09:35:07', 'system', NULL);
INSERT INTO `payment_channel` VALUES (5, 'YINSHENG', '银盛支付', 'THIRD_PARTY', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0.0055, 1, *********, NULL, NULL, '[\"PC_WEB\", \"MOBILE_WEB\", \"WECHAT_SCAN\", \"ALIPAY_SCAN\"]', 'ACTIVE', 'LOW', 40, NULL, '2025-06-09 09:35:07', '2025-06-09 09:35:07', 'system', NULL);
INSERT INTO `payment_channel` VALUES (6, 'DOUYIN_PAY', '抖音支付', 'THIRD_PARTY', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0.0060, 1, *********, NULL, NULL, '[\"MINI_PROGRAM\", \"APP\"]', 'ACTIVE', 'LOW', 50, NULL, '2025-06-09 09:35:07', '2025-06-09 09:35:07', 'system', NULL);

-- ----------------------------
-- Table structure for product_category
-- ----------------------------
DROP TABLE IF EXISTS `product_category`;
CREATE TABLE `product_category`  (
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父分类ID',
  `category_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `sort_order` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
  `dept_id` int(11) NOT NULL COMMENT '所属机构',
  `ancestors` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  PRIMARY KEY (`category_id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of product_category
-- ----------------------------
INSERT INTO `product_category` VALUES (1, 0, '推荐', 1, NULL, 1, '2025-04-08 10:16:35', '2025-04-08 10:16:38', NULL, NULL, 0, '');
INSERT INTO `product_category` VALUES (9989, 0, '苹果', 6, '', 1, '2025-05-15 10:24:20', '2025-06-17 09:41:43', 'mingyihu', 'mingyihu', 9, '');
INSERT INTO `product_category` VALUES (9990, 0, '华为', 1, '', 1, '2025-05-15 10:26:38', '2025-05-15 10:26:49', 'mingyihu', 'mingyihu', 9, '');
INSERT INTO `product_category` VALUES (9991, 0, '小米', 3, '', 1, '2025-05-15 10:27:03', '2025-05-15 10:26:19', 'mingyihu', NULL, 9, '');
INSERT INTO `product_category` VALUES (9992, 9989, 'iPhone', 1, '', 1, '2025-05-15 10:28:31', '2025-05-15 17:06:19', 'mingyihu', NULL, 9, '0,9989');
INSERT INTO `product_category` VALUES (9993, 9989, 'iPad', 2, '', 1, '2025-05-15 10:28:45', '2025-05-15 17:06:21', 'mingyihu', NULL, 9, '0,9989');
INSERT INTO `product_category` VALUES (13703, 0, 'oppo', 4, '', 1, '2025-05-30 09:13:43', '2025-05-30 09:12:59', 'mingyihu', NULL, 9, '');
INSERT INTO `product_category` VALUES (13704, 0, 'vivo', 5, '', 1, '2025-05-30 09:13:52', '2025-05-30 09:13:09', 'mingyihu', NULL, 9, '');
INSERT INTO `product_category` VALUES (13705, 0, '明意湖', 0, '', 1, '2025-06-16 11:34:27', '2025-06-17 08:55:35', 'mingyihu', 'mingyihu', 9, '');

-- ----------------------------
-- Table structure for product_info
-- ----------------------------
DROP TABLE IF EXISTS `product_info`;
CREATE TABLE `product_info`  (
  `product_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `dept_id` int(11) NOT NULL COMMENT '商户ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `product_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `product_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_brief` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品简介',
  `product_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品详情',
  `main_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品主图',
  `sub_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品子图JSON',
  `spec_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '规格数据JSON',
  `price` bigint(20) NOT NULL DEFAULT 0 COMMENT '商品价格(分)',
  `market_price` bigint(20) NULL DEFAULT NULL COMMENT '市场价格(分)',
  `cost_price` bigint(20) NULL DEFAULT NULL COMMENT '成本价格(分)',
  `stock` int(11) NOT NULL DEFAULT 0 COMMENT '库存数量',
  `sales` int(11) NULL DEFAULT 0 COMMENT '销量',
  `unit` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位',
  `weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '重量(kg)',
  `has_spec` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有规格：0-无，1-有',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0-下架，1-上架',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_recommend` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐：0-否，1-是',
  `is_new` tinyint(1) NULL DEFAULT 0 COMMENT '是否新品：0-否，1-是',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热销：0-否，1-是',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`product_id`) USING BTREE,
  INDEX `idx_merchant_id`(`dept_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_product_name`(`product_name`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of product_info
-- ----------------------------
INSERT INTO `product_info` VALUES (1, 9, 9992, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', 'http://mall.meloon.cn/img/1.jpg', 'http://mall.meloon.cn/img/11.jpg', '', 1146, NULL, NULL, 977, 125, '套', NULL, 1, 1, 5, 0, 0, 0, '2025-04-08 10:31:10', '2025-06-16 14:42:31', NULL, 'mingyihu');
INSERT INTO `product_info` VALUES (2, 9, 9990, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', 'http://mall.meloon.cn/img/2.jpg', 'http://mall.meloon.cn/img/22.jpg', '', 599, NULL, NULL, 981, 130, '套', NULL, 0, 1, 2, 0, 0, 0, '2025-04-08 10:44:34', '2025-05-16 12:59:01', NULL, 'mingyihu');
INSERT INTO `product_info` VALUES (3, 9, 9990, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '适用华为mate60pro手机膜mate60水凝膜mate60pro', '适用华为mate60pro手机膜mate60水凝膜mate60pro', 'http://mall.meloon.cn/img/3.jpg', 'http://mall.meloon.cn/img/33.jpg', '', 7500, NULL, NULL, 994, 41, '套', NULL, 0, 1, 3, 0, 0, 0, '2025-04-08 10:48:00', '2025-05-16 12:59:28', NULL, 'mingyihu');
INSERT INTO `product_info` VALUES (4, 9, 9990, '适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s', NULL, '适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s', '适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s', 'http://mall.meloon.cn/img/5.jpg', 'http://mall.meloon.cn/img/55.jpg', '', 230, NULL, NULL, 1978, 367, '套', NULL, 0, 1, 4, 0, 0, 0, '2025-04-08 14:12:16', '2025-05-16 13:09:52', NULL, 'mingyihu');
INSERT INTO `product_info` VALUES (5, 9, 9991, '信弘达 适用小米14钢化膜xiaomi 13手机膜无孔无黑边电镀防指纹', '33332', '信弘达 适用小米14钢化膜xiaomi 13手机膜无孔无黑边电镀防指纹', '信弘达 适用小米14钢化膜xiaomi 13手机膜无孔无黑边电镀防指纹', 'http://mall.meloon.cn/img/6.jpg', 'http://mall.meloon.cn/img/66.jpg', '', 368, NULL, NULL, 2000, 111, '套', NULL, 0, 1, 5, 0, 0, 0, '2025-04-11 14:40:26', '2025-05-16 13:03:36', NULL, 'mingyihu');
INSERT INTO `product_info` VALUES (6, 9, 9990, '适用华为钢化膜mate60P70nova12畅享70大猩猩电镀全屏P70荣耀50SE', NULL, '适用华为钢化膜mate60P70nova12畅享70大猩猩电镀全屏P70荣耀50SE', '适用华为钢化膜mate60P70nova12畅享70大猩猩电镀全屏P70荣耀50SE', 'http://mall.meloon.cn/img/7.jpg', 'http://mall.meloon.cn/img/77.jpg', '', 250, NULL, NULL, 3333, 444, '套', NULL, 0, 1, 6, 0, 0, 0, '2025-04-11 14:43:41', '2025-05-16 13:09:26', NULL, 'mingyihu');
INSERT INTO `product_info` VALUES (8, 9, 13703, '适用OPPO Xfold3钢化膜', '77775555', '适用OPPO Xfold3钢化膜', NULL, NULL, NULL, NULL, 0, NULL, NULL, 0, 0, '件', 0.00, 0, 1, 7, 0, 0, 0, '2025-05-30 10:07:33', '2025-05-30 10:08:47', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (9, 9, 13705, '白底黑字防窥', 'M0001', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1640, NULL, NULL, 66643, 0, '件', 0.00, 0, 1, 1, 0, 0, 0, '2025-06-16 14:12:30', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (10, 9, 13705, 'ESD SUPER X', 'M0002', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1299, NULL, NULL, 444480, 30200, '件', 0.00, 0, 1, 1, 0, 0, 0, '2025-06-16 14:41:22', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (11, 9, 13705, 'L001防窥', 'M0003', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 2350, NULL, NULL, 310625, 23650, '件', 0.00, 0, 1, 1, 0, 0, 0, '2025-06-16 14:42:04', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (12, 9, 13705, '21D', 'M0004', NULL, NULL, 'http://mall.meloon.cn/img/0a89c76f41884421a0bc257943a91b51.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 2069, NULL, NULL, 179050, 15, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:43:24', '2025-06-20 10:59:02', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (13, 9, 13705, 'ESD防窥胶', 'M0005', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1519, NULL, NULL, 101000, 13, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:44:35', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (14, 9, 13705, '定制防窥', 'M0006', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1369, NULL, NULL, 99000, 1, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:45:19', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (15, 9, 13705, 'NEW GO ESD', 'M0007', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1599, NULL, NULL, 509370, 5, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:46:30', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (16, 9, 13705, '鳄鱼定制防窥', 'M0008', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1478, NULL, NULL, 0, 0, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:46:55', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (17, 9, 13705, 'D立方高铝防窥', 'M0009', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1329, NULL, NULL, 41000, 7, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:47:21', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (18, 9, 13705, 'MLIAKE高铝防窥', 'M0010', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 0, NULL, NULL, 0, 0, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:48:19', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (23, 9, 13705, '中铝定制-帽子', 'M0015', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1488, NULL, NULL, 3170, 3, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:49:55', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (25, 9, 13705, '9D', 'M0017', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1399, NULL, NULL, 37000, 5, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:50:34', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (26, 9, 13705, '定制 SAVIOUR', 'M0018', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1979, NULL, NULL, 53000, 8, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:50:51', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (27, 9, 13705, '定制TAKICO', 'M0019', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1849, NULL, NULL, 199000, 9, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:51:13', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');
INSERT INTO `product_info` VALUES (28, 9, 13705, '定制AUTSTATIC', 'M0020', NULL, NULL, 'http://mall.meloon.cn/img/myh.jpg', 'http://mall.meloon.cn/img/myh-1.jpg', NULL, 1689, NULL, NULL, 205470, 18, '件', 0.00, 0, 1, 0, 0, 0, 0, '2025-06-16 14:51:27', '2025-06-17 09:39:11', 'mingyihu', 'mingyihu');

-- ----------------------------
-- Table structure for product_sku
-- ----------------------------
DROP TABLE IF EXISTS `product_sku`;
CREATE TABLE `product_sku`  (
  `sku_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'SKU ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `sku_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SKU编码',
  `spec_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格数据JSON',
  `price` bigint(20) NOT NULL COMMENT '价格(分)',
  `market_price` bigint(20) NULL DEFAULT NULL COMMENT '市场价格(分)',
  `cost_price` bigint(20) NULL DEFAULT NULL COMMENT '成本价格(分)',
  `stock` int(11) NOT NULL DEFAULT 0 COMMENT '库存数量',
  `sales` int(11) NULL DEFAULT 0 COMMENT '销量',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SKU图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`sku_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_sku_code`(`sku_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 79 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品SKU表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of product_sku
-- ----------------------------
INSERT INTO `product_sku` VALUES (1, 1, '1234567-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', 1100, 0, 600, 100, 0, NULL, 1, '2025-05-16 11:55:07', '2025-06-16 14:42:31');
INSERT INTO `product_sku` VALUES (2, 1, '1234567-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', 1000, 0, 500, 100, 0, NULL, 1, '2025-05-16 11:55:07', '2025-06-16 14:42:31');
INSERT INTO `product_sku` VALUES (3, 1, '1234567-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', 900, 0, 500, 100, 0, NULL, 1, '2025-05-16 11:55:07', '2025-06-16 14:42:31');
INSERT INTO `product_sku` VALUES (4, 1, '1234567-4', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"15\"}]', 800, 0, 500, 100, 0, NULL, 1, '2025-05-16 11:55:07', '2025-06-16 14:42:31');
INSERT INTO `product_sku` VALUES (5, 1, '1234567-5', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"15\"}]', 1000, 0, 500, 100, 0, NULL, 1, '2025-05-16 11:55:07', '2025-06-16 14:42:31');
INSERT INTO `product_sku` VALUES (6, 1, '1234567-6', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"15\"}]', 1000, 0, 500, 100, 0, NULL, 1, '2025-05-16 11:55:07', '2025-06-16 14:42:31');
INSERT INTO `product_sku` VALUES (7, 1, '1234567-7', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"16\"}]', 1000, 0, 500, 100, 0, NULL, 1, '2025-05-16 11:55:07', '2025-06-16 14:42:31');
INSERT INTO `product_sku` VALUES (8, 1, '1234567-8', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"16\"}]', 1000, 0, 500, 100, 0, NULL, 1, '2025-05-16 11:55:07', '2025-06-16 14:42:31');
INSERT INTO `product_sku` VALUES (10, 2, '33321231-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:01', '2025-05-16 12:58:16');
INSERT INTO `product_sku` VALUES (11, 2, '33321231-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:01', '2025-05-16 12:58:16');
INSERT INTO `product_sku` VALUES (12, 2, '33321231-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:01', '2025-05-16 12:58:16');
INSERT INTO `product_sku` VALUES (13, 2, '33321231-4', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:01', '2025-05-16 12:58:16');
INSERT INTO `product_sku` VALUES (14, 2, '33321231-5', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:01', '2025-05-16 12:58:16');
INSERT INTO `product_sku` VALUES (15, 2, '33321231-6', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:01', '2025-05-16 12:58:16');
INSERT INTO `product_sku` VALUES (16, 2, '33321231-7', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:01', '2025-05-16 12:58:16');
INSERT INTO `product_sku` VALUES (17, 2, '33321231-8', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:01', '2025-05-16 12:58:16');
INSERT INTO `product_sku` VALUES (18, 2, '33321231-9', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:01', '2025-05-16 12:58:16');
INSERT INTO `product_sku` VALUES (19, 3, '314512-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:28', '2025-05-16 12:58:43');
INSERT INTO `product_sku` VALUES (20, 3, '314512-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:28', '2025-05-16 12:58:43');
INSERT INTO `product_sku` VALUES (21, 3, '314512-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:28', '2025-05-16 12:58:43');
INSERT INTO `product_sku` VALUES (22, 3, '314512-4', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:28', '2025-05-16 12:58:43');
INSERT INTO `product_sku` VALUES (23, 3, '314512-5', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:28', '2025-05-16 12:58:43');
INSERT INTO `product_sku` VALUES (24, 3, '314512-6', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:28', '2025-05-16 12:58:43');
INSERT INTO `product_sku` VALUES (25, 3, '314512-7', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:28', '2025-05-16 12:58:43');
INSERT INTO `product_sku` VALUES (26, 3, '314512-8', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:28', '2025-05-16 12:58:43');
INSERT INTO `product_sku` VALUES (27, 3, '314512-9', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', 990, NULL, 500, 100, 0, NULL, 1, '2025-05-16 12:59:28', '2025-05-16 12:58:43');
INSERT INTO `product_sku` VALUES (34, 5, '33332-1', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米13\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"}]', 800, NULL, 400, 99, 0, NULL, 1, '2025-05-16 13:03:37', '2025-05-16 13:02:51');
INSERT INTO `product_sku` VALUES (35, 5, '33332-2', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米13\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"}]', 800, NULL, 400, 99, 0, NULL, 1, '2025-05-16 13:03:37', '2025-05-16 13:02:51');
INSERT INTO `product_sku` VALUES (36, 5, '33332-3', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米13\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"}]', 800, NULL, 400, 99, 0, NULL, 1, '2025-05-16 13:03:37', '2025-05-16 13:02:51');
INSERT INTO `product_sku` VALUES (37, 5, '33332-4', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米14\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"}]', 800, NULL, 400, 99, 0, NULL, 1, '2025-05-16 13:03:37', '2025-05-16 13:02:51');
INSERT INTO `product_sku` VALUES (38, 5, '33332-5', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米14\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"}]', 800, NULL, 400, 99, 0, NULL, 1, '2025-05-16 13:03:37', '2025-05-16 13:02:51');
INSERT INTO `product_sku` VALUES (39, 5, '33332-6', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米14\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"}]', 800, NULL, 400, 99, 0, NULL, 1, '2025-05-16 13:03:37', '2025-05-16 13:02:51');
INSERT INTO `product_sku` VALUES (40, 5, '33332-7', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米15\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"}]', 800, NULL, 400, 99, 0, NULL, 1, '2025-05-16 13:03:37', '2025-05-16 13:02:51');
INSERT INTO `product_sku` VALUES (41, 5, '33332-8', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米15\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"}]', 800, NULL, 400, 99, 0, NULL, 1, '2025-05-16 13:03:37', '2025-05-16 13:02:51');
INSERT INTO `product_sku` VALUES (42, 5, '33332-9', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米15\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"}]', 800, NULL, 400, 99, 0, NULL, 1, '2025-05-16 13:03:37', '2025-05-16 13:02:51');
INSERT INTO `product_sku` VALUES (43, 6, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:27', '2025-05-16 13:08:41');
INSERT INTO `product_sku` VALUES (44, 6, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:27', '2025-05-16 13:08:41');
INSERT INTO `product_sku` VALUES (45, 6, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:27', '2025-05-16 13:08:41');
INSERT INTO `product_sku` VALUES (46, 6, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:27', '2025-05-16 13:08:41');
INSERT INTO `product_sku` VALUES (47, 6, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:27', '2025-05-16 13:08:41');
INSERT INTO `product_sku` VALUES (48, 6, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:27', '2025-05-16 13:08:41');
INSERT INTO `product_sku` VALUES (49, 6, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:27', '2025-05-16 13:08:41');
INSERT INTO `product_sku` VALUES (50, 6, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:27', '2025-05-16 13:08:41');
INSERT INTO `product_sku` VALUES (51, 6, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:27', '2025-05-16 13:08:41');
INSERT INTO `product_sku` VALUES (52, 4, 'SKU186901', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":6,\"specName\":\"vivo型号\",\"value\":\"vivos15\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:52', '2025-05-16 13:09:06');
INSERT INTO `product_sku` VALUES (53, 4, 'SKU186901', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":6,\"specName\":\"vivo型号\",\"value\":\"vivos15\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:52', '2025-05-16 13:09:06');
INSERT INTO `product_sku` VALUES (54, 4, 'SKU186901', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":6,\"specName\":\"vivo型号\",\"value\":\"vivos15\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:52', '2025-05-16 13:09:06');
INSERT INTO `product_sku` VALUES (55, 4, 'SKU186901', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":6,\"specName\":\"vivo型号\",\"value\":\"vivos16\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:52', '2025-05-16 13:09:07');
INSERT INTO `product_sku` VALUES (56, 4, 'SKU186901', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":6,\"specName\":\"vivo型号\",\"value\":\"vivos16\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:52', '2025-05-16 13:09:07');
INSERT INTO `product_sku` VALUES (57, 4, 'SKU186901', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":6,\"specName\":\"vivo型号\",\"value\":\"vivos16\"}]', 800, NULL, 500, 10, 0, NULL, 1, '2025-05-16 13:09:52', '2025-05-16 13:09:07');
INSERT INTO `product_sku` VALUES (58, 8, '77775555-F', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"findX6\"}]', 800, NULL, 0, 0, 0, NULL, 1, '2025-05-30 10:08:47', '2025-05-30 10:08:03');
INSERT INTO `product_sku` VALUES (59, 8, '77775555-F', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"findX6pro\"}]', 800, NULL, 0, 0, 0, NULL, 1, '2025-05-30 10:08:47', '2025-05-30 10:08:03');
INSERT INTO `product_sku` VALUES (60, 8, '77775555-R', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"reno6pro\"}]', 800, NULL, 0, 0, 0, NULL, 1, '2025-05-30 10:08:47', '2025-05-30 10:08:03');
INSERT INTO `product_sku` VALUES (61, 8, '77775555-R', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"reno4pro\"}]', 800, NULL, 0, 0, 0, NULL, 1, '2025-05-30 10:08:47', '2025-05-30 10:08:03');
INSERT INTO `product_sku` VALUES (62, 9, 'SKU383147', '[{\"specId\":13,\"specName\":\"白底黑字防窥\",\"value\":\"白底黑字防窥\"}]', 2260, 0, 100, 66643, 0, NULL, 1, '2025-06-16 14:13:23', '2025-06-16 17:22:41');
INSERT INTO `product_sku` VALUES (63, 12, 'M0004-2', '[{\"specId\":9,\"specName\":\"21D\",\"value\":\"21D\"}]', 3000, 0, 1000, 179050, 0, NULL, 1, '2025-06-16 15:22:50', '2025-06-20 10:59:02');
INSERT INTO `product_sku` VALUES (64, 13, 'M0005-E', '[{\"specId\":12,\"specName\":\"ESD防窥胶\",\"value\":\"ESD防窥胶\"}]', 3400, 0, 0, 101000, 0, NULL, 1, '2025-06-16 15:23:04', '2025-06-16 17:12:19');
INSERT INTO `product_sku` VALUES (65, 14, 'M0006-定', '[{\"specId\":14,\"specName\":\"定制防窥\",\"value\":\"定制防窥\"}]', 2100, 0, 0, 99000, 0, NULL, 1, '2025-06-16 15:23:14', '2025-06-16 17:12:34');
INSERT INTO `product_sku` VALUES (66, 15, 'M0007-NGE', '[{\"specId\":15,\"specName\":\"NEW GO ESD\",\"value\":\"NEW GO ESD\"}]', 2600, 0, 0, 509370, 0, NULL, 1, '2025-06-16 15:23:27', '2025-06-16 17:12:54');
INSERT INTO `product_sku` VALUES (67, 16, 'M0008-鳄', '[{\"specId\":16,\"specName\":\"鳄鱼定制防窥\",\"value\":\"鳄鱼定制防窥\"}]', 2700, NULL, 0, 0, 0, NULL, 1, '2025-06-16 15:23:39', '2025-06-16 15:38:34');
INSERT INTO `product_sku` VALUES (68, 17, 'M0009-D', '[{\"specId\":17,\"specName\":\"D立方高铝防窥\",\"value\":\"D立方高铝防窥\"}]', 2300, 0, 0, 41000, 0, NULL, 1, '2025-06-16 15:24:04', '2025-06-16 17:13:41');
INSERT INTO `product_sku` VALUES (69, 19, 'M0011-D', '[{\"specId\":17,\"specName\":\"D立方高铝防窥\",\"value\":\"D立方高铝防窥\"}]', 0, NULL, 0, 0, 0, NULL, 1, '2025-06-16 17:16:05', '2025-06-16 17:15:15');
INSERT INTO `product_sku` VALUES (70, 20, 'M0012-定', '[{\"specId\":14,\"specName\":\"定制防窥\",\"value\":\"定制防窥\"}]', 0, NULL, 0, 0, 0, NULL, 1, '2025-06-16 17:16:35', '2025-06-16 17:15:44');
INSERT INTO `product_sku` VALUES (71, 23, 'M0015-定', '[{\"specId\":14,\"specName\":\"定制防窥\",\"value\":\"定制防窥\"}]', 1680, 0, 0, 3170, 0, NULL, 1, '2025-06-16 17:19:41', '2025-06-16 17:24:23');
INSERT INTO `product_sku` VALUES (72, 25, 'M0017-NGE', '[{\"specId\":15,\"specName\":\"NEW GO ESD\",\"value\":\"NEW GO ESD\"}]', 1590, 0, 0, 37000, 0, NULL, 1, '2025-06-16 17:20:41', '2025-06-16 17:24:11');
INSERT INTO `product_sku` VALUES (73, 26, 'M0018-NGE', '[{\"specId\":15,\"specName\":\"NEW GO ESD\",\"value\":\"NEW GO ESD\"}]', 0, NULL, 0, 53000, 0, NULL, 1, '2025-06-16 17:21:41', '2025-06-16 17:20:50');
INSERT INTO `product_sku` VALUES (74, 11, 'M0003-L', '[{\"specId\":11,\"specName\":\"L001防窥\",\"value\":\"L001防窥\"}]', 0, 0, 0, 310625, 0, NULL, 1, '2025-06-16 17:21:54', '2025-06-16 17:22:02');
INSERT INTO `product_sku` VALUES (75, 10, 'M0002-ESX', '[{\"specId\":8,\"specName\":\"ESD SUPER X\",\"value\":\"ESD SUPER X\"}]', 0, NULL, 0, 444480, 0, NULL, 1, '2025-06-16 17:22:23', '2025-06-16 17:21:32');
INSERT INTO `product_sku` VALUES (76, 28, 'M0020-NGE', '[{\"specId\":15,\"specName\":\"NEW GO ESD\",\"value\":\"NEW GO ESD\"}]', 1930, NULL, 0, 205470, 0, NULL, 1, '2025-06-16 17:23:12', '2025-06-16 17:22:22');
INSERT INTO `product_sku` VALUES (77, 27, 'M0019-鳄', '[{\"specId\":16,\"specName\":\"鳄鱼定制防窥\",\"value\":\"鳄鱼定制防窥\"}]', 1760, 0, 0, 199000, 0, NULL, 1, '2025-06-16 17:23:51', '2025-06-17 09:07:40');
INSERT INTO `product_sku` VALUES (78, 18, 'M0010-MP', '[{\"specId\":10,\"specName\":\"MILAKE PRO-ESD\",\"value\":\"MILAKE PRO-ESD\"}]', 1600, NULL, 0, 0, 0, NULL, 1, '2025-06-16 17:24:49', '2025-06-16 17:23:58');

-- ----------------------------
-- Table structure for product_spec
-- ----------------------------
DROP TABLE IF EXISTS `product_spec`;
CREATE TABLE `product_spec`  (
  `spec_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `spec_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格名称',
  `spec_values` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格值JSON数组',
  `sort_order` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `dept_id` int(11) NOT NULL COMMENT '所属机构',
  PRIMARY KEY (`spec_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品规格表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of product_spec
-- ----------------------------
INSERT INTO `product_spec` VALUES (2, '小米手机型号', '[\"小米13\",\"小米14\",\"小米15\"]', 2, '2025-05-15 11:05:50', '2025-05-16 11:39:59', 9);
INSERT INTO `product_spec` VALUES (3, '手机膜规格', '[\"全屏超清2片\",\"高清2片\",\"水凝膜2片\"]', 5, '2025-05-15 11:08:12', '2025-05-16 11:40:21', 9);
INSERT INTO `product_spec` VALUES (4, '苹果型号', '[\"14\",\"15\",\"16\"]', 3, '2025-05-16 11:35:17', '2025-05-16 11:35:28', 9);
INSERT INTO `product_spec` VALUES (5, '华为型号', '[\"P50\",\"Mate70\",\"nova11\"]', 1, '2025-05-16 11:38:25', '2025-05-16 11:39:50', 9);
INSERT INTO `product_spec` VALUES (6, 'vivo型号', '[\"vivos15\",\"vivos16\"]', 4, '2025-05-16 11:39:35', '2025-05-16 11:40:07', 9);
INSERT INTO `product_spec` VALUES (7, 'OPPO型号', '[\"findX6\",\"findX6pro\",\"reno6pro\",\"reno4pro\"]', 6, '2025-05-30 10:05:21', '2025-05-30 10:04:37', 9);
INSERT INTO `product_spec` VALUES (8, 'ESD SUPER X', '[\"ESD SUPER X\"]', 0, '2025-06-16 11:29:16', '2025-06-16 14:40:50', 9);
INSERT INTO `product_spec` VALUES (9, '21D', '[\"21D\"]', 0, '2025-06-16 14:09:26', '2025-06-16 14:08:35', 9);
INSERT INTO `product_spec` VALUES (10, 'MILAKE PRO-ESD', '[\"MILAKE PRO-ESD\"]', 0, '2025-06-16 14:09:41', '2025-06-16 14:08:50', 9);
INSERT INTO `product_spec` VALUES (11, 'L001防窥', '[\"L001防窥\"]', 0, '2025-06-16 14:10:03', '2025-06-16 14:09:13', 9);
INSERT INTO `product_spec` VALUES (12, 'ESD防窥胶', '[\"ESD防窥胶\"]', 0, '2025-06-16 14:10:15', '2025-06-16 14:09:25', 9);
INSERT INTO `product_spec` VALUES (13, '白底黑字防窥', '[\"白底黑字防窥\"]', 0, '2025-06-16 14:10:55', '2025-06-16 14:10:04', 9);
INSERT INTO `product_spec` VALUES (14, '定制防窥', '[\"定制防窥\"]', 0, '2025-06-16 14:28:17', '2025-06-16 14:27:27', 9);
INSERT INTO `product_spec` VALUES (15, 'NEW GO ESD', '[\"NEW GO ESD\"]', 0, '2025-06-16 14:31:02', '2025-06-16 14:30:12', 9);
INSERT INTO `product_spec` VALUES (16, '鳄鱼定制防窥', '[\"鳄鱼定制防窥\"]', 0, '2025-06-16 14:31:13', '2025-06-16 14:30:23', 9);
INSERT INTO `product_spec` VALUES (17, 'D立方高铝防窥', '[\"D立方高铝防窥\"]', 0, '2025-06-16 14:31:21', '2025-06-16 14:30:30', 9);
INSERT INTO `product_spec` VALUES (18, 'MLIAKE高铝防窥', '[\"MLIAKE高铝防窥\"]', 0, '2025-06-16 14:31:34', '2025-06-16 15:25:09', 9);
INSERT INTO `product_spec` VALUES (21, 'NEW SUPER  X', '[\"NEW SUPER  X\"]', 0, '2025-06-16 14:31:58', '2025-06-16 14:31:08', 9);
INSERT INTO `product_spec` VALUES (23, '中铝定制-帽子', '[\"中铝定制-帽子\"]', 0, '2025-06-16 14:32:14', '2025-06-16 14:31:23', 9);
INSERT INTO `product_spec` VALUES (24, '18D', '[\"18D\"]', 0, '2025-06-16 14:32:21', '2025-06-16 14:31:31', 9);
INSERT INTO `product_spec` VALUES (25, '9D', '[\"9D\"]', 0, '2025-06-16 14:32:28', '2025-06-16 14:31:37', 9);
INSERT INTO `product_spec` VALUES (26, '定制 SAVIOUR', '[\"定制 SAVIOUR\"]', 0, '2025-06-16 14:32:37', '2025-06-16 14:31:46', 9);
INSERT INTO `product_spec` VALUES (27, '定制TAKICO', '[\"定制TAKICO\"]', 0, '2025-06-16 14:32:43', '2025-06-16 14:31:53', 9);
INSERT INTO `product_spec` VALUES (28, '定制AUTSTATIC', '[\"定制AUTSTATIC\"]', 0, '2025-06-16 14:32:50', '2025-06-16 14:32:00', 9);

-- ----------------------------
-- Table structure for sales_order
-- ----------------------------
DROP TABLE IF EXISTS `sales_order`;
CREATE TABLE `sales_order`  (
  `order_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '销售单ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '销售单号',
  `dept_id` int(11) NOT NULL COMMENT '所属机构ID',
  `partner_id` bigint(20) NULL DEFAULT NULL COMMENT '客户ID',
  `order_date` date NOT NULL COMMENT '单据日期',
  `total_amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '订单总金额(分)',
  `discount_amount` bigint(20) NULL DEFAULT 0 COMMENT '优惠金额(分)',
  `actual_amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '实际金额(分)',
  `payment_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '付款状态：0-未付款，1-已付款, 2-付款中',
  `payment_amount` bigint(20) NULL DEFAULT 0 COMMENT '已付金额(分)',
  `delivery_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发货状态：0-未发货，1-已发货',
  `order_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单状态：0-未审核，1-已审核',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `out_payment_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台支付单号',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`order_id`) USING BTREE,
  UNIQUE INDEX `idx_order_no`(`order_no`) USING BTREE,
  INDEX `idx_dept_id`(`dept_id`) USING BTREE,
  INDEX `idx_partner_id`(`partner_id`) USING BTREE,
  INDEX `idx_order_date`(`order_date`) USING BTREE,
  INDEX `idx_order_status`(`order_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '销售单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sales_order
-- ----------------------------
INSERT INTO `sales_order` VALUES (3, '20250521155007866', 9, 1, '2025-05-21', 122100, 10000, 112100, 1, 112100, 0, 1, '已优惠100元', '2025-05-21 15:51:20', '2025-05-27 10:41:38', '2025052710101148', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (4, 'SO20250528165218201', 9, 2, '2025-05-28', 28900, 100, 28800, 1, 28800, 0, 1, '注意已经优惠了1元', '2025-05-28 16:55:09', '2025-05-28 16:54:18', '2025052817101149', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (5, 'SO20250529095214338', 9, 2, '2025-05-29', 1980, 0, 1980, 1, 1980, 0, 1, '经销商', '2025-05-29 09:53:22', '2025-05-29 09:52:38', '2025052909101150', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (6, 'SO20250529100952103', 9, 3, '2025-05-29', 3980, 0, 3980, 1, 3980, 0, 1, NULL, '2025-05-29 10:11:02', '2025-05-29 10:10:19', '2025053008101152', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (7, 'SO20250530083316766', 9, 3, '2025-05-30', 1100, 0, 1100, 0, 0, 0, 1, NULL, '2025-05-30 08:33:36', '2025-05-30 08:32:52', '2025053008101153', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (8, 'SO20250530083523267', 9, 2, '2025-05-30', 990, 0, 990, 2, 0, 0, 1, NULL, '2025-05-30 08:35:48', '2025-05-30 08:35:05', '2025053008101154', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (9, 'SO20250530093315070', 9, 1, '2025-05-30', 296300, 10000, 286300, 1, 286300, 0, 1, '已优惠100元', '2025-05-30 09:34:10', '2025-05-30 09:33:26', '2025053009101155', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (10, 'SO20250530093903450', 9, 2, '2025-05-30', 59600, 1000, 58600, 0, 0, 0, 0, '申请10元优惠', '2025-05-30 09:39:57', '2025-05-30 09:39:13', NULL, 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (11, 'SO20250530094208786', 9, 2, '2025-05-30', 240300, 10000, 230300, 1, 230300, 0, 1, '可优惠100元', '2025-05-30 09:42:51', '2025-05-30 09:42:08', '2025053011101157', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (12, 'SO20250530100909281', 9, 4, '2025-05-30', 198400, 10000, 188400, 0, 0, 0, 1, '已经申请优惠100元', '2025-05-30 10:10:50', '2025-05-30 10:10:06', NULL, 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (13, 'SO20250530111545970', 9, 1, '2025-05-30', 56000, 1000, 55000, 1, 55000, 0, 1, '方法', '2025-05-30 11:16:26', '2025-05-30 11:15:42', '2025053011101161', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (14, 'SO20250530114807855', 9, 1, '2025-05-30', 65500, 1000, 64500, 0, 0, 0, 1, '解决就', '2025-05-30 11:48:46', '2025-05-30 11:48:02', '2025053011101160', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (15, 'SO20250530152824744', 9, 1, '2025-05-30', 40000, 1000, 39000, 1, 39000, 0, 1, '了', '2025-05-30 15:28:56', '2025-05-30 15:28:13', '2025053015101164', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (16, 'SO20250603094814138', 9, 1, '2025-06-03', 17600, 100, 17500, 0, 0, 0, 1, 'd', '2025-06-03 09:48:48', '2025-06-13 09:56:32', '2025061310101175', 'mingyihu', 'mingyihu');
INSERT INTO `sales_order` VALUES (17, 'SO20250610150250579', 9, 4, '2025-06-10', 11000, 1100, 9900, 0, 0, 0, 1, NULL, '2025-06-10 15:03:13', '2025-06-10 15:02:22', '2025061311101123', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (24, 'WX202506130942490001', 9, 6, '2025-06-13', 1100, 0, 1100, 0, 0, 0, 0, '微信小程序下单', '2025-06-13 09:42:49', '2025-06-13 09:48:58', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (25, 'WX202506130950390001', 9, 6, '2025-06-13', 1000, 0, 1000, 0, 0, 0, 1, '微信小程序下单', '2025-06-13 09:50:39', '2025-06-13 09:49:48', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (26, 'WX202506131002530001', 9, 6, '2025-06-13', 990, 0, 990, 0, 0, 0, 1, '微信小程序下单', '2025-06-13 10:02:53', '2025-06-13 10:02:02', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (27, 'WX202506131006170002', 9, 6, '2025-06-13', 990, 0, 990, 0, 0, 0, 1, '微信小程序下单', '2025-06-13 10:06:17', '2025-06-13 10:05:25', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (28, 'WX202506131011060001', 9, 6, '2025-06-13', 6930, 0, 6930, 1, 6930, 0, 1, '微信小程序下单', '2025-06-13 10:11:07', '2025-06-13 10:10:15', '2025061310101174', 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (29, 'WX202506131129380001', 9, 6, '2025-06-13', 12800, 0, 12800, 0, 0, 0, 0, '微信小程序下单', '2025-06-13 11:29:38', '2025-06-13 11:28:48', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (30, 'WX202506131134380002', 9, 6, '2025-06-13', 1980, 0, 1980, 0, 0, 0, 0, '微信小程序下单', '2025-06-13 11:34:38', '2025-06-13 11:33:48', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (31, 'WX202506131135070003', 9, 6, '2025-06-13', 1980, 0, 1980, 0, 0, 0, 0, '微信小程序下单', '2025-06-13 11:35:07', '2025-06-13 11:34:17', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (32, 'WX202506131135360004', 9, 6, '2025-06-13', 4950, 0, 4950, 0, 0, 0, 0, '微信小程序下单', '2025-06-13 11:35:36', '2025-06-13 11:34:46', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (33, 'WX202506131138580005', 9, 6, '2025-06-13', 990, 0, 990, 0, 0, 0, 0, '微信小程序下单', '2025-06-13 11:38:58', '2025-06-13 11:38:08', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (34, 'WX202506131139440006', 9, 6, '2025-06-13', 990, 0, 990, 0, 0, 0, 0, '微信小程序下单', '2025-06-13 11:39:44', '2025-06-13 11:38:54', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (35, 'WX202506131144090007', 9, 6, '2025-06-13', 990, 0, 990, 0, 0, 0, 1, '微信小程序下单', '2025-06-13 11:44:09', '2025-06-13 11:43:19', '2025061815101190', 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (36, 'WX202506131144470008', 9, 6, '2025-06-13', 9900, 0, 9900, 0, 0, 0, 1, '微信小程序下单', '2025-06-13 11:44:47', '2025-06-13 11:43:57', '2025061311101179', 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (37, 'WX202506131151000009', 9, 6, '2025-06-13', 9900, 0, 9900, 0, 0, 0, 1, '微信小程序下单', '2025-06-13 11:51:00', '2025-06-13 11:50:11', '2025061313101181', 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (38, 'SO20250613145203141', 9, 1, '2025-06-13', 110000, 0, 110000, 1, 110000, 0, 1, '就', '2025-06-13 14:52:05', '2025-06-13 14:51:13', '2025061314101182', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (39, 'WX202506131454490010', 9, 6, '2025-06-13', 16000, 0, 16000, 0, 0, 0, 1, '微信小程序下单', '2025-06-13 14:54:49', '2025-06-13 14:53:59', '2025061314101183', 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (40, 'WX202506131537170001', 9, 6, '2025-06-13', 6930, 0, 6930, 0, 0, 0, 1, '微信小程序下单', '2025-06-13 15:37:17', '2025-06-13 15:36:26', '2025061315101125', 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (41, 'WX202506132322180011', 9, 6, '2025-06-13', 10890, 0, 10890, 0, 0, 0, 0, '微信小程序下单', '2025-06-13 23:22:18', '2025-06-13 23:21:28', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (42, 'SO20250616154425547', 9, 4, '2025-06-16', 5600, 0, 5600, 0, 0, 0, 1, NULL, '2025-06-16 15:44:29', '2025-06-16 15:46:01', '2025061617101185', 'mingyihu', 'mingyihu');
INSERT INTO `sales_order` VALUES (43, 'WX202506161700520012', 9, 6, '2025-06-16', 3000, 0, 3000, 0, 0, 0, 1, '微信小程序下单', '2025-06-16 17:00:52', '2025-06-16 17:00:02', '2025061617101186', 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (44, 'SO20250616172548354', 9, 3, '2025-06-16', 3400, 0, 3400, 1, 3400, 0, 1, NULL, '2025-06-16 17:26:08', '2025-06-16 17:25:18', '2025061617101187', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (45, 'SO20250617094719660', 9, 2, '2025-06-17', 20970, 0, 20970, 1, 20970, 0, 1, NULL, '2025-06-17 09:48:11', '2025-06-17 09:47:20', '2025061710101189', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (46, 'WX202506181010480013', 9, 6, '2025-06-18', 6900, 0, 6900, 0, 0, 0, 0, '微信小程序下单', '2025-06-18 10:10:48', '2025-06-18 10:09:58', NULL, 'wx_miniprogram', NULL);
INSERT INTO `sales_order` VALUES (47, 'SO20250618151406359', 9, 2, '2025-06-18', 5100, 0, 5100, 1, 5100, 0, 1, NULL, '2025-06-18 15:14:12', '2025-06-18 15:13:21', '2025061815101191', 'mingyihu', NULL);
INSERT INTO `sales_order` VALUES (48, 'WX202506181516510014', 9, 6, '2025-06-18', 8800, 0, 8800, 0, 0, 0, 0, '微信小程序下单', '2025-06-18 15:16:52', '2025-06-18 15:16:01', NULL, 'wx_miniprogram', NULL);

-- ----------------------------
-- Table structure for sales_order_item
-- ----------------------------
DROP TABLE IF EXISTS `sales_order_item`;
CREATE TABLE `sales_order_item`  (
  `item_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `order_id` bigint(20) NOT NULL COMMENT '销售单ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `sku_id` bigint(20) NULL DEFAULT NULL COMMENT 'SKU ID',
  `product_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `product_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `sku_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SKU编码',
  `spec_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '规格数据JSON',
  `unit` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位',
  `quantity` decimal(10, 2) NOT NULL COMMENT '数量',
  `price` bigint(20) NOT NULL COMMENT '单价(分)',
  `amount` bigint(20) NOT NULL COMMENT '金额(分)',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`item_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_sku_id`(`sku_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 88 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '销售单明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sales_order_item
-- ----------------------------
INSERT INTO `sales_order_item` VALUES (1, 2, 1, 1, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', '套', 1.00, 1100, 1100, '', '2025-05-20 10:24:22', '2025-05-20 10:23:35');
INSERT INTO `sales_order_item` VALUES (2, 1, 1, 4, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-4', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"15\"}]', '套', 9.00, 800, 7200, '', '2025-05-20 14:16:53', '2025-05-20 14:16:06');
INSERT INTO `sales_order_item` VALUES (3, 3, 2, 12, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 40.00, 990, 39600, '', '2025-05-21 15:51:20', '2025-05-21 15:50:35');
INSERT INTO `sales_order_item` VALUES (4, 3, 1, 1, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', '套', 30.00, 1100, 33000, '', '2025-05-21 15:51:20', '2025-05-21 15:50:35');
INSERT INTO `sales_order_item` VALUES (5, 3, 3, 24, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-6', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', '套', 50.00, 990, 49500, '', '2025-05-21 15:51:20', '2025-05-21 15:50:35');
INSERT INTO `sales_order_item` VALUES (6, 4, 1, 1, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', '套', 10.00, 1100, 11000, '', '2025-05-28 16:55:09', '2025-05-28 16:54:18');
INSERT INTO `sales_order_item` VALUES (7, 4, 2, 17, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-8', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', '套', 10.00, 990, 9900, '', '2025-05-28 16:55:09', '2025-05-28 16:54:18');
INSERT INTO `sales_order_item` VALUES (8, 4, 5, 35, '信弘达 适用小米14钢化膜xiaomi 13手机膜无孔无黑边电镀防指纹', '33332', '33332-2', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米13\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"}]', '套', 10.00, 800, 8000, '', '2025-05-28 16:55:09', '2025-05-28 16:54:18');
INSERT INTO `sales_order_item` VALUES (9, 5, 2, 12, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 1.00, 990, 990, '', '2025-05-29 09:53:22', '2025-05-29 09:52:38');
INSERT INTO `sales_order_item` VALUES (10, 5, 3, 24, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-6', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', '套', 1.00, 990, 990, '', '2025-05-29 09:53:22', '2025-05-29 09:52:38');
INSERT INTO `sales_order_item` VALUES (11, 6, 1, 6, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-6', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"15\"}]', '套', 2.00, 1000, 2000, '', '2025-05-29 10:11:02', '2025-05-29 10:10:19');
INSERT INTO `sales_order_item` VALUES (12, 6, 2, 12, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 1.00, 990, 990, '', '2025-05-29 10:11:02', '2025-05-29 10:10:19');
INSERT INTO `sales_order_item` VALUES (13, 6, 3, 20, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 1.00, 990, 990, '', '2025-05-29 10:11:02', '2025-05-29 10:10:19');
INSERT INTO `sales_order_item` VALUES (14, 7, 1, 1, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', '套', 1.00, 1100, 1100, '', '2025-05-30 08:33:36', '2025-05-30 08:32:52');
INSERT INTO `sales_order_item` VALUES (15, 8, 2, 10, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 1.00, 990, 990, '', '2025-05-30 08:35:48', '2025-05-30 08:35:05');
INSERT INTO `sales_order_item` VALUES (16, 9, 5, 42, '信弘达 适用小米14钢化膜xiaomi 13手机膜无孔无黑边电镀防指纹', '33332', '33332-9', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米15\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"}]', '套', 80.00, 800, 64000, '', '2025-05-30 09:34:10', '2025-05-30 09:33:26');
INSERT INTO `sales_order_item` VALUES (17, 9, 4, 52, '适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s', NULL, 'SKU186901', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":6,\"specName\":\"vivo型号\",\"value\":\"vivos15\"}]', '套', 80.00, 800, 64000, '', '2025-05-30 09:34:10', '2025-05-30 09:33:26');
INSERT INTO `sales_order_item` VALUES (18, 9, 3, 22, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-4', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', '套', 90.00, 990, 89100, '', '2025-05-30 09:34:10', '2025-05-30 09:33:26');
INSERT INTO `sales_order_item` VALUES (19, 9, 2, 10, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 80.00, 990, 79200, '', '2025-05-30 09:34:10', '2025-05-30 09:33:26');
INSERT INTO `sales_order_item` VALUES (20, 10, 1, 2, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', '套', 20.00, 1000, 20000, '', '2025-05-30 09:39:57', '2025-05-30 09:39:13');
INSERT INTO `sales_order_item` VALUES (21, 10, 2, 12, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 20.00, 990, 19800, '', '2025-05-30 09:39:57', '2025-05-30 09:39:13');
INSERT INTO `sales_order_item` VALUES (22, 10, 3, 23, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-5', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', '套', 20.00, 990, 19800, '', '2025-05-30 09:39:57', '2025-05-30 09:39:13');
INSERT INTO `sales_order_item` VALUES (23, 11, 1, 3, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', '套', 80.00, 900, 72000, '', '2025-05-30 09:42:51', '2025-05-30 09:42:08');
INSERT INTO `sales_order_item` VALUES (24, 11, 2, 11, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 80.00, 990, 79200, '', '2025-05-30 09:42:51', '2025-05-30 09:42:08');
INSERT INTO `sales_order_item` VALUES (25, 11, 3, 20, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 90.00, 990, 89100, '', '2025-05-30 09:42:51', '2025-05-30 09:42:08');
INSERT INTO `sales_order_item` VALUES (26, 12, 8, 59, '适用OPPO Xfold3钢化膜', '77775555', '77775555-F', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"findX6pro\"}]', '件', 10.00, 800, 8000, '', '2025-05-30 10:10:50', '2025-05-30 10:10:06');
INSERT INTO `sales_order_item` VALUES (27, 12, 8, 58, '适用OPPO Xfold3钢化膜', '77775555', '77775555-F', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"findX6\"}]', '件', 100.00, 800, 80000, '', '2025-05-30 10:10:50', '2025-05-30 10:10:06');
INSERT INTO `sales_order_item` VALUES (28, 12, 8, 61, '适用OPPO Xfold3钢化膜', '77775555', '77775555-R', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"reno4pro\"}]', '件', 50.00, 800, 40000, '', '2025-05-30 10:10:50', '2025-05-30 10:10:06');
INSERT INTO `sales_order_item` VALUES (29, 12, 8, 60, '适用OPPO Xfold3钢化膜', '77775555', '77775555-R', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"reno6pro\"}]', '件', 88.00, 800, 70400, '', '2025-05-30 10:10:50', '2025-05-30 10:10:06');
INSERT INTO `sales_order_item` VALUES (30, 13, 8, 58, '适用OPPO Xfold3钢化膜', '77775555', '77775555-F', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"findX6\"}]', '件', 20.00, 800, 16000, '', '2025-05-30 11:16:26', '2025-05-30 11:15:42');
INSERT INTO `sales_order_item` VALUES (31, 13, 6, 44, '适用华为钢化膜mate60P70nova12畅享70大猩猩电镀全屏P70荣耀50SE', NULL, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 50.00, 800, 40000, '', '2025-05-30 11:16:26', '2025-05-30 11:15:42');
INSERT INTO `sales_order_item` VALUES (32, 14, 3, 23, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-5', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', '套', 50.00, 990, 49500, '', '2025-05-30 11:48:46', '2025-05-30 11:48:02');
INSERT INTO `sales_order_item` VALUES (33, 14, 8, 58, '适用OPPO Xfold3钢化膜', '77775555', '77775555-F', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"findX6\"}]', '件', 20.00, 800, 16000, '', '2025-05-30 11:48:46', '2025-05-30 11:48:02');
INSERT INTO `sales_order_item` VALUES (34, 15, 8, 58, '适用OPPO Xfold3钢化膜', '77775555', '77775555-F', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"findX6\"}]', '件', 20.00, 800, 16000, '', '2025-05-30 15:28:56', '2025-05-30 15:28:13');
INSERT INTO `sales_order_item` VALUES (35, 15, 6, 48, '适用华为钢化膜mate60P70nova12畅享70大猩猩电镀全屏P70荣耀50SE', NULL, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', '套', 30.00, 800, 24000, '', '2025-05-30 15:28:56', '2025-05-30 15:28:13');
INSERT INTO `sales_order_item` VALUES (36, 16, 8, 58, '适用OPPO Xfold3钢化膜', '77775555', '77775555-F', '[{\"specId\":7,\"specName\":\"OPPO型号\",\"value\":\"findX6\"}]', '件', 12.00, 800, 9600, '', '2025-06-03 09:48:48', '2025-06-13 09:56:32');
INSERT INTO `sales_order_item` VALUES (37, 16, 6, 43, '适用华为钢化膜mate60P70nova12畅享70大猩猩电镀全屏P70荣耀50SE', NULL, 'SKU135854', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 10.00, 800, 8000, '', '2025-06-03 09:48:48', '2025-06-13 09:56:32');
INSERT INTO `sales_order_item` VALUES (38, 17, 1, 1, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', '套', 10.00, 1100, 11000, '', '2025-06-10 15:03:13', '2025-06-10 15:02:22');
INSERT INTO `sales_order_item` VALUES (39, 24, 1, 1, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', NULL, 1.00, 1100, 1100, NULL, '2025-06-13 09:42:50', '2025-06-13 09:41:58');
INSERT INTO `sales_order_item` VALUES (40, 25, 2, 2, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', NULL, 1.00, 1000, 1000, NULL, '2025-06-13 09:50:39', '2025-06-13 09:49:48');
INSERT INTO `sales_order_item` VALUES (41, 26, 2, 10, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 1.00, 990, 990, NULL, '2025-06-13 10:02:53', '2025-06-13 10:02:02');
INSERT INTO `sales_order_item` VALUES (42, 27, 2, 10, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 1.00, 990, 990, NULL, '2025-06-13 10:06:17', '2025-06-13 10:05:25');
INSERT INTO `sales_order_item` VALUES (43, 28, 2, 10, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 2.00, 990, 1980, NULL, '2025-06-13 10:11:07', '2025-06-13 10:10:15');
INSERT INTO `sales_order_item` VALUES (44, 28, 2, 11, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 3.00, 990, 2970, NULL, '2025-06-13 10:11:07', '2025-06-13 10:10:15');
INSERT INTO `sales_order_item` VALUES (45, 28, 2, 18, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-9', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', NULL, 2.00, 990, 1980, NULL, '2025-06-13 10:11:07', '2025-06-13 10:10:15');
INSERT INTO `sales_order_item` VALUES (46, 29, 4, 52, '适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s', NULL, 'SKU186901', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":6,\"specName\":\"vivo型号\",\"value\":\"vivos15\"}]', NULL, 5.00, 800, 4000, NULL, '2025-06-13 11:29:38', '2025-06-13 11:28:48');
INSERT INTO `sales_order_item` VALUES (47, 29, 4, 53, '适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s', NULL, 'SKU186901', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":6,\"specName\":\"vivo型号\",\"value\":\"vivos15\"}]', NULL, 3.00, 800, 2400, NULL, '2025-06-13 11:29:38', '2025-06-13 11:28:48');
INSERT INTO `sales_order_item` VALUES (48, 29, 4, 54, '适用vivox70/x60/x50/x30钢化膜x9s全屏x27手机膜x23贴膜高清x21s', NULL, 'SKU186901', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":6,\"specName\":\"vivo型号\",\"value\":\"vivos15\"}]', NULL, 8.00, 800, 6400, NULL, '2025-06-13 11:29:38', '2025-06-13 11:28:48');
INSERT INTO `sales_order_item` VALUES (49, 30, 3, 21, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 2.00, 990, 1980, NULL, '2025-06-13 11:34:38', '2025-06-13 11:33:48');
INSERT INTO `sales_order_item` VALUES (50, 31, 3, 20, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 2.00, 990, 1980, NULL, '2025-06-13 11:35:07', '2025-06-13 11:34:17');
INSERT INTO `sales_order_item` VALUES (51, 32, 3, 20, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 2.00, 990, 1980, NULL, '2025-06-13 11:35:36', '2025-06-13 11:34:46');
INSERT INTO `sales_order_item` VALUES (52, 32, 3, 27, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-9', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', NULL, 3.00, 990, 2970, NULL, '2025-06-13 11:35:36', '2025-06-13 11:34:46');
INSERT INTO `sales_order_item` VALUES (53, 33, 2, 10, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 1.00, 990, 990, NULL, '2025-06-13 11:38:58', '2025-06-13 11:38:08');
INSERT INTO `sales_order_item` VALUES (54, 34, 2, 11, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 1.00, 990, 990, NULL, '2025-06-13 11:39:44', '2025-06-13 11:38:54');
INSERT INTO `sales_order_item` VALUES (55, 35, 2, 14, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-5', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', NULL, 1.00, 990, 990, NULL, '2025-06-13 11:44:09', '2025-06-13 11:43:19');
INSERT INTO `sales_order_item` VALUES (56, 36, 2, 11, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 6.00, 990, 5940, NULL, '2025-06-13 11:44:47', '2025-06-13 11:43:57');
INSERT INTO `sales_order_item` VALUES (57, 36, 2, 12, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 4.00, 990, 3960, NULL, '2025-06-13 11:44:47', '2025-06-13 11:43:57');
INSERT INTO `sales_order_item` VALUES (58, 37, 2, 10, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 7.00, 990, 6930, NULL, '2025-06-13 11:51:00', '2025-06-13 11:50:11');
INSERT INTO `sales_order_item` VALUES (59, 37, 2, 14, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-5', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', NULL, 3.00, 990, 2970, NULL, '2025-06-13 11:51:00', '2025-06-13 11:50:11');
INSERT INTO `sales_order_item` VALUES (60, 38, 1, 1, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', '套', 10.00, 1100, 11000, '', '2025-06-13 14:52:05', '2025-06-13 14:51:13');
INSERT INTO `sales_order_item` VALUES (61, 38, 2, 12, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-3', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', '套', 100.00, 990, 99000, '', '2025-06-13 14:52:05', '2025-06-13 14:51:13');
INSERT INTO `sales_order_item` VALUES (62, 39, 1, 1, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"14\"}]', NULL, 8.00, 1100, 8800, NULL, '2025-06-13 14:54:49', '2025-06-13 14:53:59');
INSERT INTO `sales_order_item` VALUES (63, 39, 1, 4, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-4', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"15\"}]', NULL, 4.00, 800, 3200, NULL, '2025-06-13 14:54:49', '2025-06-13 14:53:59');
INSERT INTO `sales_order_item` VALUES (64, 39, 1, 8, '苹果16/15/14/13promax手机钢化膜12/11全屏高清绿光防窥无尘仓', '1234567', '1234567-8', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":4,\"specName\":\"苹果型号\",\"value\":\"16\"}]', NULL, 4.00, 1000, 4000, NULL, '2025-06-13 14:54:49', '2025-06-13 14:53:59');
INSERT INTO `sales_order_item` VALUES (65, 40, 2, 10, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 4.00, 990, 3960, NULL, '2025-06-13 15:37:18', '2025-06-13 15:36:26');
INSERT INTO `sales_order_item` VALUES (66, 40, 2, 11, '适用华为折叠屏mate x5防窥膜mate x6防偷窥膜水凝膜折叠屏手机膜', '33321231', '33321231-2', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 3.00, 990, 2970, NULL, '2025-06-13 15:37:18', '2025-06-13 15:36:26');
INSERT INTO `sales_order_item` VALUES (67, 41, 3, 19, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-1', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"P50\"}]', NULL, 5.00, 990, 4950, NULL, '2025-06-13 23:22:18', '2025-06-13 23:21:28');
INSERT INTO `sales_order_item` VALUES (68, 41, 3, 23, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-5', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"Mate70\"}]', NULL, 3.00, 990, 2970, NULL, '2025-06-13 23:22:18', '2025-06-13 23:21:28');
INSERT INTO `sales_order_item` VALUES (69, 41, 3, 27, '适用华为mate60pro手机膜mate60水凝膜mate60pro', '314512', '314512-9', '[{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"水凝膜2片\"},{\"specId\":5,\"specName\":\"华为型号\",\"value\":\"nova11\"}]', NULL, 3.00, 990, 2970, NULL, '2025-06-13 23:22:18', '2025-06-13 23:21:28');
INSERT INTO `sales_order_item` VALUES (70, 42, 12, 63, '21D', 'M0004', 'M0004-2', '[{\"specId\":9,\"specName\":\"21D\",\"value\":\"21D\"}]', '件', 1.00, 3000, 3000, '', '2025-06-16 15:46:01', '2025-06-16 15:45:10');
INSERT INTO `sales_order_item` VALUES (71, 42, 15, 66, 'NEW GO ESD', 'M0007', 'M0007-NGE', '[{\"specId\":15,\"specName\":\"NEW GO ESD\",\"value\":\"NEW GO ESD\"}]', '件', 1.00, 2600, 2600, '', '2025-06-16 15:46:01', '2025-06-16 15:45:10');
INSERT INTO `sales_order_item` VALUES (72, 43, 12, 63, '21D', 'M0004', 'M0004-2', '[{\"specId\":9,\"specName\":\"21D\",\"value\":\"21D\"}]', NULL, 1.00, 3000, 3000, NULL, '2025-06-16 17:00:52', '2025-06-16 17:00:02');
INSERT INTO `sales_order_item` VALUES (73, 44, 13, 64, 'ESD防窥胶', 'M0005', 'M0005-E', '[{\"specId\":12,\"specName\":\"ESD防窥胶\",\"value\":\"ESD防窥胶\"}]', '件', 1.00, 3400, 3400, '', '2025-06-16 17:26:08', '2025-06-16 17:25:18');
INSERT INTO `sales_order_item` VALUES (74, 45, 12, 63, '21D', 'M0004', 'M0004-2', '[{\"specId\":9,\"specName\":\"21D\",\"value\":\"21D\"}]', '件', 1.00, 3000, 3000, '', '2025-06-17 09:48:11', '2025-06-17 09:47:20');
INSERT INTO `sales_order_item` VALUES (75, 45, 13, 64, 'ESD防窥胶', 'M0005', 'M0005-E', '[{\"specId\":12,\"specName\":\"ESD防窥胶\",\"value\":\"ESD防窥胶\"}]', '件', 1.00, 3400, 3400, '', '2025-06-17 09:48:11', '2025-06-17 09:47:20');
INSERT INTO `sales_order_item` VALUES (76, 45, 15, 66, 'NEW GO ESD', 'M0007', 'M0007-NGE', '[{\"specId\":15,\"specName\":\"NEW GO ESD\",\"value\":\"NEW GO ESD\"}]', '件', 1.00, 2600, 2600, '', '2025-06-17 09:48:11', '2025-06-17 09:47:20');
INSERT INTO `sales_order_item` VALUES (77, 45, 14, 65, '定制防窥', 'M0006', 'M0006-定', '[{\"specId\":14,\"specName\":\"定制防窥\",\"value\":\"定制防窥\"}]', '件', 1.00, 2100, 2100, '', '2025-06-17 09:48:11', '2025-06-17 09:47:20');
INSERT INTO `sales_order_item` VALUES (78, 45, 16, 67, '鳄鱼定制防窥', 'M0008', 'M0008-鳄', '[{\"specId\":16,\"specName\":\"鳄鱼定制防窥\",\"value\":\"鳄鱼定制防窥\"}]', '件', 1.00, 2700, 2700, '', '2025-06-17 09:48:11', '2025-06-17 09:47:20');
INSERT INTO `sales_order_item` VALUES (79, 45, 18, 78, 'MLIAKE高铝防窥', 'M0010', 'M0010-MP', '[{\"specId\":10,\"specName\":\"MILAKE PRO-ESD\",\"value\":\"MILAKE PRO-ESD\"}]', '件', 1.00, 1600, 1600, '', '2025-06-17 09:48:11', '2025-06-17 09:47:20');
INSERT INTO `sales_order_item` VALUES (80, 45, 17, 68, 'D立方高铝防窥', 'M0009', 'M0009-D', '[{\"specId\":17,\"specName\":\"D立方高铝防窥\",\"value\":\"D立方高铝防窥\"}]', '件', 1.00, 2300, 2300, '', '2025-06-17 09:48:11', '2025-06-17 09:47:20');
INSERT INTO `sales_order_item` VALUES (81, 45, 25, 72, '9D', 'M0017', 'M0017-NGE', '[{\"specId\":15,\"specName\":\"NEW GO ESD\",\"value\":\"NEW GO ESD\"}]', '件', 1.00, 1590, 1590, '', '2025-06-17 09:48:11', '2025-06-17 09:47:20');
INSERT INTO `sales_order_item` VALUES (82, 45, 23, 71, '中铝定制-帽子', 'M0015', 'M0015-定', '[{\"specId\":14,\"specName\":\"定制防窥\",\"value\":\"定制防窥\"}]', '件', 1.00, 1680, 1680, '', '2025-06-17 09:48:11', '2025-06-17 09:47:20');
INSERT INTO `sales_order_item` VALUES (83, 46, 17, 68, 'D立方高铝防窥', 'M0009', 'M0009-D', '[{\"specId\":17,\"specName\":\"D立方高铝防窥\",\"value\":\"D立方高铝防窥\"}]', NULL, 3.00, 2300, 6900, NULL, '2025-06-18 10:10:48', '2025-06-18 10:09:58');
INSERT INTO `sales_order_item` VALUES (84, 47, 12, 63, '21D', 'M0004', 'M0004-2', '[{\"specId\":9,\"specName\":\"21D\",\"value\":\"21D\"}]', '件', 1.00, 3000, 3000, '', '2025-06-18 15:14:12', '2025-06-18 15:13:21');
INSERT INTO `sales_order_item` VALUES (85, 47, 14, 65, '定制防窥', 'M0006', 'M0006-定', '[{\"specId\":14,\"specName\":\"定制防窥\",\"value\":\"定制防窥\"}]', '件', 1.00, 2100, 2100, '', '2025-06-18 15:14:12', '2025-06-18 15:13:21');
INSERT INTO `sales_order_item` VALUES (86, 48, 5, 34, '信弘达 适用小米14钢化膜xiaomi 13手机膜无孔无黑边电镀防指纹', '33332', '33332-1', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米13\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"全屏超清2片\"}]', NULL, 7.00, 800, 5600, NULL, '2025-06-18 15:16:52', '2025-06-18 15:16:01');
INSERT INTO `sales_order_item` VALUES (87, 48, 5, 35, '信弘达 适用小米14钢化膜xiaomi 13手机膜无孔无黑边电镀防指纹', '33332', '33332-2', '[{\"specId\":2,\"specName\":\"小米手机型号\",\"value\":\"小米13\"},{\"specId\":3,\"specName\":\"手机膜规格\",\"value\":\"高清2片\"}]', NULL, 4.00, 800, 3200, NULL, '2025-06-18 15:16:52', '2025-06-18 15:16:01');

-- ----------------------------
-- Table structure for sales_order_pay
-- ----------------------------
DROP TABLE IF EXISTS `sales_order_pay`;
CREATE TABLE `sales_order_pay`  (
  `out_payment_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户支付单号',
  `order_id` bigint(20) NOT NULL COMMENT '销售单ID',
  `pay_channel` int(2) NOT NULL DEFAULT 1 COMMENT '1：微企付 2：银盛',
  `amount` bigint(20) NOT NULL COMMENT '支付金额(分)',
  `expire_time` datetime(0) NOT NULL COMMENT '过期时间',
  `memo` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附言',
  `payee_ent_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收款商户企业ID',
  `payee_ent_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收款商户名称',
  `create_time` datetime(0) NOT NULL COMMENT '平台订单生成时间',
  `payer_options` json NULL COMMENT '付款方信息JSON',
  `channel_options` json NULL COMMENT '渠道选项JSON',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'USERPAYING' COMMENT '订单状态\r\nSUCCESS：支付成功\r\nREFUND：退款\r\nNOTPAY：未支付\r\nCLOSED：已关闭\r\nUSERPAYING：用户支付中\r\nPAYERROR：支付失败',
  `payment_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付渠道订单号',
  `fee_amount` bigint(20) NULL DEFAULT NULL COMMENT '总手续费金额，单位：分',
  `close_reason` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关单原因',
  `close_time` datetime(0) NULL DEFAULT NULL COMMENT '关单成功时间',
  `out_refund_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台退款单号',
  `refund_amount` bigint(20) NULL DEFAULT NULL COMMENT '退款金额，单位：分',
  `refund_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款原因',
  `refund_time` datetime(0) NULL DEFAULT NULL COMMENT '退款时间',
  `refund_channel` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款渠道:ORIGINAL 原路退款,OTHER_BALANCE 退到卖家账户余额,OTHER_BANKCARD 退到买家其他银行卡',
  `refund_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款状态:ACCEPTED 退款受理成功\r\nABNORMAL 退款异常\r\nSUCCEEDED 退款成功\r\nMERCHANT_SUCCEEDED 退回商户余额成功\r\nFAILED 退款失败',
  `payment_type` int(2) NOT NULL DEFAULT 1 COMMENT '1：微信\r\n2：支付宝\r\n3：微企付\r\n4：云闪付\r\n5：银行卡',
  `pay_time` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付完成时间',
  `open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `qrcode_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '二维码地址',
  PRIMARY KEY (`out_payment_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sales_order_pay
-- ----------------------------
INSERT INTO `sales_order_pay` VALUES ('2025052710101148', 3, 2, 112100, '2025-05-27 10:47:51', NULL, '************', '微企付测试商户', '2025-05-27 10:42:51', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://qr.alipay.com/bax077702sgthwe7nyvo55ac');
INSERT INTO `sales_order_pay` VALUES ('2025052817101149', 4, 2, 28800, '2025-05-28 17:06:21', NULL, '************', '微企付测试商户', '2025-05-28 17:01:21', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://qr.alipay.com/bax094857pnggep3dluo00a8');
INSERT INTO `sales_order_pay` VALUES ('2025052909101150', 5, 2, 1980, '2025-05-29 09:59:56', NULL, '************', '微企付测试商户', '2025-05-29 09:54:56', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://qr.alipay.com/bax07282yiqlcryxdb5q0074');
INSERT INTO `sales_order_pay` VALUES ('2025053008101151', 6, 1, 3980, '2025-05-30 08:25:17', NULL, '************', '微企付测试商户', '2025-05-30 08:20:17', NULL, NULL, 'CLOSED', 'APPTMS000100202505303506590005', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/CgxF2IRW');
INSERT INTO `sales_order_pay` VALUES ('2025053008101152', 6, 2, 3980, '2025-05-30 08:35:17', NULL, '************', '微企付测试商户', '2025-05-30 08:30:17', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://qr.alipay.com/bax04173zat3xt8mezw400e6');
INSERT INTO `sales_order_pay` VALUES ('2025053008101153', 7, 1, 1100, '2025-05-30 08:39:03', NULL, '************', '微企付测试商户', '2025-05-30 08:34:03', NULL, NULL, 'CLOSED', 'APPTMS000100202505303506590008', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/qEGVUkO6');
INSERT INTO `sales_order_pay` VALUES ('2025053008101154', 8, 2, 990, '2025-05-30 08:43:13', NULL, '************', '微企付测试商户', '2025-05-30 08:38:13', NULL, NULL, 'USERPAYING', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://qr.alipay.com/bax064784pjulclplohn3032');
INSERT INTO `sales_order_pay` VALUES ('2025053009101155', 9, 1, 286300, '2025-05-30 09:39:23', NULL, '************', '微企付测试商户', '2025-05-30 09:34:23', NULL, NULL, 'SUCCESS', 'APPTMS000100202505303506590012', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/aOGrXCX8');
INSERT INTO `sales_order_pay` VALUES ('2025053009101156', 11, 1, 230300, '2025-05-30 09:50:12', NULL, '************', '微企付测试商户', '2025-05-30 09:45:12', NULL, NULL, 'CLOSED', 'APPTMS000100202505303506590014', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/kFy9XeIi');
INSERT INTO `sales_order_pay` VALUES ('2025053011101157', 11, 2, 230300, '2025-05-30 11:22:22', NULL, '************', '微企付测试商户', '2025-05-30 11:17:22', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://qr.alipay.com/bax07530misxcow64lf13089');
INSERT INTO `sales_order_pay` VALUES ('2025053011101160', 14, 1, 64500, '2025-05-30 11:54:16', NULL, '************', '微企付测试商户', '2025-05-30 11:49:16', NULL, NULL, 'CLOSED', 'APPTMS000100202505303506590044', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/s7YVfCyZ');
INSERT INTO `sales_order_pay` VALUES ('2025053011101161', 13, 2, 55000, '2025-05-30 11:55:34', NULL, '************', '微企付测试商户', '2025-05-30 11:50:34', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://qr.alipay.com/bax040230c5c5trzfgio3020');
INSERT INTO `sales_order_pay` VALUES ('2025053015101164', 15, 1, 39000, '2025-05-30 15:34:26', NULL, '************', '微企付测试商户', '2025-05-30 15:29:26', NULL, NULL, 'SUCCESS', 'APPTMS000100202505303506590066', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/PrBF0itm');
INSERT INTO `sales_order_pay` VALUES ('2025061310101174', 28, 2, 6930, '2025-06-13 10:57:09', NULL, '************', '微企付测试商户', '2025-06-13 10:52:09', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://qr.alipay.com/bax08065yqhkuzwypfdu25ec');
INSERT INTO `sales_order_pay` VALUES ('2025061310101175', 16, 1, 17500, '2025-06-13 10:57:49', NULL, '************', '微企付测试商户', '2025-06-13 10:52:49', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880042', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/MaIh8O2K');
INSERT INTO `sales_order_pay` VALUES ('2025061311101123', 17, 1, 9900, '2025-06-13 11:14:52', NULL, '************', '微企付测试商户', '2025-06-13 11:09:52', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880063', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/8tXpnSfd');
INSERT INTO `sales_order_pay` VALUES ('2025061311101176', 35, 1, 990, '2025-06-13 11:49:12', NULL, '************', '微企付测试商户', '2025-06-13 11:44:12', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880082', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/zOh7E2yq');
INSERT INTO `sales_order_pay` VALUES ('2025061311101177', 36, 1, 9900, '2025-06-13 11:49:50', NULL, '************', '微企付测试商户', '2025-06-13 11:44:50', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880084', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/8rtVK4ej');
INSERT INTO `sales_order_pay` VALUES ('2025061311101178', 37, 1, 9900, '2025-06-13 11:56:17', NULL, '************', '微企付测试商户', '2025-06-13 11:51:17', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880094', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/4RrqNRpj');
INSERT INTO `sales_order_pay` VALUES ('2025061311101179', 36, 1, 9900, '2025-06-13 11:56:48', NULL, '************', '微企付测试商户', '2025-06-13 11:51:48', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880096', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/8bzWKjOq');
INSERT INTO `sales_order_pay` VALUES ('2025061312101180', 35, 1, 990, '2025-06-13 12:16:08', NULL, '************', '微企付测试商户', '2025-06-13 12:11:08', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880098', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/HWX71lSC');
INSERT INTO `sales_order_pay` VALUES ('2025061313101181', 37, 1, 9900, '2025-06-13 13:19:41', NULL, '************', '微企付测试商户', '2025-06-13 13:14:41', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880106', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/KYJwM2Wc');
INSERT INTO `sales_order_pay` VALUES ('2025061314101182', 38, 2, 110000, '2025-06-13 14:57:35', NULL, '9', '明意湖集团', '2025-06-13 14:52:35', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://qr.alipay.com/bax09489bruz2tlis0so007b');
INSERT INTO `sales_order_pay` VALUES ('2025061314101183', 39, 1, 16000, '2025-06-13 14:59:52', NULL, '************', '微企付测试商户', '2025-06-13 14:54:52', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880184', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/Y3c3rEeG');
INSERT INTO `sales_order_pay` VALUES ('2025061315101124', 40, 1, 6930, '2025-06-13 15:42:24', NULL, '************', '微企付测试商户', '2025-06-13 15:37:24', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880222', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/H79smrXg');
INSERT INTO `sales_order_pay` VALUES ('2025061315101125', 40, 1, 6930, '2025-06-13 15:48:17', NULL, '************', '微企付测试商户', '2025-06-13 15:43:17', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880226', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/zJM3uR9s');
INSERT INTO `sales_order_pay` VALUES ('2025061315101184', 35, 1, 990, '2025-06-13 15:07:22', NULL, '************', '微企付测试商户', '2025-06-13 15:02:22', NULL, NULL, 'CLOSED', 'APPTMS000100202506133506880190', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/OX6x6s15');
INSERT INTO `sales_order_pay` VALUES ('2025061617101185', 42, 1, 5600, '2025-06-16 17:05:17', NULL, '************', '微企付测试商户', '2025-06-16 17:00:17', NULL, NULL, 'CLOSED', 'APPTMS000100202506163506970225', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/fzH18t8o');
INSERT INTO `sales_order_pay` VALUES ('2025061617101186', 43, 1, 3000, '2025-06-16 17:05:54', NULL, '************', '微企付测试商户', '2025-06-16 17:00:54', NULL, NULL, 'CLOSED', 'APPTMS000100202506163506970226', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/oINaPeDZ');
INSERT INTO `sales_order_pay` VALUES ('2025061617101187', 44, 1, 3400, '2025-06-16 17:33:47', NULL, '************', '微企付测试商户', '2025-06-16 17:28:47', NULL, NULL, 'SUCCESS', 'APPTMS000100202506163506970243', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/3SrOmbG2');
INSERT INTO `sales_order_pay` VALUES ('2025061709101188', 45, 1, 20970, '2025-06-17 09:56:10', NULL, '************', '微企付测试商户', '2025-06-17 09:51:10', NULL, NULL, 'CLOSED', 'APPTMS000100202506173506990006', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/OjbhDF2W');
INSERT INTO `sales_order_pay` VALUES ('2025061710101189', 45, 1, 20970, '2025-06-17 10:08:54', NULL, '************', '微企付测试商户', '2025-06-17 10:03:54', NULL, NULL, 'SUCCESS', 'APPTMS000100202506173506990007', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/8kjq9BYN');
INSERT INTO `sales_order_pay` VALUES ('2025061815101190', 35, 1, 990, '2025-06-18 15:19:42', NULL, '************', '微企付测试商户', '2025-06-18 15:14:42', NULL, NULL, 'CLOSED', 'APPTMS000100202506183507012193', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://sqf.qq.com/Llq75YTR');
INSERT INTO `sales_order_pay` VALUES ('2025061815101191', 47, 2, 5100, '2025-06-18 15:20:11', NULL, '9', '明意湖集团', '2025-06-18 15:15:11', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, NULL, NULL, 'https://qr.alipay.com/bax05951onhrjjoxc2cv5521');

-- ----------------------------
-- Table structure for shopping_cart
-- ----------------------------
DROP TABLE IF EXISTS `shopping_cart`;
CREATE TABLE `shopping_cart`  (
  `cart_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
  `open_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `sku_id` bigint(20) NULL DEFAULT NULL COMMENT 'SKU ID',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '数量',
  `selected` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否选中：0-未选中，1-选中',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`cart_id`) USING BTREE,
  UNIQUE INDEX `uk_user_product_sku`(`open_id`, `product_id`, `sku_id`) USING BTREE,
  INDEX `idx_open_id`(`open_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '购物车表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shopping_cart
-- ----------------------------

-- ----------------------------
-- Table structure for sys_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log`  (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) NULL DEFAULT NULL COMMENT '操作用户ID',
  `dept_id` int(11) NULL DEFAULT NULL COMMENT '用户所属集团ID',
  `username` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `operation_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型(LOGIN,LOGOUT,CREATE,UPDATE,DELETE,QUERY)',
  `module_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块名称',
  `operation_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作描述',
  `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求方法',
  `request_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求URL',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数',
  `response_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '响应结果',
  `ip_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户代理',
  `execute_time` int(11) NULL DEFAULT NULL COMMENT '执行时间(毫秒)',
  `status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'SUCCESS' COMMENT '状态(SUCCESS-成功,FAILED-失败)',
  `error_message` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`log_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_dept_id`(`dept_id`) USING BTREE,
  INDEX `idx_operation_type`(`operation_type`) USING BTREE,
  INDEX `idx_module_name`(`module_name`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_operation_log
-- ----------------------------

-- ----------------------------
-- Table structure for user_merchant_permission
-- ----------------------------
DROP TABLE IF EXISTS `user_merchant_permission`;
CREATE TABLE `user_merchant_permission`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `permission_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'read' COMMENT '权限类型(read-只读,write-读写,admin-管理)',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_merchant`(`user_id`, `merchant_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_merchant_id`(`merchant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户商户权限关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_merchant_permission
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
