package com.mn.constants;

public class WxConstants {

    public static final String urlGetOpenId = "https://api.weixin.qq.com/sns/oauth2/access_token?grant_type=authorization_code";

    public static final String get_open_id = "https://api.weixin.qq.com/sns/jscode2session?";

    public static final String getTicket = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?type=jsapi&access_token=";

    public static final String getAccessToken  = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential";

    public static final String getUserInfo = "https://api.weixin.qq.com/sns/userinfo?access_token=";

    // 发送微信订阅消息链接【一次性订阅消息】
    public static final String sendSubscribeMsg = "https://api.weixin.qq.com/cgi-bin/message/template/subscribe?access_token=";
    // 订阅消息模板id
    public static final String subTemplateId = "q_C5qqnqgiWwyETimS_2loO2L30TgU8H6JweE81ZXjw";

    // 订阅模板消息链接【订阅通知】
    public static final String sendSubTemplateMsg = "https://api.weixin.qq.com/cgi-bin/message/subscribe/bizsend?access_token=";

    // 发送微信模板消息链接
    public static final String sendTemplateMsg = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=";

    public static final String unifiedorder = "https://api.mch.weixin.qq.com/pay/unifiedorder";

    public static final String orderquery = "https://api.mch.weixin.qq.com/pay/orderquery";

    public static final String closeorder = "https://api.mch.weixin.qq.com/pay/closeorder";

}
