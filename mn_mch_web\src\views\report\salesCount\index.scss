.finance-report-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);

  .page-header {
    margin-bottom: 20px;
    text-align: center;

    h2 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 24px;
      font-weight: 500;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .filter-card {
    margin-bottom: 20px;

    .el-form {
      margin-bottom: 0;
    }
  }

  .overview-row {
    margin-bottom: 20px;

    .overview-card {
      height: 120px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .overview-content {
        display: flex;
        align-items: center;
        height: 100%;
        padding: 20px;

        .overview-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;

          i {
            font-size: 24px;
            color: white;
          }
        }

        .overview-info {
          flex: 1;

          .overview-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
          }

          .overview-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
          }

          .overview-desc {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-weight: bold;
        color: #333;
        font-size: 16px;
      }
    }

    .el-table {
      .el-table__header {
        th {
          background-color: #fafafa;
          color: #333;
          font-weight: bold;
        }
      }

      .el-table__body {
        tr:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .finance-report-container {
    .overview-row {
      .el-col {
        margin-bottom: 10px;
      }
    }
  }
}

@media (max-width: 768px) {
  .finance-report-container {
    padding: 10px;

    .overview-row {
      .overview-card {
        height: auto;

        .overview-content {
          flex-direction: column;
          text-align: center;
          padding: 15px;

          .overview-icon {
            margin-right: 0;
            margin-bottom: 10px;
          }
        }
      }
    }

    .table-card {
      .el-table {
        font-size: 12px;
      }
    }
  }
}