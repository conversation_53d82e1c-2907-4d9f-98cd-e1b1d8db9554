<view class="payment-container">
  <!-- 顶部导航 -->
  <view class="nav-bar">
    <view class="back-icon" bindtap="navigateBack">
      <image src="/assets/icons/back.png" mode="aspectFit"></image>
    </view>
    <view class="title">商户收款</view>
  </view>

  <!-- 主要内容区域 -->
  <view class="content-area">
    <!-- 商户信息卡片 -->
    <view class="merchant-card">
      <view class="merchant-header">
        <view class="merchant-logo">
          <image src="/assets/icons/merchant.png" mode="aspectFit"></image>
        </view>
        <view class="merchant-details">
          <text class="merchant-name">{{merchantName}}</text>
        </view>
      </view>
    </view>

    <!-- 支付信息卡片 -->
    <view class="payment-card">
      <!-- 金额输入区 -->
      <view class="amount-section">
        <view class="amount-label">付款金额</view>
        <view class="amount-input">
          <text class="currency-symbol">¥</text>
          <input 
            type="digit" 
            placeholder="请输入金额" 
            focus="{{true}}" 
            bindinput="handleInputAmount" 
            value="{{amount}}"
            class="amount-value" 
          />
        </view>
      </view>

      <!-- 备注区域 -->
      <view class="remark-section" bindtap="handleAddRemark">
        <view class="remark-label">添加备注</view>
        <view class="remark-content">
          <text wx:if="{{remark}}">{{remark}}</text>
          <text wx:else class="remark-placeholder">选填</text>
          <image class="arrow-icon" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 底部付款按钮 -->
    <view class="bottom-btn-area safe-area-bottom">
      <button class="payment-btn {{amount ? 'active' : 'disabled'}}" bindtap="handlePayment">
        确认付款 ¥{{amount || '0.00'}}
      </button>
    </view>

    <!-- 消息提示组件 -->
    <t-message id="t-message" />
  </view>
</view>