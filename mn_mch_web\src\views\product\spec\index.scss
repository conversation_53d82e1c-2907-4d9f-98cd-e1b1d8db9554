.app-container {
  .detail-title {
    margin-top: 20px;
    padding-left: 10px;
    font-size: 16px;
    font-weight: bold;
    border-left: 4px solid #409EFF;
  }

  .el-table {
    margin-top: 15px;
  }

  .text-center {
    text-align: center;
  }
}

.time-info {
  .time-detail {
    font-size: 12px;
    color: #909399;
    margin-top: 3px;
  }
}

// 规格详情样式
.spec-detail-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  
  .spec-basic-info {
    flex: 1;
    
    .spec-id, .spec-name, .spec-sort {
      font-size: 14px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      
      .label {
        color: #606266;
        margin-right: 8px;
        min-width: 80px;
      }
      
      .value {
        font-weight: 500;
        color: #303133;
      }
    }
    
    .spec-time {
      color: #606266;
      margin-bottom: 8px;
      font-size: 14px;
      
      i {
        margin-right: 6px;
        color: #909399;
      }
    }
  }
}

.spec-info-section {
  margin-bottom: 25px;

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    position: relative;
    
    i {
      font-size: 18px;
      margin-right: 8px;
      color: #409EFF;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }

  .spec-values-container {
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    min-height: 60px;
  }
}

.float-right {
  float: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-10 {
  margin-bottom: 10px;
}