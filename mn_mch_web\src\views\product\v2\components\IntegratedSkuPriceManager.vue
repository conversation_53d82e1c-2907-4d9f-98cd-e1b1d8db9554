<template>
  <div class="integrated-sku-price-manager">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- SKU管理标签页 -->
      <el-tab-pane label="SKU管理" name="sku">
        <div class="sku-management">
          <!-- 规格选择区域 -->
          <div class="spec-selection-area" v-if="selectedSpecs.length > 0">
            <el-alert
              title="选择商品规格，系统将自动生成规格组合"
              type="info"
              :closable="false"
              show-icon
              class="mb-15"
            />
            <div class="spec-select-container">
              <el-form :inline="true" size="small">
                <el-form-item label="已选规格:">
                  <el-tag
                    v-for="spec in selectedSpecs"
                    :key="spec.specId"
                    type="primary"
                    class="spec-tag"
                  >
                    {{ spec.specName }}
                  </el-tag>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="generateSkuCombinations" :disabled="loading">
                    <i class="el-icon-magic-stick"></i> 生成SKU组合
                  </el-button>
                  <el-button @click="addSku" v-if="skuList.length > 0">
                    <i class="el-icon-plus"></i> 手动添加SKU
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <!-- SKU列表 -->
          <el-table
            v-loading="loading"
            :data="skuList"
            border
            size="small"
            stripe
            highlight-current-row
            style="width: 100%"
            v-if="skuList.length > 0"
          >
            <el-table-column label="SKU编码" align="center" prop="skuCode" min-width="120">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.skuCode"
                  placeholder="请输入SKU编码"
                  size="mini"
                  @change="() => markSkuCodeAsEdited(scope.row)"
                />
              </template>
            </el-table-column>

            <!-- 规格值列 - 动态生成 -->
            <el-table-column
              v-for="(spec, index) in specColumns"
              :key="index"
              :label="spec.specName"
              align="center"
              min-width="100"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.specDataObj && scope.row.specDataObj[index]"
                  v-model="scope.row.specDataObj[index].value"
                  size="mini"
                  placeholder="请输入规格值"
                  @change="handleSpecValueChange(scope.row)"
                />
                <span v-else>-</span>
              </template>
            </el-table-column>

            <el-table-column label="价格(元)" align="center" width="100">
              <template slot-scope="scope">
                <el-input-number
                  v-model="scope.row.price"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  controls-position="right"
                  size="mini"
                  @change="handleSkuDataChange"
                />
              </template>
            </el-table-column>

            <el-table-column label="成本价(元)" align="center" width="100">
              <template slot-scope="scope">
                <el-input-number
                  v-model="scope.row.costPrice"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  controls-position="right"
                  size="mini"
                  @change="handleSkuDataChange"
                />
              </template>
            </el-table-column>

            <el-table-column label="库存" align="center" width="80">
              <template slot-scope="scope">
                <el-input-number
                  v-model="scope.row.stock"
                  :min="0"
                  controls-position="right"
                  size="mini"
                  @change="handleSkuDataChange"
                />
              </template>
            </el-table-column>

            <el-table-column label="状态" align="center" width="60">
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  @change="handleSkuDataChange"
                />
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" width="60">
              <template slot-scope="scope">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  size="mini"
                  @click="removeSku(scope.$index)"
                />
              </template>
            </el-table-column>
          </el-table>

          <!-- 批量设置 -->
          <div class="batch-setting mt-10" v-if="skuList.length > 0">
            <el-divider content-position="left">批量设置</el-divider>
            <el-form :inline="true" class="batch-form" size="mini">
              <el-form-item label="价格(元)">
                <el-input-number
                  v-model="batchSettings.price"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  controls-position="right"
                  size="mini"
                />
              </el-form-item>
              <el-form-item label="成本价(元)">
                <el-input-number
                  v-model="batchSettings.costPrice"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  controls-position="right"
                  size="mini"
                />
              </el-form-item>
              <el-form-item label="库存">
                <el-input-number
                  v-model="batchSettings.stock"
                  :min="0"
                  controls-position="right"
                  size="mini"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="mini" @click="applyBatchSettings">应用</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 空状态 -->
          <div v-if="skuList.length === 0" class="empty-state">
            <el-empty description="暂无SKU数据">
              <el-button type="primary" @click="generateSkuCombinations" v-if="selectedSpecs.length > 0">
                生成SKU组合
              </el-button>
            </el-empty>
          </div>
        </div>
      </el-tab-pane>

      <!-- 等级价格标签页 -->
      <el-tab-pane label="等级价格" name="level">
        <div class="level-price-management">
          <el-alert
            title="为不同客户等级设置专属价格，可以设置具体价格或基于基础价格的折扣率"
            type="info"
            :closable="false"
            show-icon
            class="mb-15"
          />
          
          <div class="level-price-content">
            <div class="level-selector">
              <span>选择客户等级：</span>
              <el-select v-model="selectedLevelId" placeholder="请选择客户等级" @change="loadLevelPrices">
                <el-option
                  v-for="level in customerLevels"
                  :key="level.levelId"
                  :label="level.levelName"
                  :value="level.levelId"
                />
              </el-select>
            </div>

            <div v-if="selectedLevelId && skuList.length > 0" class="level-price-table">
              <el-table :data="levelPriceData" border stripe>
                <el-table-column prop="skuCode" label="SKU编码" width="150" />
                <el-table-column label="规格组合" min-width="200">
                  <template slot-scope="scope">
                    <div class="spec-combination">
                      <el-tag 
                        v-for="spec in scope.row.specCombination" 
                        :key="spec.specId"
                        size="small"
                        class="spec-tag"
                      >
                        {{ spec.specName }}: {{ spec.value }}
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="basePrice" label="基础价格(分)" width="120" />
                <el-table-column label="等级价格(分)" width="150">
                  <template slot-scope="scope">
                    <el-input-number 
                      v-model="scope.row.levelPrice" 
                      :min="0"
                      size="small"
                      @change="handleLevelPriceChange(scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="折扣率" width="120">
                  <template slot-scope="scope">
                    <span>{{ calculateDiscountRate(scope.row.basePrice, scope.row.levelPrice) }}%</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 阶梯价格标签页 -->
      <el-tab-pane label="阶梯价格" name="tier">
        <div class="tier-price-management">
          <el-alert
            title="设置数量阶梯价格，购买数量越多价格越优惠"
            type="info"
            :closable="false"
            show-icon
            class="mb-15"
          />
          
          <div class="tier-price-content">
            <div class="tier-selector">
              <span>选择客户等级：</span>
              <el-select v-model="selectedTierLevelId" placeholder="请选择客户等级" @change="loadTierPrices">
                <el-option
                  v-for="level in customerLevels"
                  :key="level.levelId"
                  :label="level.levelName"
                  :value="level.levelId"
                />
              </el-select>
              
              <el-divider direction="vertical" />
              
              <span>选择SKU：</span>
              <el-select v-model="selectedTierSkuId" placeholder="请选择SKU" @change="loadTierPrices">
                <el-option
                  v-for="sku in skuList"
                  :key="sku.skuId"
                  :label="sku.skuCode"
                  :value="sku.skuId"
                />
              </el-select>
            </div>

            <div v-if="selectedTierLevelId && selectedTierSkuId" class="tier-price-table">
              <div class="tier-toolbar">
                <el-button type="primary" size="small" @click="addTierPrice">
                  <i class="el-icon-plus"></i> 添加阶梯
                </el-button>
              </div>

              <el-table :data="tierPriceData" border stripe>
                <el-table-column label="最小数量" width="120">
                  <template slot-scope="scope">
                    <el-input-number 
                      v-model="scope.row.minQuantity" 
                      :min="1"
                      size="small"
                      @change="handleTierPriceChange"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="最大数量" width="120">
                  <template slot-scope="scope">
                    <el-input-number 
                      v-model="scope.row.maxQuantity" 
                      :min="scope.row.minQuantity"
                      size="small"
                      @change="handleTierPriceChange"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="阶梯价格(分)" width="150">
                  <template slot-scope="scope">
                    <el-input-number 
                      v-model="scope.row.tierPrice" 
                      :min="0"
                      size="small"
                      @change="handleTierPriceChange"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template slot-scope="scope">
                    <el-button 
                      type="text" 
                      size="small" 
                      @click="removeTierPrice(scope.$index)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>


  </div>
</template>

<script>
export default {
  name: "IntegratedSkuPriceManager",
  props: {
    selectedSpecs: {
      type: Array,
      default: () => []
    },
    customerLevels: {
      type: Array,
      default: () => []
    },
    productId: {
      type: [String, Number],
      required: true
    },
    existingSkus: {
      type: Array,
      default: () => []
    },
    existingBasePrices: {
      type: Array,
      default: () => []
    },
    existingLevelPrices: {
      type: Array,
      default: () => []
    },
    existingTierPrices: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeTab: 'sku',
      loading: false,
      skuList: [],
      specColumns: [],
      selectedLevelId: null,
      selectedTierLevelId: null,
      selectedTierSkuId: null,
      levelPriceData: [],
      tierPriceData: [],
      // 批量设置
      batchSettings: {
        price: 0,
        costPrice: 0,
        stock: 0
      }
    };
  },
  watch: {
    selectedSpecs: {
      handler(newVal, oldVal) {
        console.log('SKU组件-规格变化:', JSON.stringify({
          newCount: newVal ? newVal.length : 0,
          oldCount: oldVal ? oldVal.length : 0,
          newSpecs: newVal ? newVal.map(s => ({id: s.specId, name: s.specName})) : []
        }));
        this.initializeSkus();
      },
      immediate: true,
      deep: true
    },
    existingSkus: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.skuList = [...newVal];
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 初始化SKU */
    initializeSkus() {
      // 更新规格列
      this.updateSpecColumns();

      // 如果有现有SKU数据，使用现有数据
      if (this.existingSkus && this.existingSkus.length > 0) {
        this.skuList = [...this.existingSkus];
        // 从现有SKU中提取规格列信息
        this.extractSpecColumnsFromSkus();
      } else {
        this.skuList = [];
      }
    },
    /** 更新规格列 */
    updateSpecColumns() {
      if (!this.selectedSpecs || this.selectedSpecs.length === 0) {
        this.specColumns = [];
        return;
      }

      this.specColumns = this.selectedSpecs.map(spec => ({
        specId: spec.specId,
        specName: spec.specName
      }));

      console.log('规格列更新:', JSON.stringify({
        count: this.specColumns.length,
        columns: this.specColumns
      }));
    },
    /** 从现有SKU中提取规格列信息 */
    extractSpecColumnsFromSkus() {
      if (!this.skuList || this.skuList.length === 0) {
        return;
      }

      // 从第一个SKU中提取规格信息
      const firstSku = this.skuList[0];
      if (!firstSku.specDataObj) {
        return;
      }

      const specs = [];
      // 如果specDataObj是数组形式
      if (Array.isArray(firstSku.specDataObj)) {
        firstSku.specDataObj.forEach(spec => {
          if (spec.specId && spec.specName) {
            specs.push({
              specId: spec.specId,
              specName: spec.specName
            });
          }
        });
      }

      if (specs.length > 0) {
        this.specColumns = specs;
        console.log('从现有SKU提取的规格列:', this.specColumns);
      }
    },
    /** 生成SKU组合 */
    generateSkuCombinations() {
      console.log('开始生成SKU组合:', JSON.stringify({
        specsCount: this.selectedSpecs ? this.selectedSpecs.length : 0,
        specs: this.selectedSpecs ? this.selectedSpecs.map(s => ({id: s.specId, name: s.specName})) : []
      }));

      if (!this.selectedSpecs || this.selectedSpecs.length === 0) {
        this.$message.warning('请先选择商品规格');
        return;
      }

      this.loading = true;

      try {
        // 解析规格数据
        const specList = this.selectedSpecs.map(spec => {
          let specValues = [];
          try {
            specValues = JSON.parse(spec.specValues);
          } catch (e) {
            console.error('解析规格值失败:', spec.specValues, e);
            specValues = [];
          }

          return {
            specId: spec.specId,
            specName: spec.specName,
            specValues: specValues
          };
        });

        console.log('解析后的规格列表:', JSON.stringify(specList));

        // 保存当前已有的SKU数据
        const existingSkus = [...this.skuList];

        // 生成新的规格组合
        this.generateSkuCombinationsFromSpecs(specList, existingSkus);

        console.log('SKU生成完成:', JSON.stringify({
          generatedCount: this.skuList.length,
          specColumnsCount: this.specColumns.length
        }));

        this.$message.success(`成功生成 ${this.skuList.length} 个SKU组合`);

      } catch (error) {
        console.error('生成SKU组合失败:', error);
        this.$message.error('生成SKU组合失败，请检查规格数据');
      } finally {
        this.loading = false;
      }
    },

    /** 删除SKU */
    removeSku(index) {
      this.skuList.splice(index, 1);
      this.handleSkuDataChange();
    },

    /** 处理SKU数据变化 */
    handleSkuDataChange() {
      this.$emit('sku-data-change', this.skuList);
    },

    /** 加载等级价格 */
    loadLevelPrices() {
      // 等级价格加载逻辑将在下一步实现
      console.log('加载等级价格，等级ID:', this.selectedLevelId);
    },
    /** 处理等级价格变化 */
    handleLevelPriceChange(row) {
      // 等级价格变化处理逻辑将在下一步实现
      console.log('等级价格变化:', row);
    },
    /** 计算折扣率 */
    calculateDiscountRate(basePrice, levelPrice) {
      if (!basePrice || !levelPrice) return 0;
      return ((levelPrice / basePrice) * 100).toFixed(2);
    },
    /** 加载阶梯价格 */
    loadTierPrices() {
      // 阶梯价格加载逻辑将在下一步实现
      console.log('加载阶梯价格，等级ID:', this.selectedTierLevelId, 'SKU ID:', this.selectedTierSkuId);
    },
    /** 添加阶梯价格 */
    addTierPrice() {
      this.tierPriceData.push({
        minQuantity: 1,
        maxQuantity: null,
        tierPrice: 0
      });
    },
    /** 删除阶梯价格 */
    removeTierPrice(index) {
      this.tierPriceData.splice(index, 1);
      this.handleTierPriceChange();
    },
    /** 处理阶梯价格变化 */
    handleTierPriceChange() {
      // 阶梯价格变化处理逻辑将在下一步实现
      console.log('阶梯价格变化:', this.tierPriceData);
    },
    /** 从规格列表生成SKU组合 */
    generateSkuCombinationsFromSpecs(specList, existingSkus = []) {
      // 创建一个映射来存储已有的SKU，以规格组合字符串为键
      const existingSkuMap = new Map();

      // 处理已有的SKU
      existingSkus.forEach(sku => {
        if (sku.specDataObj) {
          // 创建一个规格组合的唯一标识
          const specKey = this.createSpecKey(sku.specDataObj);
          existingSkuMap.set(specKey, sku);
        }
      });

      // 生成新的SKU列表
      const newSkuList = [];

      const generateCombinations = (specs, current = [], index = 0) => {
        if (index === specs.length) {
          // 创建规格组合的唯一标识
          const specDataObj = current.map(item => ({
            specId: item.specId,
            specName: item.specName,
            value: item.value
          }));

          const specKey = this.createSpecKey(specDataObj);

          // 检查是否已存在相同规格组合的SKU
          if (existingSkuMap.has(specKey)) {
            // 使用已有的SKU
            const existingSku = existingSkuMap.get(specKey);
            newSkuList.push(existingSku);
            // 从映射中移除，以便后续知道哪些已不再需要
            existingSkuMap.delete(specKey);
          } else {
            // 创建新的SKU
            const newSku = {
              productId: this.productId,
              skuCode: this.generateIntelligentSkuCode(specDataObj),
              price: 0,
              costPrice: 0,
              stock: 0,
              status: true,
              specDataObj: specDataObj
            };

            // 将规格数据转为JSON字符串
            newSku.specData = JSON.stringify(newSku.specDataObj);

            newSkuList.push(newSku);
          }
          return;
        }

        const currentSpec = specs[index];
        currentSpec.specValues.forEach(value => {
          generateCombinations(specs, [...current, {
            specId: currentSpec.specId,
            specName: currentSpec.specName,
            value: value
          }], index + 1);
        });
      };

      generateCombinations(specList);

      // 更新SKU列表
      this.skuList = newSkuList;

      // 更新规格列
      this.specColumns = specList.map(spec => ({
        specId: spec.specId,
        specName: spec.specName
      }));


    },
    /** 创建规格组合的唯一标识 */
    createSpecKey(specDataObj) {
      if (Array.isArray(specDataObj)) {
        // 如果是数组形式，按规格ID和值排序后拼接
        return specDataObj
          .sort((a, b) => a.specId - b.specId)
          .map(spec => `${spec.specId}:${spec.value}`)
          .join('|');
      } else {
        // 如果是对象形式，按键排序后拼接
        return Object.keys(specDataObj)
          .sort()
          .map(key => `${key}:${specDataObj[key]}`)
          .join('|');
      }
    },
    /** 添加SKU */
    addSku() {
      if (this.selectedSpecs.length === 0) {
        this.$message.warning("请先选择规格");
        return;
      }

      // 创建一个新的SKU对象
      const specDataObj = [];

      // 为每个规格添加默认值
      this.specColumns.forEach(spec => {
        specDataObj.push({
          specId: spec.specId,
          specName: spec.specName,
          value: ''  // 默认为空，用户需要手动填写
        });
      });

      const newSku = {
        productId: this.productId,
        skuCode: this.generateIntelligentSkuCode(specDataObj),
        price: 0,
        costPrice: 0,
        stock: 0,
        status: true,
        specDataObj: specDataObj
      };

      // 将规格数据转为JSON字符串
      newSku.specData = JSON.stringify(newSku.specDataObj);

      this.skuList.push(newSku);
      this.handleSkuDataChange();
    },
    /** 生成智能SKU编码 */
    generateIntelligentSkuCode(specDataObj) {
      // 如果没有商品ID，使用时间戳
      if (!this.productId) {
        return `SKU${Date.now().toString().slice(-6)}`;
      }

      // 基础前缀：商品ID
      let skuCode = this.productId.toString();

      // 如果规格数据为空，直接返回商品ID加序号
      if (!specDataObj || specDataObj.length === 0) {
        return `${skuCode}-${this.skuList.length + 1}`;
      }

      // 从规格值中提取信息
      const specCodes = [];

      specDataObj.forEach(spec => {
        if (spec.value) {
          // 尝试提取规格值的首字母或首个字符
          let specCode = '';

          // 如果是中文，取第一个字符
          if (/[\u4e00-\u9fa5]/.test(spec.value)) {
            specCode = spec.value.charAt(0);
          }
          // 如果是英文，取首字母并大写
          else if (/[a-zA-Z]/.test(spec.value)) {
            // 提取所有单词的首字母
            specCode = spec.value
              .split(/\s+/)
              .map(word => word.charAt(0).toUpperCase())
              .join('');
          }
          // 如果是数字，直接使用
          else if (/^\d+$/.test(spec.value)) {
            specCode = spec.value;
          }
          // 其他情况，取前两个字符
          else {
            specCode = spec.value.substring(0, 2);
          }

          if (specCode) {
            specCodes.push(specCode);
          }
        }
      });

      // 如果提取到了规格编码，添加到SKU编码中
      if (specCodes.length > 0) {
        skuCode += `-${specCodes.join('')}`;
      } else {
        // 如果没有提取到规格编码，添加序号
        skuCode += `-${this.skuList.length + 1}`;
      }

      return skuCode;
    },
    /** 标记SKU编码为手动编辑过 */
    markSkuCodeAsEdited(sku) {
      sku.skuCodeManuallyEdited = true;
    },
    /** 处理规格值变更 */
    handleSpecValueChange(sku) {
      // 更新规格数据JSON字符串
      sku.specData = JSON.stringify(sku.specDataObj);

      // 如果SKU编码是自动生成的（没有手动修改过），则更新SKU编码
      if (!sku.skuCodeManuallyEdited) {
        sku.skuCode = this.generateIntelligentSkuCode(sku.specDataObj);
      }

      this.handleSkuDataChange();
    },
    /** 应用批量设置 */
    applyBatchSettings() {
      if (this.skuList.length === 0) {
        return;
      }

      this.skuList.forEach(sku => {
        if (this.batchSettings.price > 0) {
          sku.price = this.batchSettings.price;
        }
        if (this.batchSettings.costPrice > 0) {
          sku.costPrice = this.batchSettings.costPrice;
        }
        if (this.batchSettings.stock > 0) {
          sku.stock = this.batchSettings.stock;
        }
      });

      this.handleSkuDataChange();
      this.$message.success("批量设置已应用");
    }
  }
};
</script>

<style lang="scss" scoped>
.integrated-sku-price-manager {
  .mb-15 {
    margin-bottom: 15px;
  }

  .mt-10 {
    margin-top: 10px;
  }

  .spec-selection-area {
    margin-bottom: 20px;

    .spec-select-container {
      margin-top: 10px;
    }
  }

  .spec-tag {
    margin-right: 5px;
    margin-bottom: 2px;
  }

  .batch-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .el-form-item {
      margin-bottom: 10px;
      margin-right: 0;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }

  .level-selector,
  .tier-selector {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    span {
      margin-right: 10px;
      font-weight: 600;
    }
  }

  .tier-toolbar {
    margin-bottom: 15px;
  }

  .level-price-content,
  .tier-price-content {
    padding: 15px;
    background: #fafafa;
    border-radius: 4px;
  }

  // 调整表格内部元素的间距
  >>> .el-table {
    font-size: 12px;

    .el-input-number {
      width: 100%;
    }

    .el-input__inner {
      padding-left: 5px;
      padding-right: 5px;
    }
  }
}
</style>
