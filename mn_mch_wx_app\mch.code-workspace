{"folders": [{"path": "D:/mch"}, {"path": "."}], "settings": {"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "interactive", "java.format.settings.url": "", "java.format.settings.profile": "GoogleStyle", "java.format.enabled": true, "java.format.comments.enabled": true, "java.completion.enabled": true, "java.completion.guessMethodArguments": true, "java.completion.favoriteStaticMembers": ["org.junit.Assert.*", "org.junit.Assume.*", "org.junit.jupiter.api.Assertions.*", "org.junit.jupiter.api.Assumptions.*", "org.junit.jupiter.api.DynamicContainer.*", "org.junit.jupiter.api.DynamicTest.*", "org.mockito.Mockito.*", "org.mockito.ArgumentMatchers.*", "org.mockito.Answers.*"], "maven.terminal.useJavaHome": true, "spring-boot.ls.problem.application-properties.PROP_UNKNOWN_PROPERTY": "WARNING", "spring-boot.ls.problem.application-properties.PROP_INVALID_BEAN_NAVIGATION": "WARNING", "java.debug.settings.enableRunDebugCodeLens": true, "java.debug.settings.showHex": false, "java.debug.settings.showStaticVariables": true, "java.debug.settings.showQualifiedNames": false, "java.debug.settings.maxStringLength": 0, "files.associations": {"*.properties": "properties", "*.yml": "yaml", "*.yaml": "yaml"}, "git.confirmSync": false, "terminal.integrated.defaultProfile.windows": "Command Prompt", "workbench.panel.defaultLocation": "right", "workbench.sideBar.location": "left", "java.project.sourcePaths": ["mn_mch_manage/src/main/java"], "java.project.outputPath": "mn_mch_manage/target/classes", "java.project.referencedLibraries": ["mn_mch_manage/src/main/resources/lib/**/*.jar"]}}