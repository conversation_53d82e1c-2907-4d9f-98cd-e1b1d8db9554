import useToastBehavior from '~/behaviors/useToast';

Page({
  behaviors: [useToastBehavior],

  data: {
    isLoad: false,
    service: [],
    personalInfo: {},
    gridList: [
    ],

    settingList: [
      { name: '我的订单', iconClass: 'icon-order', type: 'order', url: '/pages/order/index' },
      { name: '收货地址', iconClass: 'icon-location', type: 'address' },
      { name: '账单明细', iconClass: 'icon-finance', type: 'finance' },
      { name: '联系客服', iconClass: 'icon-service', type: 'service' }
    ],
  },

  onLoad() {
    
  },

  async onShow() {

  },


  onLogin(e) {
    wx.navigateTo({
      url: '/pages/login/login',
    });
  },

  onNavigateTo() {
    wx.navigateTo({ url: `/pages/my/info-edit/index` });
  },

  onEleClick(e) {
    const { name, url, type } = e.currentTarget.dataset.data;
    if (url) {
      wx.navigateTo({ url });
      return;
    }

    // 处理不同类型的点击事件
    switch(type) {
      case 'address':
        this.onShowToast('#t-toast', '收货地址功能开发中');
        break;
      case 'finance':
        this.onShowToast('#t-toast', '账单明细功能开发中');
        break;
      case 'service':
        this.onShowToast('#t-toast', '客服功能开发中');
        break;
      default:
        this.onShowToast('#t-toast', name);
    }
  },
});
