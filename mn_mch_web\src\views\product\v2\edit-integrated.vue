<template>
  <div class="app-container">
    <el-card v-loading="pageLoading">
      <div slot="header" class="card-header">
        <span class="card-title">编辑商品</span>
        <div class="header-buttons">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">保存</el-button>
        </div>
      </div>

      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 基础信息 -->
        <el-divider content-position="left">
          <i class="el-icon-info"></i> 基础信息
        </el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入商品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品编码" prop="productCode">
              <el-input 
                v-model="form.productCode" 
                placeholder="请输入商品编码"
                @blur="checkProductCode"
              />
              <div v-if="codeCheckResult !== null" class="code-check-result">
                <i v-if="codeCheckResult" class="el-icon-success" style="color: #67c23a"></i>
                <i v-else class="el-icon-error" style="color: #f56c6c"></i>
                <span :style="{ color: codeCheckResult ? '#67c23a' : '#f56c6c' }">
                  {{ codeCheckResult ? '编码可用' : '编码已存在' }}
                </span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品分类" prop="categoryId">
              <el-select v-model="form.categoryId" placeholder="请选择商品分类" style="width: 100%">
                <el-option
                  v-for="category in categoryOptions"
                  :key="category.categoryId"
                  :label="category.categoryName"
                  :value="category.categoryId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="true">启用</el-radio>
                <el-radio :label="false">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="库存数量" prop="stock">
              <el-input-number
                v-model="form.stock"
                :min="0"
                :max="999999"
                placeholder="库存数量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="form.unit" placeholder="如：件、个、台等" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重量(kg)">
              <el-input-number
                v-model="form.weight"
                :min="0"
                :precision="2"
                placeholder="商品重量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="商品简介">
          <el-input
            v-model="form.productBrief"
            type="textarea"
            :rows="3"
            placeholder="请输入商品简介"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="排序">
          <el-input-number
            v-model="form.sortOrder"
            :min="0"
            :max="9999"
            placeholder="数值越大排序越靠前"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 规格选择 -->
        <el-divider content-position="left">
          <i class="el-icon-collection-tag"></i> 商品规格设置
        </el-divider>
        
        <el-form-item label="选择规格" prop="selectedSpecs">
          <el-alert
            title="请选择该商品需要的规格类型，系统将根据选择的规格自动生成SKU组合"
            type="info"
            :closable="false"
            show-icon
            class="mb-15"
          />
          <el-select
            v-model="form.selectedSpecs"
            multiple
            collapse-tags
            placeholder="请选择商品规格（如：颜色、尺寸、材质等）"
            style="width: 100%"
          >
            <el-option
              v-for="spec in productSpecs"
              :key="spec.specId"
              :label="spec.specName"
              :value="spec.specId"
            >
              <span style="float: left">{{ spec.specName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ spec.specDesc || '规格描述' }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 已选规格预览 -->
        <el-form-item v-if="form.selectedSpecs && form.selectedSpecs.length > 0">
          <div class="selected-specs-preview">
            <span class="preview-label">已选规格：</span>
            <el-tag
              v-for="specId in form.selectedSpecs"
              :key="specId"
              type="primary"
              class="spec-tag"
            >
              {{ getSpecName(specId) }}
            </el-tag>
            <el-button
              type="text"
              size="small"
              @click="clearSpecSelection"
              style="margin-left: 10px"
            >
              <i class="el-icon-refresh-left"></i> 重新选择
            </el-button>
          </div>
        </el-form-item>

        <!-- 调试信息 -->
        <el-form-item>
          <el-alert
            :title="`调试信息: selectedSpecs长度=${form.selectedSpecs.length}, 内容=${JSON.stringify(form.selectedSpecs)}, productSpecs长度=${productSpecs.length}`"
            type="info"
            :closable="false"
          />
        </el-form-item>

        <!-- 商品图片 -->
        <el-divider content-position="left">
          <i class="el-icon-picture"></i> 商品图片
        </el-divider>
        
        <el-form-item label="商品主图" prop="mainImage">
          <image-upload
            v-model="form.mainImage"
            :limit="1"
            :file-size="5"
            :file-type="['png', 'jpg', 'jpeg']"
          />
        </el-form-item>
        
        <el-form-item label="商品子图">
          <image-upload
            v-model="subImageList"
            :limit="9"
            :file-size="5"
            :file-type="['png', 'jpg', 'jpeg']"
            @input="handleSubImagesChange"
          />
        </el-form-item>

        <!-- 商品详情 -->
        <el-form-item label="商品详情">
          <editor v-model="form.productDetail" :min-height="300" />
        </el-form-item>

        <!-- SKU与价格管理 -->
        <el-divider content-position="left">
          <i class="el-icon-goods"></i> SKU与价格管理
        </el-divider>

        <div v-if="form.selectedSpecs.length > 0">
          <integrated-sku-price-manager
            :selected-specs="selectedSpecsData"
            :customer-levels="customerLevels"
            :product-id="productId"
            :existing-skus="form.skuList"
            :existing-base-prices="form.basePriceList"
            :existing-level-prices="form.levelPriceList"
            :existing-tier-prices="form.tierPriceList"
            @sku-data-change="handleSkuDataChange"
            @price-data-change="handlePriceDataChange"
          />
        </div>
        <div v-else>
          <el-alert
            title="请先选择商品规格"
            type="warning"
            :closable="false"
          />
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getProductV2, updateProductV2, checkProductV2Code } from "@/api/product/v2";
import { listCategory } from "@/api/product/category";
import { listSpec } from "@/api/product/spec";
import { listCustomerLevel } from "@/api/pricing/level";
import ImageUpload from "@/components/ImageUpload";
import Editor from "@/components/Editor";
import IntegratedSkuPriceManager from "./components/IntegratedSkuPriceManager";

export default {
  name: "ProductV2EditIntegrated",
  components: {
    ImageUpload,
    Editor,
    IntegratedSkuPriceManager
  },
  data() {
    return {
      loading: false,
      pageLoading: true,
      codeCheckResult: null,
      subImageList: [],
      categoryOptions: [],
      productSpecs: [],
      customerLevels: [],
      originalProductCode: '',
      form: {
        productId: null,
        deptId: null,
        productName: '',
        productCode: '',
        categoryId: null,
        productBrief: '',
        productDetail: '',
        mainImage: '',
        subImages: '',
        specData: '',
        selectedSpecs: [], // 选中的规格ID数组
        stock: 0,
        unit: '',
        weight: 0,
        status: true,
        sortOrder: 0,
        skuList: [],
        basePriceList: [],
        levelPriceList: [],
        tierPriceList: []
      },
      rules: {
        productName: [
          { required: true, message: "商品名称不能为空", trigger: "blur" },
          { min: 1, max: 100, message: "商品名称长度在 1 到 100 个字符", trigger: "blur" }
        ],
        productCode: [
          { required: true, message: "商品编码不能为空", trigger: "blur" },
          { min: 1, max: 50, message: "商品编码长度在 1 到 50 个字符", trigger: "blur" },
          { pattern: /^[a-zA-Z0-9_-]+$/, message: "商品编码只能包含字母、数字、下划线和横线", trigger: "blur" }
        ],
        categoryId: [
          { required: true, message: "请选择商品分类", trigger: "change" }
        ],
        stock: [
          { required: true, message: "库存数量不能为空", trigger: "blur" },
          { type: 'number', min: 0, message: "库存数量不能小于0", trigger: "blur" }
        ],
        mainImage: [
          { required: true, message: "请上传商品主图", trigger: "change" }
        ],
        status: [
          { required: true, message: "请选择商品状态", trigger: "change" }
        ],
        selectedSpecs: [
          { required: true, type: 'array', min: 1, message: "请至少选择一个商品规格", trigger: "change" }
        ]
      }
    };
  },
  computed: {
    /** 选中的规格数据 */
    selectedSpecsData() {
      if (!this.productSpecs || !this.form.selectedSpecs) {
        return [];
      }

      const result = this.productSpecs.filter(spec => {
        return this.form.selectedSpecs.includes(spec.specId);
      });

      console.log('selectedSpecsData计算结果:', JSON.stringify({
        selectedSpecIds: this.form.selectedSpecs,
        resultCount: result.length,
        resultSpecs: result.map(s => ({id: s.specId, name: s.specName}))
      }));

      return result;
    }
  },
  watch: {
    'form.selectedSpecs': {
      handler(newVal, oldVal) {
        console.log('watch-规格变化:', JSON.stringify({
          newSpecs: newVal,
          oldSpecs: oldVal,
          newCount: newVal ? newVal.length : 0,
          oldCount: oldVal ? oldVal.length : 0
        }));

        // 只有在真正变化时才处理
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.handleSpecSelectionChange(newVal);
        }
      },
      deep: true
    }
  },
  created() {
    this.productId = this.$route.params.productId;
    this.getCategoryList();
    this.getProductSpecs();
    this.getCustomerLevels();
    this.getProductInfo();
  },
  methods: {
    /** 获取分类列表 */
    getCategoryList() {
      listCategory().then(response => {
        this.categoryOptions = response.rows || [];
      });
    },
    /** 获取规格列表 */
    getProductSpecs() {
      listSpec().then(response => {
        this.productSpecs = response.rows || [];
      }).catch(error => {
        console.error('获取规格列表失败:', error);
      });
    },
    /** 获取客户等级列表 */
    getCustomerLevels() {
      listCustomerLevel().then(response => {
        this.customerLevels = response.rows || [];
      });
    },
    /** 获取商品信息 */
    getProductInfo() {
      getProductV2(this.productId).then(response => {
        console.log('整合版本 - 获取商品信息响应:', response.data);
        this.form = { ...response.data };
        this.originalProductCode = this.form.productCode;

        console.log('整合版本 - 设置后的form.skuList:', this.form.skuList);
        console.log('整合版本 - 设置后的form.basePriceList:', this.form.basePriceList);

        // 处理子图片
        if (this.form.subImages) {
          try {
            this.subImageList = JSON.parse(this.form.subImages);
          } catch (e) {
            this.subImageList = [];
          }
        }

        // 处理规格数据
        if (this.form.specData) {
          try {
            const specData = JSON.parse(this.form.specData);
            this.form.selectedSpecs = specData.selectedSpecs || [];
            console.log('整合版本 - 从specData解析的selectedSpecs:', this.form.selectedSpecs);
          } catch (e) {
            this.form.selectedSpecs = [];
          }
        } else {
          this.form.selectedSpecs = [];
        }

        this.pageLoading = false;
      }).catch(() => {
        this.pageLoading = false;
      });
    },
    /** 检查商品编码 */
    checkProductCode() {
      if (!this.form.productCode || this.form.productCode === this.originalProductCode) {
        this.codeCheckResult = null;
        return;
      }

      checkProductV2Code(this.form.productCode).then(response => {
        this.codeCheckResult = response.data;
      }).catch(() => {
        this.codeCheckResult = false;
      });
    },
    /** 处理子图片变化 */
    handleSubImagesChange(imageList) {
      this.form.subImages = JSON.stringify(imageList);
    },
    /** 处理规格选择变化 */
    handleSpecSelectionChange(selectedSpecs) {
      console.log('规格选择变化:', JSON.stringify({
        newSpecs: selectedSpecs,
        currentSpecs: this.form.selectedSpecs
      }));

      // 保存当前选择的规格（用于取消时恢复）
      const previousSpecs = [...this.form.selectedSpecs];

      // 使用Vue.set确保响应式更新
      this.$set(this.form, 'selectedSpecs', [...selectedSpecs]);

      // 使用nextTick确保DOM更新完成
      this.$nextTick(() => {
        console.log('规格更新完成:', JSON.stringify({
          updatedSpecs: this.form.selectedSpecs,
          selectedSpecsDataLength: this.selectedSpecsData.length
        }));
      });

      // 更新规格数据
      const specData = {
        selectedSpecs: selectedSpecs,
        specNames: selectedSpecs.map(specId => ({
          specId,
          specName: this.getSpecName(specId)
        }))
      };
      this.form.specData = JSON.stringify(specData);

      // 如果有现有的SKU和价格数据，询问是否清空
      if (this.form.skuList.length > 0 || this.form.basePriceList.length > 0) {
        this.$confirm('更改规格将清空现有的SKU和价格设置，是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 用户确认，清空SKU和价格数据
          this.clearSkuAndPriceData();
          this.$message.success('规格设置已更新');
        }).catch(() => {
          // 用户取消，恢复之前的选择
          this.$set(this.form, 'selectedSpecs', [...previousSpecs]);
          this.restorePreviousSpecData();
        });
      } else {
        // 没有现有数据，直接应用新选择
        this.$message.success('规格设置已更新');
      }
    },
    /** 清空SKU和价格数据 */
    clearSkuAndPriceData() {
      this.form.skuList = [];
      this.form.basePriceList = [];
      this.form.levelPriceList = [];
      this.form.tierPriceList = [];
    },
    /** 恢复之前的规格数据 */
    restorePreviousSpecData() {
      try {
        // 恢复规格数据到当前选择的状态
        const previousSpecData = {
          selectedSpecs: this.form.selectedSpecs,
          specNames: this.form.selectedSpecs.map(specId => ({
            specId,
            specName: this.getSpecName(specId)
          }))
        };
        this.form.specData = JSON.stringify(previousSpecData);
      } catch (e) {
        console.error('恢复规格数据失败:', e);
      }
    },
    /** 清空规格选择 */
    clearSpecSelection() {
      this.$confirm('清空规格选择将同时清空SKU和价格设置，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.selectedSpecs = [];
        this.$forceUpdate(); // 强制更新视图
        this.form.specData = '';
        this.clearSkuAndPriceData();
        this.$message.success('已清空规格选择');
      });
    },
    /** 获取规格名称 */
    getSpecName(specId) {
      const spec = this.productSpecs.find(item => item.specId === specId);
      const specName = spec ? spec.specName : `规格${specId}`;
      console.log('整合版本 - getSpecName调用:', specId, '->', specName);
      return specName;
    },
    /** 提交表单 */
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          return;
        }

        if (this.codeCheckResult === false) {
          this.$modal.msgError("商品编码已存在，请修改后重试");
          return;
        }

        // 验证基础价格
        if (!this.form.basePriceList || this.form.basePriceList.length === 0) {
          this.$modal.msgError("请设置基础价格");
          return;
        }

        this.loading = true;
        updateProductV2(this.form).then(response => {
          this.$modal.msgSuccess("修改成功");
          // 直接使用window.history.back()返回上一页
          window.history.back();
        }).catch(() => {
          this.loading = false;
        });
      });
    },
    /** 取消 */
    handleCancel() {
      // 直接使用window.history.back()返回上一页
      window.history.back();
    },
    /** 处理SKU数据变化 */
    handleSkuDataChange(skuData) {
      console.log('整合版本 - SKU数据变化:', skuData);
      this.form.skuList = skuData;
    },
    /** 处理价格数据变化 */
    handlePriceDataChange(priceData) {
      console.log('整合版本 - 价格数据变化:', priceData);
      this.form.basePriceList = priceData.basePrices || [];
      this.form.levelPriceList = priceData.levelPrices || [];
      this.form.tierPriceList = priceData.tierPrices || [];
    }
  }
};
</script>

<style lang="scss" scoped>
.card-title {
  font-size: 18px;
  font-weight: bold;
}

.header-buttons {
  float: right;
}

.code-check-result {
  margin-top: 5px;
  font-size: 12px;

  i {
    margin-right: 5px;
  }
}

.mb-15 {
  margin-bottom: 15px;
}

.selected-specs-preview {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  .preview-label {
    font-weight: 600;
    color: #606266;
    margin-right: 10px;
  }

  .spec-tag {
    margin-right: 8px;
    margin-bottom: 4px;
  }
}
</style>
