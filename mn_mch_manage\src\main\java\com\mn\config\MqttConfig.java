package com.mn.config;

import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;

@Configuration
@ConditionalOnProperty(value = "mqtt.enabled", havingValue = "true")
public class MqttConfig {

    @Value("${mqtt.host}")
    public String host;

    @Value("${mqtt.userName}")
    public String userName;

    @Value("${mqtt.pwd}")
    public String pwd;

    @Value("${mqtt.topic}")
    public String topic;

    @Bean(value = "factory")
    public MqttPahoClientFactory factory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        options.setUserName(userName);
        options.setPassword(pwd.toCharArray());
        options.setServerURIs(new String[]{host});
        factory.setConnectionOptions(options);
        return factory;
    }

}
