import API from '../../../api/request';

Page({
  data: {
    // 搜索相关
    searchKeyword: '',

    // 分类相关
    categoryList: [],
    selectedCategoryId: '', // 使用空字符串代替null
    currentCategoryName: '推荐产品', // 当前分类名称

    // 产品列表相关
    productList: [],
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    loadingMore: false
  },

  // 价格转换函数：分转元
  formatPrice: function(priceInCents) {
    if (!priceInCents && priceInCents !== 0) return '0.00';
    return (priceInCents / 100).toFixed(2);
  },

  // 获取商品实时价格
  async getProductPrice(productId, skuId, quantity = 1) {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      const partnerId = userInfo?.partnerId || 6; // 默认合作伙伴ID

      const params = {
        productId: productId,
        skuId: skuId,
        partnerId: partnerId,
        quantity: quantity
      };

      const res = await API.getProductPrice(params);
      if (res.code === 200) {
        return res.data;
      } else {
        console.warn('获取商品价格失败:', res.msg);
        return null;
      }
    } catch (error) {
      console.error('获取商品价格异常:', error);
      return null;
    }
  },

  // 批量获取商品价格
  async batchGetProductPrices(products) {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      const partnerId = userInfo?.partnerId || 6;

      const requests = products.map(product => ({
        productId: product.productId,
        skuId: product.defaultSkuId || product.productId, // 使用默认SKU或商品ID
        partnerId: partnerId,
        quantity: 1,
        orderDate: new Date().toISOString().split('T')[0]
      }));

      const res = await API.batchCalculatePrice(requests);
      if (res.code === 200) {
        return res.data;
      } else {
        console.warn('批量获取商品价格失败:', res.msg);
        return [];
      }
    } catch (error) {
      console.error('批量获取商品价格异常:', error);
      return [];
    }
  },

  onLoad: function(options) {
    // 获取分类列表
    this.getCategories();
    // 获取产品列表
    this.getProductList();
  },

  onShow: function() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  // 获取产品分类列表
  getCategories() {
    API.getProductCategories().then(res => {
      if (res.code === 0) {
        let categoryList = res.data || [];

        // 过滤掉名称为"推荐"的分类，避免重复
        categoryList = categoryList.filter(cat =>
          cat.categoryName !== '推荐' &&
          cat.categoryName !== '推荐产品'
        );

        // 去重处理
        const uniqueCategories = [];
        const seenNames = new Set();
        categoryList.forEach(cat => {
          if (!seenNames.has(cat.categoryName)) {
            seenNames.add(cat.categoryName);
            uniqueCategories.push(cat);
          }
        });

        console.log('分类列表:', uniqueCategories);
        this.setData({
          categoryList: uniqueCategories
        });
      }
    }).catch(err => {
      console.error('获取分类列表失败', err);
      wx.showToast({
        title: '获取分类失败',
        icon: 'none'
      });
    });
  },

  // 更新当前分类名称
  updateCategoryName() {
    if (this.data.selectedCategoryId === '') {
      this.setData({
        currentCategoryName: '推荐产品'
      });
    } else {
      const category = this.data.categoryList.find(cat => cat.categoryId === this.data.selectedCategoryId);
      this.setData({
        currentCategoryName: category ? category.categoryName : '产品列表'
      });
    }
  },

  // 获取产品列表
  async getProductList(isLoadMore = false) {
    if (this.data.loading || (isLoadMore && this.data.loadingMore)) {
      return;
    }

    this.setData({
      loading: !isLoadMore,
      loadingMore: isLoadMore
    });

    const params = {
      categoryId: this.data.selectedCategoryId || undefined, // 空字符串转为undefined
      keyword: this.data.searchKeyword || undefined,
      pageNum: isLoadMore ? this.data.pageNum + 1 : 1,
      pageSize: this.data.pageSize
    };

    try {
      const res = await API.getProductsByCategory(params);
      if (res.code === 0) {
        let newList = res.data || [];

        // 批量获取实时价格
        const priceResults = await this.batchGetProductPrices(newList);

        // 合并价格信息
        newList = newList.map((product, index) => {
          const priceData = priceResults[index];
          if (priceData) {
            return {
              ...product,
              price: this.formatPrice(priceData.finalPrice), // 使用计算后的价格
              originalPrice: this.formatPrice(product.price), // 保留原价格
              priceType: priceData.priceType,
              priceDescription: priceData.priceDescription,
              hasSpecialPrice: priceData.priceType !== 'BASE_PRICE'
            };
          } else {
            return {
              ...product,
              price: this.formatPrice(product.price), // 使用原价格
              originalPrice: this.formatPrice(product.price),
              priceType: 'BASE_PRICE',
              hasSpecialPrice: false
            };
          }
        });

        const productList = isLoadMore ?
          [...this.data.productList, ...newList] :
          newList;

        this.setData({
          productList: productList,
          pageNum: params.pageNum,
          hasMore: newList.length >= this.data.pageSize,
          loading: false,
          loadingMore: false
        });
      } else {
        wx.showToast({
          title: res.message || '获取产品列表失败',
          icon: 'none'
        });
        this.setData({
          loading: false,
          loadingMore: false
        });
      }
    } catch (err) {
      console.error('获取产品列表失败', err);
      wx.showToast({
        title: '获取产品列表失败',
        icon: 'none'
      });
      this.setData({
        loading: false,
        loadingMore: false
      });
    }
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });

    // 清除之前的搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }

    // 设置延迟搜索，避免频繁请求
    this.searchTimer = setTimeout(() => {
      this.onSearch();
    }, 500);
  },

  // 执行搜索
  onSearch() {
    console.log('执行搜索，关键词:', this.data.searchKeyword);
    this.setData({
      pageNum: 1,
      hasMore: true
    });
    this.getProductList();
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchKeyword: '',
      pageNum: 1,
      hasMore: true
    });
    this.getProductList();
  },

  // 选择分类
  selectCategory(e) {
    const categoryId = e.currentTarget.dataset.id || ''; // 确保不是undefined
    this.setData({
      selectedCategoryId: categoryId,
      pageNum: 1,
      hasMore: true
    });
    this.updateCategoryName();
    this.getProductList();
  },

  // 加载更多
  loadMore() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.getProductList(true);
    }
  },

  // 跳转到产品详情
  navigateToDetail(e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product/detail/index?id=${productId}`
    });
  },

  // 刷新数据
  refreshData() {
    this.setData({
      pageNum: 1,
      hasMore: true
    });
    this.getProductList();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData();
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadMore();
  }
});
