package com.mn.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mn.util.AjaxResult;
import com.mn.model.TableDataInfo;
import com.mn.entity.ProductBasePrice;
import com.mn.service.IProductBasePriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 基础价格管理控制器
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@RestController
@RequestMapping("/pricing/base")
@Slf4j
public class ProductBasePriceController extends BaseController {

    @Autowired
    private IProductBasePriceService productBasePriceService;

    /**
     * 查询基础价格列表
     */
    @PreAuthorize("@ss.hasPermi('pricing:base:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductBasePrice productBasePrice) {
        startPage();
        QueryWrapper<ProductBasePrice> queryWrapper = new QueryWrapper<>();
        
        if (productBasePrice.getProductId() != null) {
            queryWrapper.eq("product_id", productBasePrice.getProductId());
        }
        if (productBasePrice.getSkuId() != null) {
            queryWrapper.eq("sku_id", productBasePrice.getSkuId());
        }
        
        queryWrapper.orderByDesc("create_time");
        List<ProductBasePrice> list = productBasePriceService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 获取基础价格详细信息
     */
    @PreAuthorize("@ss.hasPermi('pricing:base:query')")
    @GetMapping(value = "/{priceId}")
    public AjaxResult getInfo(@PathVariable("priceId") Long priceId) {
        return success(productBasePriceService.getById(priceId));
    }

    /**
     * 新增基础价格
     */
    @PreAuthorize("@ss.hasPermi('pricing:base:add')")
    @PostMapping
    public AjaxResult add(@RequestBody ProductBasePrice productBasePrice) {
        try {
            productBasePrice.setCreateBy(getUsername());
            boolean result = productBasePriceService.save(productBasePrice);
            return result ? success() : error("新增基础价格失败");
        } catch (Exception e) {
            log.error("新增基础价格失败", e);
            return error("新增基础价格失败：" + e.getMessage());
        }
    }

    /**
     * 修改基础价格
     */
    @PreAuthorize("@ss.hasPermi('pricing:base:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody ProductBasePrice productBasePrice) {
        try {
            productBasePrice.setUpdateBy(getUsername());
            boolean result = productBasePriceService.updateById(productBasePrice);
            return result ? success() : error("修改基础价格失败");
        } catch (Exception e) {
            log.error("修改基础价格失败", e);
            return error("修改基础价格失败：" + e.getMessage());
        }
    }

    /**
     * 删除基础价格
     */
    @PreAuthorize("@ss.hasPermi('pricing:base:remove')")
    @DeleteMapping("/{priceIds}")
    public AjaxResult remove(@PathVariable Long[] priceIds) {
        try {
            boolean result = productBasePriceService.removeByIds(Arrays.asList(priceIds));
            return result ? success() : error("删除基础价格失败");
        } catch (Exception e) {
            log.error("删除基础价格失败", e);
            return error("删除基础价格失败：" + e.getMessage());
        }
    }

    /**
     * 导出基础价格列表
     */
    @PreAuthorize("@ss.hasPermi('pricing:base:export')")
    @PostMapping("/export")
    public void export(ProductBasePrice productBasePrice) {
        QueryWrapper<ProductBasePrice> queryWrapper = new QueryWrapper<>();
        
        if (productBasePrice.getProductId() != null) {
            queryWrapper.eq("product_id", productBasePrice.getProductId());
        }
        if (productBasePrice.getSkuId() != null) {
            queryWrapper.eq("sku_id", productBasePrice.getSkuId());
        }
        
        List<ProductBasePrice> list = productBasePriceService.list(queryWrapper);
        log.info("导出基础价格列表，共{}条记录", list.size());
    }
}
