<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML转图片工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        
        .upload-area:hover {
            border-color: #667eea;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        input[type="file"] {
            display: none;
        }
        
        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }
        
        .upload-btn:hover {
            transform: translateY(-2px);
        }
        
        .convert-btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .convert-btn:hover {
            transform: translateY(-2px);
        }
        
        .convert-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .preview-area {
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            display: none;
        }
        
        .preview-content {
            max-height: 500px;
            overflow-y: auto;
            background: white;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .format-options {
            margin: 20px 0;
            text-align: center;
        }
        
        .format-options label {
            margin: 0 15px;
            cursor: pointer;
        }
        
        .format-options input[type="radio"] {
            margin-right: 5px;
        }
        
        .quality-control {
            margin: 20px 0;
            text-align: center;
        }
        
        .quality-control input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }
        
        .tips {
            background: #e8f4fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .tips h3 {
            margin-top: 0;
            color: #1976D2;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ HTML转图片工具</h1>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 拖拽HTML文件到这里，或点击选择文件</p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                选择HTML文件
            </button>
            <input type="file" id="fileInput" accept=".html,.htm" onchange="loadFile(event)">
        </div>
        
        <div class="format-options">
            <h3>输出格式：</h3>
            <label><input type="radio" name="format" value="png" checked> PNG (推荐)</label>
            <label><input type="radio" name="format" value="jpeg"> JPEG</label>
            <label><input type="radio" name="format" value="webp"> WebP</label>
        </div>
        
        <div class="quality-control">
            <h3>图片质量：</h3>
            <input type="range" id="quality" min="0.1" max="1" step="0.1" value="0.9">
            <span id="qualityValue">90%</span>
        </div>
        
        <div class="preview-area" id="previewArea">
            <div class="preview-content" id="previewContent"></div>
        </div>
        
        <div class="controls">
            <button class="convert-btn" id="convertBtn" onclick="convertToImage()" disabled>
                🖼️ 转换为图片
            </button>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在转换中，请稍候...</p>
        </div>
        
        <div class="tips">
            <h3>💡 使用提示：</h3>
            <ul>
                <li>支持HTML和HTM格式文件</li>
                <li>PNG格式支持透明背景，适合网页使用</li>
                <li>JPEG格式文件更小，适合打印</li>
                <li>建议质量设置为80%-90%以获得最佳效果</li>
                <li>转换后的图片会自动下载到您的下载文件夹</li>
            </ul>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const previewArea = document.getElementById('previewArea');
        const previewContent = document.getElementById('previewContent');
        const convertBtn = document.getElementById('convertBtn');
        const loading = document.getElementById('loading');
        const qualitySlider = document.getElementById('quality');
        const qualityValue = document.getElementById('qualityValue');
        
        let currentHTML = '';
        
        // 质量滑块事件
        qualitySlider.addEventListener('input', function() {
            qualityValue.textContent = Math.round(this.value * 100) + '%';
        });
        
        // 拖拽事件
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });
        
        function loadFile(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }
        
        function handleFile(file) {
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                alert('请选择HTML文件！');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                currentHTML = e.target.result;
                previewContent.innerHTML = currentHTML;
                previewArea.style.display = 'block';
                convertBtn.disabled = false;
            };
            reader.readAsText(file);
        }
        
        function convertToImage() {
            if (!currentHTML) {
                alert('请先选择HTML文件！');
                return;
            }
            
            loading.style.display = 'block';
            convertBtn.disabled = true;
            
            // 获取选择的格式和质量
            const format = document.querySelector('input[name="format"]:checked').value;
            const quality = parseFloat(qualitySlider.value);
            
            // 创建临时iframe来渲染HTML
            const iframe = document.createElement('iframe');
            iframe.style.position = 'absolute';
            iframe.style.left = '-9999px';
            iframe.style.width = '1200px';
            iframe.style.height = 'auto';
            document.body.appendChild(iframe);
            
            iframe.onload = function() {
                // 等待iframe内容加载完成
                setTimeout(() => {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    html2canvas(iframeDoc.body, {
                        useCORS: true,
                        allowTaint: true,
                        scale: 2, // 提高清晰度
                        backgroundColor: format === 'png' ? null : '#ffffff',
                        width: 1200,
                        height: iframeDoc.body.scrollHeight
                    }).then(canvas => {
                        // 转换为指定格式
                        let mimeType = 'image/png';
                        let extension = 'png';
                        
                        if (format === 'jpeg') {
                            mimeType = 'image/jpeg';
                            extension = 'jpg';
                        } else if (format === 'webp') {
                            mimeType = 'image/webp';
                            extension = 'webp';
                        }
                        
                        const dataURL = canvas.toDataURL(mimeType, quality);
                        
                        // 下载图片
                        const link = document.createElement('a');
                        link.download = `美企付产品版本对比表.${extension}`;
                        link.href = dataURL;
                        link.click();
                        
                        // 清理
                        document.body.removeChild(iframe);
                        loading.style.display = 'none';
                        convertBtn.disabled = false;
                        
                        alert('图片转换完成！已下载到您的下载文件夹。');
                    }).catch(error => {
                        console.error('转换失败:', error);
                        alert('转换失败，请重试！');
                        document.body.removeChild(iframe);
                        loading.style.display = 'none';
                        convertBtn.disabled = false;
                    });
                }, 1000);
            };
            
            // 写入HTML内容
            iframe.contentDocument.open();
            iframe.contentDocument.write(currentHTML);
            iframe.contentDocument.close();
        }
    </script>
</body>
</html>
