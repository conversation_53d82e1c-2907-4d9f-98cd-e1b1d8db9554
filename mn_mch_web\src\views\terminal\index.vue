<template>
    <div class="payment-container">
      <!-- 加载状态 -->
      <div v-if="currentStep === 'loading'" class="loading-section">
        <div class="loading-animation">
          <div class="spinner"></div>
        </div>
        <h2 class="loading-title">{{ loadingTitle }}</h2>
        <p class="loading-desc">{{ loadingDesc }}</p>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progressWidth + '%' }"></div>
        </div>
        <div v-if="orderNo" class="order-display">
          <span class="order-label">订单号：</span>
          <span class="order-number">{{ orderNo }}</span>
        </div>
      </div>

      <!-- 成功跳转状态 -->
      <div v-else-if="currentStep === 'redirecting'" class="success-section">
        <div class="success-icon">
          <i class="el-icon-success"></i>
        </div>
        <h2 class="success-title">支付链接获取成功</h2>
        <p class="success-desc">正在跳转到支付页面...</p>
        <div class="countdown-display">
          <span>{{ redirectCountdown }}秒后自动跳转</span>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="currentStep === 'error'" class="error-section">
        <div class="error-icon">
          <i :class="errorIcon"></i>
        </div>
        <h2 class="error-title">{{ errorTitle }}</h2>
        <p class="error-desc">{{ errorDesc }}</p>

        <div v-if="orderNo" class="order-display">
          <span class="order-label">订单号：</span>
          <span class="order-number">{{ orderNo }}</span>
        </div>

        <div v-if="detailedErrorMessage" class="detailed-error">
          <p>{{ detailedErrorMessage }}</p>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button v-if="showRetryButton" @click="retryPayment" class="retry-btn">
            <i class="el-icon-refresh"></i>
            重新尝试
          </button>
          <button v-if="showWaitButton" @click="startWaitingMode" class="wait-btn">
            <i class="el-icon-time"></i>
            等待支付完成
          </button>
        </div>

        <!-- 倒计时提示 -->
        <div v-if="showCountdown && remainingTime > 0" class="countdown-info">
          <i class="el-icon-time"></i>
          <span>{{ Math.floor(remainingTime / 60) }}分{{ remainingTime % 60 }}秒后可重新尝试</span>
        </div>
      </div>

      <!-- 等待模式 -->
      <div v-else-if="currentStep === 'waiting'" class="waiting-section">
        <div class="waiting-icon">
          <i class="el-icon-loading"></i>
        </div>
        <h2 class="waiting-title">等待支付完成</h2>
        <p class="waiting-desc">正在等待当前支付完成，请稍候...</p>

        <div class="waiting-progress">
          <div class="waiting-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>

        <div class="waiting-actions">
          <button @click="checkPaymentStatus" class="check-btn">
            <i class="el-icon-search"></i>
            检查状态
          </button>
          <button @click="cancelWaiting" class="cancel-btn">
            <i class="el-icon-close"></i>
            取消等待
          </button>
        </div>
      </div>
    </div>
  </template>

  <script>
  import { getQueryObject } from '@/utils'

  export default {
    name: 'TerminalDetection',
    data() {
      return {
        // 当前步骤：loading, redirecting, error, waiting
        currentStep: 'loading',

        // 基础信息
        orderNo: '',
        payChannel: 0,
        userAgent: '',

        // 加载状态
        loadingTitle: '正在识别支付终端',
        loadingDesc: '正在识别您的支付方式...',
        progressWidth: 0,

        // 成功跳转状态
        redirectCountdown: 3,

        // 错误状态
        errorTitle: '',
        errorDesc: '',
        errorIcon: 'el-icon-warning',
        detailedErrorMessage: '',
        showRetryButton: false,
        showWaitButton: false,
        showCountdown: false,
        remainingTime: 0,

        // 定时器
        progressTimer: null,
        countdownTimer: null,
        redirectTimer: null,
        waitingTimer: null
      }
    },
    created() {
      this.getOrderNo()
      this.detectTerminal()
    },
    watch: {
      '$route'() {
        // 监听路由变化，重新获取参数
        this.getOrderNo()
        this.detectTerminal()
      }
    },
    methods: {
      // 获取URL中的orderNo参数
      getOrderNo() {
        // 重置状态
        this.orderNo = ''
        this.errorMessage = ''

        console.log('=== 开始获取订单号 ===')
        console.log('当前完整URL:', window.location.href)
        console.log('window.location.hash:', window.location.hash)
        console.log('window.location.search:', window.location.search)

        // 方法1: 从Vue路由中获取参数
        if (this.$route && this.$route.query && this.$route.query.orderNo) {
          this.orderNo = this.$route.query.orderNo
          console.log('从Vue路由获取到订单号:', this.orderNo)
        }

        // 方法2: 使用工具函数从完整URL获取
        if (!this.orderNo) {
          const queryObj = getQueryObject(window.location.href)
          console.log('工具函数解析结果:', queryObj)
          this.orderNo = queryObj.orderNo || ''
          if (this.orderNo) {
            console.log('从工具函数获取到订单号:', this.orderNo)
          }
        }

        // 方法3: 从URL hash中手动解析
        if (!this.orderNo) {
          const hash = window.location.hash
          console.log('当前hash:', hash)
          const queryIndex = hash.indexOf('?')
          if (queryIndex !== -1) {
            const queryString = hash.substring(queryIndex + 1)
            console.log('查询字符串:', queryString)
            const urlParams = new URLSearchParams(queryString)
            this.orderNo = urlParams.get('orderNo') || ''
            if (this.orderNo) {
              console.log('从hash手动解析获取到订单号:', this.orderNo)
            }
          }
        }

        // 方法4: 从URL search中获取
        if (!this.orderNo) {
          const urlParams = new URLSearchParams(window.location.search)
          this.orderNo = urlParams.get('orderNo') || ''
          if (this.orderNo) {
            console.log('从URL search获取到订单号:', this.orderNo)
          }
        }

        console.log('最终获取到的订单号:', this.orderNo)
        console.log('Vue路由参数:', this.$route ? this.$route.query : 'route未定义')
        console.log('===================')

        if (!this.orderNo) {
          this.errorMessage = '缺少订单号参数'
          this.terminalType = '参数错误'
          this.terminalDesc = '请检查访问链接是否正确'
          this.terminalIcon = 'el-icon-warning'
        }
      },

      detectTerminal() {
        // 如果没有订单号，显示错误
        if (!this.orderNo) {
          this.showError('参数错误', '缺少订单号参数', '请检查访问链接是否正确', false, false)
          return
        }

        // 获取用户代理字符串
        this.userAgent = navigator.userAgent.toLowerCase()

        // 开始进度条动画
        this.startProgressAnimation()

        // 识别终端类型
        setTimeout(() => {
          if (this.isWechat()) {
            this.loadingTitle = '微信支付'
            this.loadingDesc = '检测到微信扫码，正在获取支付链接...'
            this.payChannel = 1
            this.processPay()
          } else if (this.isAlipay()) {
            this.loadingTitle = '支付宝支付'
            this.loadingDesc = '检测到支付宝扫码，正在获取支付链接...'
            this.payChannel = 2
            this.processPay()
          } else {
            this.showError(
              '暂不支持',
              '当前访问方式暂不支持',
              '请使用微信或支付宝扫码访问此页面',
              true,
              false
            )
          }
        }, 1500)
      },

      // 开始进度条动画
      startProgressAnimation() {
        this.progressWidth = 0
        this.progressTimer = setInterval(() => {
          if (this.progressWidth < 90) {
            this.progressWidth += Math.random() * 15
          }
        }, 200)
      },

      // 完成进度条
      completeProgress() {
        if (this.progressTimer) {
          clearInterval(this.progressTimer)
          this.progressTimer = null
        }
        this.progressWidth = 100
      },

      // 处理支付
      async processPay() {
        try {
          this.loadingDesc = '正在获取支付链接，请稍候...'

          // 使用原生axios调用，绕过响应拦截器的错误处理
          const axios = require('axios')
          const response = await axios.get(`${process.env.VUE_APP_BASE_API}/pay/qrCodePay`, {
            params: {
              orderNo: this.orderNo,
              payChannel: this.payChannel
            },
            timeout: 10000
          })

          if (response.data.code === 0 && response.data.data && response.data.data.qrCodeUrl) {
            // 支付成功，准备跳转
            this.completeProgress()
            this.showSuccessAndRedirect(response.data.data.qrCodeUrl)
          } else {
            // 支付失败，显示服务器返回的具体错误信息
            this.completeProgress()
            this.showError(
              '支付失败',
              response.data.msg || '获取支付链接失败',
              response.data.msg || '支付处理失败，请稍后重试',
              true,
              false
            )
          }
        } catch (error) {
          console.error('支付处理异常:', error)
          this.completeProgress()

          // 检查是否是HTTP错误响应
          if (error.response && error.response.data) {
            const errorData = error.response.data
            console.log('服务器返回的错误数据:', errorData)

            // 判断是否是订单支付中的错误
            if (errorData.msg && errorData.msg.includes('正在使用') && errorData.msg.includes('支付中')) {
              this.showPaymentInProgressError(errorData.msg)
            } else {
              this.showError(
                '支付失败',
                errorData.msg || '支付处理出现异常',
                errorData.msg || '支付处理失败，请稍后重试',
                true,
                false
              )
            }
          } else {
            // 网络异常或其他未知错误
            this.showError(
              '网络异常',
              '无法连接到支付服务器',
              '请检查网络连接后重试',
              true,
              false
            )
          }
        }
      },

      // 检测是否为微信浏览器
      isWechat() {
        return /micromessenger/.test(this.userAgent)
      },

      // 检测是否为支付宝
      isAlipay() {
        return /alipayclient/.test(this.userAgent)
      },

      // 显示成功状态并准备跳转
      showSuccessAndRedirect(qrCodeUrl) {
        this.currentStep = 'redirecting'
        this.redirectCountdown = 3

        // 开始倒计时
        this.redirectTimer = setInterval(() => {
          this.redirectCountdown--
          if (this.redirectCountdown <= 0) {
            clearInterval(this.redirectTimer)
            window.location.href = qrCodeUrl
          }
        }, 1000)

        // 用户可以点击立即跳转
        setTimeout(() => {
          if (this.redirectCountdown > 0) {
            window.location.href = qrCodeUrl
          }
        }, 1000)
      },

      // 显示错误状态
      showError(title, desc, detailedMessage, showRetry, showWait) {
        this.currentStep = 'error'
        this.errorTitle = title
        this.errorDesc = desc
        this.detailedErrorMessage = detailedMessage
        this.showRetryButton = showRetry
        this.showWaitButton = showWait
        this.errorIcon = 'el-icon-warning'
      },

      // 显示支付进行中错误
      showPaymentInProgressError(message) {
        this.currentStep = 'error'
        this.errorTitle = '订单支付中'
        this.errorDesc = '当前订单正在被其他用户支付'
        this.detailedErrorMessage = message
        this.showRetryButton = false
        this.showWaitButton = true
        this.errorIcon = 'el-icon-time'

        // 解析剩余时间
        this.parseRemainingTime(message)
      },

      // 解析剩余时间
      parseRemainingTime(message) {
        const match = message.match(/(\d+)分钟后过期/)
        if (match) {
          this.remainingTime = parseInt(match[1]) * 60
          this.showCountdown = true
          this.startCountdown()
        }
      },

      // 开始倒计时
      startCountdown() {
        this.countdownTimer = setInterval(() => {
          this.remainingTime--
          if (this.remainingTime <= 0) {
            clearInterval(this.countdownTimer)
            this.showCountdown = false
            this.showRetryButton = true
          }
        }, 1000)
      },

      // 重试支付
      retryPayment() {
        this.currentStep = 'loading'
        this.loadingTitle = '重新获取支付链接'
        this.loadingDesc = '正在重新获取支付链接...'
        this.startProgressAnimation()
        this.processPay()
      },

      // 开始等待模式
      startWaitingMode() {
        this.currentStep = 'waiting'
        this.startWaitingAnimation()
      },

      // 开始等待动画
      startWaitingAnimation() {
        // 这里可以添加等待动画逻辑
      },

      // 检查支付状态
      async checkPaymentStatus() {
        this.currentStep = 'loading'
        this.loadingTitle = '检查支付状态'
        this.loadingDesc = '正在检查当前支付状态...'
        this.startProgressAnimation()

        // 重新尝试获取支付链接
        setTimeout(() => {
          this.processPay()
        }, 1000)
      },

      // 取消等待
      cancelWaiting() {
        this.currentStep = 'error'
        this.errorTitle = '已取消等待'
        this.errorDesc = '您可以稍后重新尝试支付'
        this.showRetryButton = true
        this.showWaitButton = false
      }
    },

    // 组件销毁时清理定时器
    beforeDestroy() {
      if (this.progressTimer) clearInterval(this.progressTimer)
      if (this.countdownTimer) clearInterval(this.countdownTimer)
      if (this.redirectTimer) clearInterval(this.redirectTimer)
      if (this.waitingTimer) clearInterval(this.waitingTimer)
    }
  }
  </script>

  <style scoped>
  .payment-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* 加载状态样式 */
  .loading-section {
    background: white;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
  }

  .loading-animation {
    margin-bottom: 24px;
  }

  .spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #409EFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 12px 0;
  }

  .loading-desc {
    font-size: 16px;
    color: #606266;
    margin: 0 0 24px 0;
  }

  .progress-bar {
    width: 100%;
    height: 6px;
    background-color: #f5f7fa;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 20px;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #409EFF, #67C23A);
    border-radius: 3px;
    transition: width 0.3s ease;
  }

  .order-display {
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 8px;
    margin-top: 20px;
  }

  .order-label {
    font-size: 14px;
    color: #909399;
  }

  .order-number {
    font-size: 14px;
    color: #303133;
    font-weight: 600;
  }

  /* 成功跳转状态样式 */
  .success-section {
    background: white;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
  }

  .success-icon i {
    font-size: 64px;
    color: #67C23A;
    margin-bottom: 20px;
  }

  .success-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 12px 0;
  }

  .success-desc {
    font-size: 16px;
    color: #606266;
    margin: 0 0 24px 0;
  }

  .countdown-display {
    background: #f0f9ff;
    padding: 12px 16px;
    border-radius: 8px;
    color: #409EFF;
    font-weight: 600;
  }

  /* 错误状态样式 */
  .error-section {
    background: white;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
  }

  .error-icon i {
    font-size: 64px;
    color: #F56C6C;
    margin-bottom: 20px;
  }

  .error-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 12px 0;
  }

  .error-desc {
    font-size: 16px;
    color: #606266;
    margin: 0 0 20px 0;
  }

  .detailed-error {
    background: #fef0f0;
    padding: 16px;
    border-radius: 8px;
    margin: 20px 0;
    border-left: 4px solid #F56C6C;
  }

  .detailed-error p {
    margin: 0;
    color: #F56C6C;
    font-size: 14px;
    line-height: 1.5;
  }

  .action-buttons {
    margin: 24px 0;
    display: flex;
    gap: 12px;
    justify-content: center;
  }

  .retry-btn, .wait-btn, .check-btn, .cancel-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .retry-btn {
    background: #409EFF;
    color: white;
  }

  .retry-btn:hover {
    background: #337ecc;
  }

  .wait-btn {
    background: #E6A23C;
    color: white;
  }

  .wait-btn:hover {
    background: #cf9236;
  }

  .check-btn {
    background: #67C23A;
    color: white;
  }

  .check-btn:hover {
    background: #5daf34;
  }

  .cancel-btn {
    background: #909399;
    color: white;
  }

  .cancel-btn:hover {
    background: #82848a;
  }

  .countdown-info {
    background: #fff7e6;
    padding: 12px 16px;
    border-radius: 8px;
    color: #E6A23C;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  /* 等待状态样式 */
  .waiting-section {
    background: white;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
  }

  .waiting-icon i {
    font-size: 64px;
    color: #E6A23C;
    margin-bottom: 20px;
    animation: spin 2s linear infinite;
  }

  .waiting-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 12px 0;
  }

  .waiting-desc {
    font-size: 16px;
    color: #606266;
    margin: 0 0 24px 0;
  }

  .waiting-progress {
    margin: 24px 0;
  }

  .waiting-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
  }

  .waiting-dots span {
    width: 8px;
    height: 8px;
    background: #409EFF;
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
  }

  .waiting-dots span:nth-child(1) { animation-delay: -0.32s; }
  .waiting-dots span:nth-child(2) { animation-delay: -0.16s; }

  @keyframes bounce {
    0%, 80%, 100% {
      transform: scale(0);
    } 40% {
      transform: scale(1);
    }
  }

  .waiting-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 24px;
  }

  /* 响应式设计 */
  @media (max-width: 480px) {
    .payment-container {
      padding: 10px;
    }

    .loading-section,
    .success-section,
    .error-section,
    .waiting-section {
      padding: 30px 20px;
    }

    .action-buttons {
      flex-direction: column;
    }

    .retry-btn, .wait-btn, .check-btn, .cancel-btn {
      width: 100%;
      justify-content: center;
    }
  }
  </style>