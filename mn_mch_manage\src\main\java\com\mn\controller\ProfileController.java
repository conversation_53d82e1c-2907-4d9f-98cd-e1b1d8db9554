package com.mn.controller;

import com.mn.entity.NyUser;
import com.mn.security.LoginUser;
import com.mn.security.service.TokenService;
import com.mn.service.INyRoleService;
import com.mn.service.INyUserService;
import com.mn.util.AjaxResult;
import com.mn.util.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/user/profile")
public class ProfileController extends BaseController {

    @Resource
    INyRoleService roleService;

    @Resource
    TokenService tokenService;

    @Resource
    INyUserService userService;

    /**
     * 个人信息
     */
    @GetMapping
    public AjaxResult profile() {
        LoginUser loginUser = getLoginUser();
        NyUser user = loginUser.getUser();
        AjaxResult ajax = AjaxResult.success(user);
        ajax.put("roleGroup", roleService.selectRoleListByUserId(loginUser.getUserId()));
        return ajax;
    }

    /**
     * 修改用户
     */
    @PutMapping
    public AjaxResult updateProfile(@RequestBody NyUser user) {
        LoginUser loginUser = getLoginUser();
        NyUser sysUser = loginUser.getUser();
        user.setUserName(sysUser.getUserName());
        user.setUserId(sysUser.getUserId());
        if (userService.updateById(user)) {
            // 更新缓存用户信息
            sysUser.setNickName(user.getNickName());
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword) {
        LoginUser loginUser = getLoginUser();
        String password = loginUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password)) {
            return error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            return error("新密码不能与旧密码相同");
        }
        NyUser db = new NyUser();
        db.setUserId(loginUser.getUserId());
        db.setPassword(SecurityUtils.encryptPassword(newPassword));
        if (userService.updateById(db)) {
            // 更新缓存用户密码
            loginUser.getUser().setPassword(SecurityUtils.encryptPassword(newPassword));
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改密码异常，请联系管理员");
    }

}
