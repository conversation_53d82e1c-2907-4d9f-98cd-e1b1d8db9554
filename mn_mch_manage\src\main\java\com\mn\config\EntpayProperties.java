package com.mn.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@Data
@Configuration
@ConfigurationProperties(prefix = "entpay")
public class EntpayProperties {

    private String platformId;

    private String baseUrl;

    private String platformPrivateCertSerialNo;

    private String tbepSerialNo;

    private String tbepPublicKey;

    private String platformPrivateKey;

    private String entId;

    private String entName;

    private String entAcctId;

}
