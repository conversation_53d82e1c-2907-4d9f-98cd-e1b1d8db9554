.order-detail-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 订单状态 */
.order-status-section {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 40rpx 30rpx;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order-status-section.success {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.order-status-section.closed {
  background: linear-gradient(135deg, #999 0%, #666 100%);
}

.status-icon {
  margin-bottom: 20rpx;
}

.status-text {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 订单信息 */
.order-info-section,
.goods-info-section,
.payment-info-section {
  background-color: #fff;
  margin: 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  font-size: 28rpx;
}

.item-label {
  color: #666;
}

.item-value {
  color: #333;
}

.status-SUCCEEDED {
  color: #52c41a;
}

.status-CLOSED {
  color: #999;
}

.status-PROCESSING {
  color: #1890ff;
}

/* 商品信息 */
.goods-card {
  display: flex;
  padding: 20rpx 0;
}

.goods-image-container {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  overflow: hidden;
  background-color: #f5f5f5;
}

.goods-image {
  width: 100%;
  height: 100%;
}

.goods-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f8ff;
}

.goods-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 30rpx;
  color: #1890ff;
  font-weight: 500;
}

.goods-qty {
  font-size: 26rpx;
  color: #999;
}

/* 支付信息 */
.payment-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.amount-label {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.amount-value {
  display: flex;
  align-items: baseline;
}

.amount-symbol {
  font-size: 28rpx;
  color: #1890ff;
  margin-right: 4rpx;
}

.amount-number {
  font-size: 40rpx;
  color: #1890ff;
  font-weight: bold;
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.pay-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(135deg, #1890ff, #0050b3);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

/* 状态图标样式 */
.status-icon-style {
  width: 80rpx;
  height: 80rpx;
}

.icon-success::before {
  content: '';
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #fff;
  border-radius: 50%;
  top: 20rpx;
  left: 20rpx;
}

.icon-success::after {
  content: '✓';
  position: absolute;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  top: 24rpx;
  left: 32rpx;
}

.icon-close::before {
  content: '';
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #fff;
  border-radius: 50%;
  top: 20rpx;
  left: 20rpx;
}

.icon-close::after {
  content: '✕';
  position: absolute;
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  top: 26rpx;
  left: 34rpx;
}

.icon-waiting::before {
  content: '';
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #fff;
  border-radius: 50%;
  top: 20rpx;
  left: 20rpx;
}

.icon-waiting::after {
  content: '⏰';
  position: absolute;
  font-size: 24rpx;
  top: 28rpx;
  left: 32rpx;
}

/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
  z-index: 1000;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1890ff;
}

.tab-css-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}

.tab-text {
  font-size: 24rpx;
  line-height: 1;
}

/* CSS图标样式 */
.css-icon {
  width: 48rpx;
  height: 48rpx;
  position: relative;
  display: inline-block;
}

.icon-home::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 16rpx solid currentColor;
  top: 8rpx;
  left: 12rpx;
}

.icon-home::after {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 12rpx;
  background-color: currentColor;
  bottom: 8rpx;
  left: 15rpx;
}

.icon-product::before {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid currentColor;
  border-radius: 6rpx;
  top: 6rpx;
  left: 6rpx;
}

.icon-product::after {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: currentColor;
  border-radius: 3rpx;
  top: 12rpx;
  left: 12rpx;
}

.icon-user::before {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border: 2rpx solid currentColor;
  border-radius: 50%;
  top: 8rpx;
  left: 18rpx;
}

.icon-user::after {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 10rpx;
  border: 2rpx solid currentColor;
  border-top: none;
  border-radius: 0 0 9rpx 9rpx;
  bottom: 8rpx;
  left: 15rpx;
}

/* 商品占位图标 */
.icon-product-placeholder {
  width: 80rpx;
  height: 80rpx;
}

.icon-product-placeholder::before {
  content: '';
  position: absolute;
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #1890ff;
  border-radius: 8rpx;
  top: 16rpx;
  left: 16rpx;
}

.icon-product-placeholder::after {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
  top: 28rpx;
  left: 28rpx;
}