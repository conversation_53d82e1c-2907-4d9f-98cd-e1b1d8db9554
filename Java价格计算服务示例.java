/**
 * 客户等级价格体系 - Java实现示例
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */

// 1. 实体类定义

/**
 * 客户等级实体
 */
@Data
@TableName("customer_level")
public class CustomerLevel {
    @TableId(type = IdType.AUTO)
    private Integer levelId;
    
    private String levelCode;
    private String levelName;
    private Integer levelOrder;
    private BigDecimal discountRate;
    private Long minOrderAmount;
    private String description;
    private Integer status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
}

/**
 * 商品基础价格实体
 */
@Data
@TableName("product_base_price")
public class ProductBasePrice {
    @TableId(type = IdType.AUTO)
    private Long priceId;
    
    private Long productId;
    private Long skuId;
    private Long basePrice;
    private Long costPrice;
    private Long marketPrice;
    private LocalDate effectiveDate;
    private LocalDate expireDate;
    private Integer status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
}

/**
 * 数量阶梯价格实体
 */
@Data
@TableName("quantity_tier_price")
public class QuantityTierPrice {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long productId;
    private Long skuId;
    private Integer levelId;
    private Integer minQuantity;
    private Integer maxQuantity;
    private Long tierPrice;
    private BigDecimal discountRate;
    private LocalDate effectiveDate;
    private LocalDate expireDate;
    private Integer status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
}

/**
 * 客户专享价格实体
 */
@Data
@TableName("customer_exclusive_price")
public class CustomerExclusivePrice {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long partnerId;
    private Long productId;
    private Long skuId;
    private Long exclusivePrice;
    private Integer minQuantity;
    private LocalDate effectiveDate;
    private LocalDate expireDate;
    private Integer status;
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
}

// 2. 价格计算请求和响应DTO

/**
 * 价格计算请求
 */
@Data
public class PriceCalculateRequest {
    private Long partnerId;      // 合作伙伴ID
    private Long productId;      // 商品ID
    private Long skuId;          // SKU ID
    private Integer quantity;    // 数量
    private LocalDate orderDate; // 订单日期
}

/**
 * 价格计算响应
 */
@Data
public class PriceCalculateResponse {
    private Long finalPrice;           // 最终价格(分)
    private String priceType;          // 价格类型
    private String priceDescription;   // 价格说明
    private Long originalPrice;        // 原价(分)
    private BigDecimal discountRate;   // 折扣率
    private Long discountAmount;       // 折扣金额(分)
    
    // 价格明细
    private CustomerLevel customerLevel;
    private ProductBasePrice basePrice;
    private QuantityTierPrice tierPrice;
    private CustomerExclusivePrice exclusivePrice;
}

// 3. 价格计算服务接口

public interface PriceCalculateService {
    
    /**
     * 计算商品价格
     */
    PriceCalculateResponse calculatePrice(PriceCalculateRequest request);
    
    /**
     * 批量计算价格
     */
    List<PriceCalculateResponse> batchCalculatePrice(List<PriceCalculateRequest> requests);
    
    /**
     * 获取客户等级信息
     */
    CustomerLevel getCustomerLevel(Long partnerId);
    
    /**
     * 获取商品阶梯价格列表
     */
    List<QuantityTierPrice> getQuantityTierPrices(Long productId, Long skuId, Integer levelId);
}

// 4. 价格计算服务实现

@Service
@Slf4j
public class PriceCalculateServiceImpl implements PriceCalculateService {
    
    @Autowired
    private PartnerInfoMapper partnerInfoMapper;
    
    @Autowired
    private CustomerLevelMapper customerLevelMapper;
    
    @Autowired
    private ProductBasePriceMapper basePriceMapper;
    
    @Autowired
    private QuantityTierPriceMapper tierPriceMapper;
    
    @Autowired
    private CustomerExclusivePriceMapper exclusivePriceMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public PriceCalculateResponse calculatePrice(PriceCalculateRequest request) {
        log.info("开始计算价格: {}", request);
        
        PriceCalculateResponse response = new PriceCalculateResponse();
        
        try {
            // 1. 获取客户等级
            CustomerLevel customerLevel = getCustomerLevel(request.getPartnerId());
            response.setCustomerLevel(customerLevel);
            
            // 2. 检查客户专享价格
            CustomerExclusivePrice exclusivePrice = getExclusivePrice(request);
            if (exclusivePrice != null && request.getQuantity() >= exclusivePrice.getMinQuantity()) {
                response.setFinalPrice(exclusivePrice.getExclusivePrice());
                response.setPriceType("EXCLUSIVE");
                response.setPriceDescription("客户专享价格");
                response.setExclusivePrice(exclusivePrice);
                return response;
            }
            
            // 3. 检查数量阶梯价格
            QuantityTierPrice tierPrice = getQuantityTierPrice(request, customerLevel.getLevelId());
            if (tierPrice != null) {
                response.setFinalPrice(tierPrice.getTierPrice());
                response.setPriceType("TIER");
                response.setPriceDescription(String.format("数量阶梯价格(%d-%s个)", 
                    tierPrice.getMinQuantity(), 
                    tierPrice.getMaxQuantity() != null ? tierPrice.getMaxQuantity().toString() : "∞"));
                response.setTierPrice(tierPrice);
                return response;
            }
            
            // 4. 使用基础价格 + 等级折扣
            ProductBasePrice basePrice = getBasePrice(request);
            if (basePrice != null) {
                Long finalPrice = calculateDiscountPrice(basePrice.getBasePrice(), customerLevel.getDiscountRate());
                response.setFinalPrice(finalPrice);
                response.setOriginalPrice(basePrice.getBasePrice());
                response.setDiscountRate(customerLevel.getDiscountRate());
                response.setDiscountAmount(basePrice.getBasePrice() - finalPrice);
                response.setPriceType("LEVEL");
                response.setPriceDescription(String.format("%s等级价格", customerLevel.getLevelName()));
                response.setBasePrice(basePrice);
                return response;
            }
            
            throw new BusinessException("未找到有效的价格配置");
            
        } catch (Exception e) {
            log.error("价格计算失败: {}", request, e);
            throw new BusinessException("价格计算失败: " + e.getMessage());
        }
    }
    
    @Override
    public CustomerLevel getCustomerLevel(Long partnerId) {
        // 先从缓存获取
        String cacheKey = "customer_level:" + partnerId;
        CustomerLevel cached = (CustomerLevel) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 从数据库查询
        PartnerInfo partner = partnerInfoMapper.selectById(partnerId);
        if (partner == null || partner.getCustomerLevelId() == null) {
            throw new BusinessException("客户信息不存在或未设置等级");
        }
        
        CustomerLevel level = customerLevelMapper.selectById(partner.getCustomerLevelId());
        if (level == null || level.getStatus() != 1) {
            throw new BusinessException("客户等级配置无效");
        }
        
        // 缓存5分钟
        redisTemplate.opsForValue().set(cacheKey, level, 5, TimeUnit.MINUTES);
        
        return level;
    }
    
    private CustomerExclusivePrice getExclusivePrice(PriceCalculateRequest request) {
        QueryWrapper<CustomerExclusivePrice> wrapper = new QueryWrapper<>();
        wrapper.eq("partner_id", request.getPartnerId())
               .eq("product_id", request.getProductId())
               .eq("sku_id", request.getSkuId())
               .eq("status", 1)
               .le("effective_date", request.getOrderDate())
               .and(w -> w.isNull("expire_date").or().ge("expire_date", request.getOrderDate()))
               .orderByDesc("effective_date")
               .last("LIMIT 1");
        
        return exclusivePriceMapper.selectOne(wrapper);
    }
    
    private QuantityTierPrice getQuantityTierPrice(PriceCalculateRequest request, Integer levelId) {
        QueryWrapper<QuantityTierPrice> wrapper = new QueryWrapper<>();
        wrapper.eq("product_id", request.getProductId())
               .eq("sku_id", request.getSkuId())
               .and(w -> w.isNull("level_id").or().eq("level_id", levelId))
               .eq("status", 1)
               .le("min_quantity", request.getQuantity())
               .and(w -> w.isNull("max_quantity").or().ge("max_quantity", request.getQuantity()))
               .le("effective_date", request.getOrderDate())
               .and(w -> w.isNull("expire_date").or().ge("expire_date", request.getOrderDate()))
               .orderByDesc("level_id")  // 优先使用特定等级的价格
               .orderByDesc("min_quantity")  // 优先使用更高数量档的价格
               .last("LIMIT 1");
        
        return tierPriceMapper.selectOne(wrapper);
    }
    
    private ProductBasePrice getBasePrice(PriceCalculateRequest request) {
        QueryWrapper<ProductBasePrice> wrapper = new QueryWrapper<>();
        wrapper.eq("product_id", request.getProductId())
               .eq("sku_id", request.getSkuId())
               .eq("status", 1)
               .le("effective_date", request.getOrderDate())
               .and(w -> w.isNull("expire_date").or().ge("expire_date", request.getOrderDate()))
               .orderByDesc("effective_date")
               .last("LIMIT 1");
        
        return basePriceMapper.selectOne(wrapper);
    }
    
    private Long calculateDiscountPrice(Long originalPrice, BigDecimal discountRate) {
        if (discountRate == null) {
            discountRate = BigDecimal.ONE;
        }
        return originalPrice.multiply(discountRate).longValue();
    }
    
    @Override
    public List<QuantityTierPrice> getQuantityTierPrices(Long productId, Long skuId, Integer levelId) {
        QueryWrapper<QuantityTierPrice> wrapper = new QueryWrapper<>();
        wrapper.eq("product_id", productId)
               .eq("sku_id", skuId)
               .and(w -> w.isNull("level_id").or().eq("level_id", levelId))
               .eq("status", 1)
               .le("effective_date", LocalDate.now())
               .and(w -> w.isNull("expire_date").or().ge("expire_date", LocalDate.now()))
               .orderByAsc("min_quantity");
        
        return tierPriceMapper.selectList(wrapper);
    }
}

// 5. 控制器示例

@RestController
@RequestMapping("/api/price")
@Slf4j
public class PriceController {
    
    @Autowired
    private PriceCalculateService priceCalculateService;
    
    /**
     * 计算商品价格
     */
    @PostMapping("/calculate")
    public Result<PriceCalculateResponse> calculatePrice(@RequestBody PriceCalculateRequest request) {
        PriceCalculateResponse response = priceCalculateService.calculatePrice(request);
        return Result.success(response);
    }
    
    /**
     * 获取商品阶梯价格
     */
    @GetMapping("/tier/{productId}/{skuId}")
    public Result<List<QuantityTierPrice>> getTierPrices(
            @PathVariable Long productId,
            @PathVariable Long skuId,
            @RequestParam Long partnerId) {
        
        CustomerLevel level = priceCalculateService.getCustomerLevel(partnerId);
        List<QuantityTierPrice> tierPrices = priceCalculateService.getQuantityTierPrices(
            productId, skuId, level.getLevelId());
        
        return Result.success(tierPrices);
    }
}
