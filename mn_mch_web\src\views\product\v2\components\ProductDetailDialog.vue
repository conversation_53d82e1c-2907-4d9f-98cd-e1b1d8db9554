<template>
  <el-dialog
    title="商品详情"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
    append-to-body
  >
    <div v-loading="loading" class="product-detail">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础信息 -->
        <el-tab-pane label="基础信息" name="basic">
          <div class="basic-info">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>商品ID：</label>
                  <span>{{ productDetail.productId }}</span>
                </div>
                <div class="info-item">
                  <label>商品名称：</label>
                  <span>{{ productDetail.productName }}</span>
                </div>
                <div class="info-item">
                  <label>商品编码：</label>
                  <span>{{ productDetail.productCode }}</span>
                </div>
                <div class="info-item">
                  <label>商品分类：</label>
                  <span>{{ productDetail.categoryName }}</span>
                </div>
                <div class="info-item">
                  <label>商品简介：</label>
                  <span>{{ productDetail.productBrief }}</span>
                </div>
                <div class="info-item">
                  <label>库存数量：</label>
                  <span>{{ productDetail.stock }}</span>
                </div>
                <div class="info-item">
                  <label>销量：</label>
                  <span>{{ productDetail.sales }}</span>
                </div>
                <div class="info-item">
                  <label>单位：</label>
                  <span>{{ productDetail.unit }}</span>
                </div>
                <div class="info-item">
                  <label>重量：</label>
                  <span>{{ productDetail.weight }}kg</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>商品状态：</label>
                  <el-tag :type="productDetail.status ? 'success' : 'danger'">
                    {{ productDetail.status ? '上架' : '下架' }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <label>是否有规格：</label>
                  <el-tag :type="productDetail.hasSpec ? 'success' : 'info'">
                    {{ productDetail.hasSpec ? '是' : '否' }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <label>是否推荐：</label>
                  <el-tag :type="productDetail.isRecommend ? 'success' : 'info'">
                    {{ productDetail.isRecommend ? '是' : '否' }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <label>是否新品：</label>
                  <el-tag :type="productDetail.isNew ? 'warning' : 'info'">
                    {{ productDetail.isNew ? '是' : '否' }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <label>是否热销：</label>
                  <el-tag :type="productDetail.isHot ? 'danger' : 'info'">
                    {{ productDetail.isHot ? '是' : '否' }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <label>排序：</label>
                  <span>{{ productDetail.sortOrder }}</span>
                </div>
                <div class="info-item">
                  <label>创建时间：</label>
                  <span>{{ parseTime(productDetail.createTime) }}</span>
                </div>
                <div class="info-item">
                  <label>更新时间：</label>
                  <span>{{ parseTime(productDetail.updateTime) }}</span>
                </div>
                <div class="info-item">
                  <label>创建者：</label>
                  <span>{{ productDetail.createBy }}</span>
                </div>
              </el-col>
            </el-row>
            
            <!-- 商品图片 -->
            <div class="image-section">
              <h4>商品图片</h4>
              <div class="image-gallery">
                <el-image
                  v-if="productDetail.mainImage"
                  :src="productDetail.mainImage"
                  :preview-src-list="allImages"
                  class="main-image"
                  fit="cover"
                >
                  <div slot="placeholder" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <el-image
                  v-for="(image, index) in subImages"
                  :key="index"
                  :src="image"
                  :preview-src-list="allImages"
                  class="sub-image"
                  fit="cover"
                >
                  <div slot="placeholder" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
            </div>

            <!-- 商品详情 -->
            <div class="detail-section" v-if="productDetail.productDetail">
              <h4>商品详情</h4>
              <div class="detail-content" v-html="productDetail.productDetail"></div>
            </div>
          </div>
        </el-tab-pane>

        <!-- SKU信息 -->
        <el-tab-pane label="SKU信息" name="sku">
          <el-table :data="productDetail.skuList" border>
            <el-table-column label="SKU ID" prop="skuId" width="80" />
            <el-table-column label="SKU编码" prop="skuCode" width="150" />
            <el-table-column label="规格信息" prop="specData" />
            <el-table-column label="库存" prop="stock" width="80" />
            <el-table-column label="销量" prop="sales" width="80" />
            <el-table-column label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status ? 'success' : 'danger'">
                  {{ scope.row.status ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="SKU图片" width="100">
              <template slot-scope="scope">
                <el-image
                  v-if="scope.row.image"
                  :src="scope.row.image"
                  :preview-src-list="[scope.row.image]"
                  style="width: 50px; height: 50px"
                  fit="cover"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 基础价格 -->
        <el-tab-pane label="基础价格" name="basePrice">
          <el-table :data="productDetail.basePriceList" border>
            <el-table-column label="价格ID" prop="priceId" width="80" />
            <el-table-column label="SKU ID" prop="skuId" width="80" />
            <el-table-column label="基础价格" width="100">
              <template slot-scope="scope">
                <span>¥{{ scope.row.basePriceYuan }}</span>
              </template>
            </el-table-column>
            <el-table-column label="成本价格" width="100">
              <template slot-scope="scope">
                <span>¥{{ scope.row.costPriceYuan }}</span>
              </template>
            </el-table-column>
            <el-table-column label="市场价格" width="100">
              <template slot-scope="scope">
                <span>¥{{ scope.row.marketPriceYuan }}</span>
              </template>
            </el-table-column>
            <el-table-column label="生效日期" prop="effectiveDate" width="120" />
            <el-table-column label="失效日期" prop="expireDate" width="120" />
            <el-table-column label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status ? 'success' : 'danger'">
                  {{ scope.row.status ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 等级价格 -->
        <el-tab-pane label="等级价格" name="levelPrice">
          <el-table :data="productDetail.levelPriceList" border>
            <el-table-column label="等级" prop="levelName" width="100" />
            <el-table-column label="SKU ID" prop="skuId" width="80" />
            <el-table-column label="等级价格" width="100">
              <template slot-scope="scope">
                <span>¥{{ scope.row.priceYuan }}</span>
              </template>
            </el-table-column>
            <el-table-column label="折扣率" prop="discountRate" width="100" />
            <el-table-column label="生效日期" prop="effectiveDate" width="120" />
            <el-table-column label="失效日期" prop="expireDate" width="120" />
            <el-table-column label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status ? 'success' : 'danger'">
                  {{ scope.row.status ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 阶梯价格 -->
        <el-tab-pane label="阶梯价格" name="tierPrice">
          <el-table :data="productDetail.tierPriceList" border>
            <el-table-column label="等级" prop="levelName" width="100" />
            <el-table-column label="SKU ID" prop="skuId" width="80" />
            <el-table-column label="数量范围" prop="quantityRange" width="100" />
            <el-table-column label="阶梯价格" width="100">
              <template slot-scope="scope">
                <span>¥{{ scope.row.tierPriceYuan }}</span>
              </template>
            </el-table-column>
            <el-table-column label="折扣率" prop="discountRate" width="100" />
            <el-table-column label="生效日期" prop="effectiveDate" width="120" />
            <el-table-column label="失效日期" prop="expireDate" width="120" />
            <el-table-column label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status ? 'success' : 'danger'">
                  {{ scope.row.status ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getProductV2 } from "@/api/product/v2";
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "ProductDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    productId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      activeTab: 'basic',
      productDetail: {},
      subImages: [],
      allImages: []
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  watch: {
    visible(val) {
      if (val && this.productId) {
        this.getProductDetail();
      }
    }
  },
  methods: {
    parseTime,
    /** 获取商品详情 */
    getProductDetail() {
      this.loading = true;
      const params = {
        includePrice: true,
        includeSku: true,
        includeLevelPrice: true,
        includeTierPrice: true
      };
      
      getProductV2(this.productId, params).then(response => {
        this.productDetail = response.data || {};
        
        // 处理图片
        this.processImages();
        
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 处理图片 */
    processImages() {
      this.allImages = [];
      this.subImages = [];
      
      if (this.productDetail.mainImage) {
        this.allImages.push(this.productDetail.mainImage);
      }
      
      if (this.productDetail.subImages) {
        try {
          this.subImages = JSON.parse(this.productDetail.subImages);
          this.allImages = this.allImages.concat(this.subImages);
        } catch (e) {
          console.error('解析子图片失败:', e);
        }
      }
    },
    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false;
      this.activeTab = 'basic';
      this.productDetail = {};
    }
  }
};
</script>

<style lang="scss" scoped>
.product-detail {
  .basic-info {
    .info-item {
      display: flex;
      margin-bottom: 15px;
      
      label {
        width: 120px;
        font-weight: bold;
        color: #606266;
      }
      
      span {
        flex: 1;
        color: #303133;
      }
    }
  }
  
  .image-section {
    margin-top: 30px;
    
    h4 {
      margin-bottom: 15px;
      color: #303133;
    }
    
    .image-gallery {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      
      .main-image {
        width: 150px;
        height: 150px;
        border: 2px solid #409eff;
        border-radius: 8px;
      }
      
      .sub-image {
        width: 100px;
        height: 100px;
        border: 1px solid #dcdfe6;
        border-radius: 8px;
      }
      
      .image-slot {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        color: #909399;
        font-size: 30px;
      }
    }
  }
  
  .detail-section {
    margin-top: 30px;
    
    h4 {
      margin-bottom: 15px;
      color: #303133;
    }
    
    .detail-content {
      padding: 15px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #fafafa;
      max-height: 300px;
      overflow-y: auto;
    }
  }
}
</style>
