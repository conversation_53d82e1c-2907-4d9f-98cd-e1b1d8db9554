package com.mn.controller;

import com.alibaba.fastjson.JSON;
import com.mn.config.ClientConfig;
import com.mn.util.AjaxResult;
import com.mn.util.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.Objects;
import java.util.UUID;

@RestController
@RequestMapping("/common")
@Slf4j
public class CommonController {

    @Resource
    ClientConfig clientConfig;



    /**
     * 通用上传请求（单个）
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) {
        try {
            // 上传文件路径
            log.info("开始上传");
            String filePath = clientConfig.imgPath;
            File imageFolder = new File(filePath);
            //获取文件后缀名
            String fileSuffix = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf("."));
            //上传文件名生成规则-UUID前六位+文件后缀名
            String newFileName = UUID.randomUUID().toString().replaceAll("-", "") + fileSuffix;
            File newFile = new File(imageFolder, newFileName);
            //判断父路径是否存在，如果不存在则新建。
            if (!newFile.getParentFile().exists()) {
                newFile.getParentFile().mkdirs();
            }
            //把内存中File类对象信息写入磁盘
            file.transferTo(newFile);
            String url = clientConfig.baseUrl + newFileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", url);
            ajax.put("newFileName", url);
            ajax.put("originalFilename", url);
//            ajax.put("newFileName", FileUtils.getName(newFileName));
//            ajax.put("originalFilename", file.getOriginalFilename());
//            log.info("上传成功"+ JSON.toJSONString(ajax));
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }


}
