package com.mn.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.entity.QuantityTierPrice;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

/**
 * <p>
 * 数量阶梯价格表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
public interface QuantityTierPriceMapper extends BaseMapper<QuantityTierPrice> {

    /**
     * 获取有效的阶梯价格
     *
     * @param productId 商品ID
     * @param skuId SKU ID
     * @param levelId 客户等级ID
     * @param quantity 购买数量
     * @param orderDate 订单日期
     * @return 阶梯价格
     */
    QuantityTierPrice getEffectiveTierPrice(@Param("productId") Long productId,
                                           @Param("skuId") Long skuId,
                                           @Param("levelId") Integer levelId,
                                           @Param("quantity") Integer quantity,
                                           @Param("orderDate") LocalDate orderDate);
}
