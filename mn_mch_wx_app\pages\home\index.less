@import '/variable.less';

@home-tab-item-height: 96rpx;

.home-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 顶部企业信息区域 */
.header-section {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 40rpx 30rpx 30rpx;
  color: #fff;
}

.company-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.company-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.company-logo image {
  width: 60rpx;
  height: 60rpx;
}

.logo-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
}

.company-details {
  flex: 1;
}

.company-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.company-subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.8;
}

.header-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 20rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  margin: 0 30rpx;
}

/* 扩展统计信息 */
.extended-stats {
  display: flex;
  margin-top: 20rpx;
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 12rpx;
  padding: 16rpx;
}

.extended-stat-item {
  flex: 1;
  text-align: center;
}

.extended-stat-number {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 6rpx;
  color: #fff;
}

.extended-stat-label {
  display: block;
  font-size: 22rpx;
  opacity: 0.7;
  color: #fff;
}

/* 快捷功能区域 */
.function-section {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-link {
  font-size: 26rpx;
  color: #1890ff;
  font-weight: normal;
}

.function-grid {
  display: flex;
  flex-wrap: wrap;
  margin: -10rpx;
}

.function-item {
  width: calc(25% - 20rpx);
  margin: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.function-item:active {
  background-color: #f8f9fa;
  transform: scale(0.95);
}

.function-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.function-icon {
  width: 48rpx;
  height: 48rpx;
}

/* CSS图标基础样式 */
.css-icon {
  width: 48rpx;
  height: 48rpx;
  position: relative;
  display: inline-block;
}

/* 产品图标 */
.icon-product::before {
  content: '';
  position: absolute;
  width: 36rpx;
  height: 36rpx;
  border: 4rpx solid #fff;
  border-radius: 8rpx;
  top: 6rpx;
  left: 6rpx;
}

.icon-product::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background-color: #fff;
  border-radius: 4rpx;
  top: 14rpx;
  left: 14rpx;
}

/* 订单图标 */
.icon-order::before {
  content: '';
  position: absolute;
  width: 32rpx;
  height: 40rpx;
  border: 4rpx solid #fff;
  border-radius: 4rpx;
  top: 4rpx;
  left: 8rpx;
}

.icon-order::after {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: #fff;
  top: 16rpx;
  left: 16rpx;
  box-shadow: 0 6rpx 0 #fff, 0 12rpx 0 #fff;
}

/* 库存图标 */
.icon-inventory::before {
  content: '';
  position: absolute;
  width: 6rpx;
  height: 24rpx;
  background-color: #fff;
  bottom: 8rpx;
  left: 8rpx;
}

.icon-inventory::after {
  content: '';
  position: absolute;
  width: 6rpx;
  height: 32rpx;
  background-color: #fff;
  bottom: 8rpx;
  left: 18rpx;
  box-shadow: 10rpx 8rpx 0 #fff, 20rpx 0rpx 0 #fff;
}

/* 财务图标 */
.icon-finance::before {
  content: '';
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid #fff;
  border-radius: 50%;
  top: 8rpx;
  left: 8rpx;
}

.icon-finance::after {
  content: '¥';
  position: absolute;
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
  top: 14rpx;
  left: 18rpx;
}

.function-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
}



/* 价格图标 */
.icon-price::before {
  content: '';
  position: absolute;
  width: 32rpx;
  height: 20rpx;
  border: 4rpx solid #fff;
  border-radius: 4rpx;
  top: 14rpx;
  left: 8rpx;
}

.icon-price::after {
  content: '$';
  position: absolute;
  color: #fff;
  font-size: 18rpx;
  font-weight: bold;
  top: 16rpx;
  left: 18rpx;
}

/* 报表图标 */
.icon-report::before {
  content: '';
  position: absolute;
  width: 6rpx;
  height: 16rpx;
  background-color: #fff;
  bottom: 8rpx;
  left: 8rpx;
}

.icon-report::after {
  content: '';
  position: absolute;
  width: 6rpx;
  height: 24rpx;
  background-color: #fff;
  bottom: 8rpx;
  left: 18rpx;
  box-shadow: 10rpx -8rpx 0 #fff, 20rpx -16rpx 0 #fff;
}

/* 服务图标 */
.icon-service::before {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 4rpx solid #fff;
  border-radius: 50%;
  top: 12rpx;
  left: 12rpx;
}

.icon-service::after {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 8rpx;
  border: 3rpx solid #fff;
  border-top: none;
  border-radius: 0 0 16rpx 16rpx;
  bottom: 6rpx;
  left: 16rpx;
}

/* 通知图标 */
.icon-notice::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-bottom: 24rpx solid #fff;
  top: 8rpx;
  left: 8rpx;
}

.icon-notice::after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background-color: #fff;
  border-radius: 50%;
  bottom: 8rpx;
  left: 20rpx;
}

/* 首页图标 */
.icon-home::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-bottom: 20rpx solid currentColor;
  top: 8rpx;
  left: 8rpx;
}

.icon-home::after {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 16rpx;
  background-color: currentColor;
  bottom: 8rpx;
  left: 12rpx;
}

/* 用户图标 */
.icon-user::before {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  border: 3rpx solid currentColor;
  border-radius: 50%;
  top: 8rpx;
  left: 16rpx;
}

.icon-user::after {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 12rpx;
  border: 3rpx solid currentColor;
  border-top: none;
  border-radius: 0 0 12rpx 12rpx;
  bottom: 8rpx;
  left: 12rpx;
}

/* 底部导航CSS图标 */
.tab-css-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}

/* 最新动态区域 */
.news-section {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.06);
}

.news-list {
  margin-top: 20rpx;
}

.news-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx 0;
  position: relative;
}

.news-item:not(:last-child) {
  border-bottom: 1rpx solid #f0f0f0;
}

.news-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #1890ff;
  margin-top: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.news-content {
  flex: 1;
}

.news-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.news-time {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.bottom-space {
  height: 40rpx;
}