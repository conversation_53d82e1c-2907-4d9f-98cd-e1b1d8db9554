// 格式化规格数据显示
export function formatSpecData(specData) {
  if (!specData) return '';
  try {
    const specObj = JSON.parse(specData);
    if (Array.isArray(specObj)) {
      return specObj.map(item => `${item.specName}:${item.value}`).join(', ');
    } else {
      return Object.keys(specObj).map(key => `${key.split(':')[1]}:${specObj[key]}`).join(', ');
    }
  } catch (e) {
    return specData;
  }
}

// 格式化价格显示
export function formatPrice(price) {
  if (price === undefined || price === null) return '0.00';
  return parseFloat(price).toFixed(2);
}

// 格式化金额显示（分转元）
export function formatAmount(amount) {
  if (amount === undefined || amount === null) return '0.00';
  return (amount / 100).toFixed(2);
}
