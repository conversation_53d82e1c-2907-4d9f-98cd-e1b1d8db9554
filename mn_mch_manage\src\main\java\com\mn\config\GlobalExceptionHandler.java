package com.mn.config;

import com.mn.util.MeloonException;
import com.mn.util.MisRspModel;
import com.mn.util.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.xml.bind.ValidationException;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

	@ResponseBody
	@ExceptionHandler(value = ValidationException.class)
	public MisRspModel validationExceptionHandler(ValidationException exception) {
		String msg= exception.getMessage();
		log.info(msg);
		return new MisRspModel(-1,msg);
	}
	
	
	@ResponseBody
	@ExceptionHandler(value = MeloonException.class)
	public MisRspModel meloonExceptionclassHandler(MeloonException exception) {
		String msg= exception.getMsg();
		log.info(msg);
		return new MisRspModel(exception.getCode(),msg);
	}

	@ResponseBody
	@ExceptionHandler(value = ServiceException.class)
	public MisRspModel meloonExceptionclassHandler(ServiceException exception) {
		String msg= exception.getMessage();
		log.info(msg);
		return new MisRspModel(exception.getCode(),msg);
	}

}
