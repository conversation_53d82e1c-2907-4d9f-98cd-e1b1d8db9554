package com.mn.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mn.config.EntpayProperties;
import com.mn.service.ISalesOrderPayService;
import com.mn.util.StringUtils;
import com.mn.util.ysPay.YsPaySignUtils;
import com.tenpay.business.entpay.mse.sdk.model.PaymentNotifyModel;
import com.tenpay.business.entpay.mse.sdk.model.RefundNotifyModel;

import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
public class NotifyController {

    @Resource
    EntpayProperties entpayProperties;

    @Resource
    ISalesOrderPayService salesOrderPayService;

    @ResponseBody
    @PostMapping("/ysNotify")
    public String ysNotify(HttpServletRequest request) {
        log.info("【银盛支付异步通知】收到异步通知请求");

        try {
            // 获取请求参数
            Map<String, String[]> parameterMap = request.getParameterMap();
            Map<String, String> params = new HashMap<>();
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String[] values = entry.getValue();
                if (values != null && values.length > 0) {
                    params.put(entry.getKey(), values[0]);
                }
            }

            // 提取关键字段
            String outTradeNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            String tradeStatus = params.get("trade_status");
            String totalAmount = params.get("total_amount");
            String sign = params.get("sign");

            log.info("【银盛支付异步通知】订单号: {}, 交易号: {}, 状态: {}, 金额: {}",
                    outTradeNo, tradeNo, tradeStatus, totalAmount);

            // 验证必要参数
            if (StringUtils.isBlank(outTradeNo) || StringUtils.isBlank(tradeStatus) || StringUtils.isBlank(sign)) {
                log.error("【银盛支付异步通知】缺少必要参数: outTradeNo={}, tradeStatus={}, sign={}",
                        outTradeNo, tradeStatus, sign);
                return "fail";
            }

            // 验证签名
            try {
                boolean signValid = YsPaySignUtils.asynVerifyYs(params);
                if (!signValid) {
                    log.error("【银盛支付异步通知】签名验证失败");
                    return "fail";
                }
                log.info("【银盛支付异步通知】签名验证成功");
            } catch (Exception e) {
                log.error("【银盛支付异步通知】签名验证异常: {}", e.getMessage(), e);
                return "fail";
            }

            // 映射银盛支付状态到系统内部状态
            String systemStatus = mapYsPayStatusToSystem(tradeStatus);
            log.info("【银盛支付异步通知】状态映射: {} -> {}", tradeStatus, systemStatus);

            // 同步订单状态
            salesOrderPayService.querySalesOrderPay(outTradeNo, systemStatus);

            log.info("【银盛支付异步通知】订单状态同步完成");
            return "success";

        } catch (Exception e) {
            log.error("【银盛支付异步通知】处理异步通知异常: {}", e.getMessage(), e);
            return "fail";
        }
    }

    /**
     * 映射银盛支付状态到系统内部状态
     */
    private String mapYsPayStatusToSystem(String tradeStatus) {
        switch (tradeStatus) {
            case "WAIT_BUYER_PAY":
                return "USERPAYING";
            case "TRADE_SUCCESS":
                return "SUCCESS";
            case "TRADE_CLOSED":
                return "CLOSED";
            case "TRADE_PART_REFUND":
                return "SUCCESS"; // 部分退款仍视为支付成功
            case "TRADE_ALL_REFUND":
                return "REFUND";
            case "OVERPAYMENT":
                return "SUCCESS"; // 超金额支付视为成功
            case "TRADE_PROCESS":
                return "USERPAYING";
            case "TRADE_FAILED":
                return "PAYERROR";
            default:
                log.warn("【银盛支付异步通知】未知的交易状态: {}", tradeStatus);
                return "PAYERROR";
        }
    }

    // 支付回调
    @ResponseBody
    @PostMapping("/payNotify")
    public String paymentNotify(HttpServletRequest request, @RequestBody String body,
            @RequestHeader("TBEP-Authorization") String authorization) throws Exception {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("retcode", 0);
        jsonObject.put("retmsg", "SUCCESS");
        // StringBuffer requestURL = request.getRequestURL();
        // 验签并获取model
        // PaymentNotifyModel model = NotifyHandler.handlerWebhook(body, authorization,
        // PaymentNotifyModel.class,
        // RsaUtil.convertToPublicKey(entpayProperties.getTbepPublicKey()));
        PaymentNotifyModel paymentNotifyModel = JSON.parseObject(body, PaymentNotifyModel.class);
        if (paymentNotifyModel == null) {
            log.error("支付回调失败,解析失败");
            return jsonObject.toString();
        }
        log.info("支付回调:{}", JSON.toJSONString(paymentNotifyModel));
        switch (paymentNotifyModel.getEventType()) {
            case "mse_payment.succeeded":
                log.info("支付回调成功" + paymentNotifyModel.getEventContent().getOutPaymentId());
                // merchantPayOrderService.queryPayOrder(paymentNotifyModel.getEventContent().getOutPaymentId(),
                // "SUCCEEDED");
                salesOrderPayService.querySalesOrderPay(paymentNotifyModel.getEventContent().getOutPaymentId(),
                        "SUCCESS");
                break;
            case "mse_payment.close":
                log.info("支付关闭订单");
                // merchantPayOrderService.queryPayOrder(paymentNotifyModel.getEventContent().getOutPaymentId(),
                // "CLOSED");
                salesOrderPayService.querySalesOrderPay(paymentNotifyModel.getEventContent().getOutPaymentId(),
                        "CLOSED");
                break;
            case "mse_payment.revoke":
                log.info("支付退款");
                // merchantPayOrderService.queryPayOrder(paymentNotifyModel.getEventContent().getOutPaymentId(),
                // "REFUND");
                salesOrderPayService.querySalesOrderPay(paymentNotifyModel.getEventContent().getOutPaymentId(),
                        "REFUND");
                break;
            default:
                log.info("未知的回调事件:" + paymentNotifyModel.getEventType());
        }
        return jsonObject.toString();
    }

    // 退款回调也需要同样修改
    @PostMapping("/refundNotify")
    public String refundNotify(HttpServletRequest request, @RequestBody String body,
            @RequestHeader("TBEP-Authorization") String authorization) throws Exception {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("retcode", 0);
        jsonObject.put("retmsg", "SUCCESS");
        // 验签并获取model
        // RefundNotifyModel model = NotifyHandler.handlerWebhook(body, authorization,
        // RefundNotifyModel.class,
        // RsaUtil.getPublicKey(null));
        RefundNotifyModel model = JSON.parseObject(body, RefundNotifyModel.class);
        if (model == null) {
            log.error("退款回调失败,解析失败");
            return jsonObject.toString();
        }
//        merchantPayOrderService.queryRefundOrder(model.getEventContent().getOutPaymentId(),
//                model.getEventContent().getStatus());
        log.info("退款回调,model:{}", JSONObject.toJSONString(model));
        return jsonObject.toString();
    }

}
