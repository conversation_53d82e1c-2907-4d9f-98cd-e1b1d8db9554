/**
 * 格式化工具函数
 */

/**
 * 格式化金额（分转元）
 * @param {number} amount - 金额（分）
 * @param {number} precision - 小数位数，默认2位
 * @returns {string} 格式化后的金额字符串
 */
export function formatAmount(amount, precision = 2) {
  if (!amount && amount !== 0) return '0.00';
  return (amount / 100).toFixed(precision);
}

/**
 * 格式化金额（元转分）
 * @param {number} yuan - 金额（元）
 * @returns {number} 金额（分）
 */
export function yuanToFen(yuan) {
  if (!yuan && yuan !== 0) return 0;
  return Math.round(yuan * 100);
}

/**
 * 格式化规格数据
 * @param {string} specData - 规格数据JSON字符串
 * @returns {string} 格式化后的规格字符串
 */
export function formatSpecData(specData) {
  if (!specData) return '';
  
  try {
    const specs = JSON.parse(specData);
    if (Array.isArray(specs)) {
      return specs.map(spec => `${spec.name}:${spec.value}`).join(', ');
    } else if (typeof specs === 'object') {
      return Object.entries(specs).map(([key, value]) => `${key}:${value}`).join(', ');
    }
    return specData;
  } catch (error) {
    return specData;
  }
}

/**
 * 格式化百分比
 * @param {number} value - 数值（0-1之间）
 * @param {number} precision - 小数位数，默认1位
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercent(value, precision = 1) {
  if (!value && value !== 0) return '0%';
  return (value * 100).toFixed(precision) + '%';
}

/**
 * 格式化数字，添加千分位分隔符
 * @param {number} num - 数字
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(num) {
  if (!num && num !== 0) return '0';
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (!bytes && bytes !== 0) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * 格式化时间差
 * @param {Date|string|number} startTime - 开始时间
 * @param {Date|string|number} endTime - 结束时间，默认为当前时间
 * @returns {string} 格式化后的时间差
 */
export function formatTimeDiff(startTime, endTime = new Date()) {
  const start = new Date(startTime);
  const end = new Date(endTime);
  const diff = end - start;
  
  if (diff < 0) return '0秒';
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) return `${days}天${hours % 24}小时`;
  if (hours > 0) return `${hours}小时${minutes % 60}分钟`;
  if (minutes > 0) return `${minutes}分钟${seconds % 60}秒`;
  return `${seconds}秒`;
}

/**
 * 格式化手机号码
 * @param {string} phone - 手机号码
 * @returns {string} 格式化后的手机号码
 */
export function formatPhone(phone) {
  if (!phone) return '';
  const phoneStr = phone.toString();
  if (phoneStr.length === 11) {
    return phoneStr.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3');
  }
  return phone;
}

/**
 * 格式化身份证号码
 * @param {string} idCard - 身份证号码
 * @returns {string} 格式化后的身份证号码
 */
export function formatIdCard(idCard) {
  if (!idCard) return '';
  const idStr = idCard.toString();
  if (idStr.length === 18) {
    return idStr.replace(/(\d{6})(\d{8})(\d{4})/, '$1********$3');
  }
  return idCard;
}

/**
 * 格式化银行卡号
 * @param {string} bankCard - 银行卡号
 * @returns {string} 格式化后的银行卡号
 */
export function formatBankCard(bankCard) {
  if (!bankCard) return '';
  const cardStr = bankCard.toString();
  if (cardStr.length >= 16) {
    const start = cardStr.substring(0, 4);
    const end = cardStr.substring(cardStr.length - 4);
    const middle = '*'.repeat(cardStr.length - 8);
    return `${start}${middle}${end}`;
  }
  return bankCard;
}

/**
 * 格式化价格类型显示文本
 * @param {string} priceType - 价格类型
 * @returns {string} 显示文本
 */
export function formatPriceType(priceType) {
  const typeMap = {
    'TIER_PRICE': '阶梯价格',
    'LEVEL_PRICE': '等级价格',
    'BASE_PRICE': '基础价格'
  };
  return typeMap[priceType] || '基础价格';
}

/**
 * 格式化客户等级
 * @param {number} levelOrder - 等级排序
 * @returns {string} 等级显示文本
 */
export function formatCustomerLevel(levelOrder) {
  if (levelOrder <= 1) return 'VIP';
  if (levelOrder <= 2) return '金牌';
  if (levelOrder <= 3) return '银牌';
  if (levelOrder <= 4) return '铜牌';
  return '普通';
}

/**
 * 格式化订单状态
 * @param {number} status - 状态值
 * @returns {string} 状态显示文本
 */
export function formatOrderStatus(status) {
  const statusMap = {
    0: '待确认',
    1: '已确认',
    2: '生产中',
    3: '已发货',
    4: '已完成',
    5: '已取消'
  };
  return statusMap[status] || '未知状态';
}

/**
 * 格式化支付状态
 * @param {number} payStatus - 支付状态
 * @returns {string} 支付状态显示文本
 */
export function formatPayStatus(payStatus) {
  const statusMap = {
    0: '未支付',
    1: '已支付',
    2: '部分支付',
    3: '已退款'
  };
  return statusMap[payStatus] || '未知状态';
}
