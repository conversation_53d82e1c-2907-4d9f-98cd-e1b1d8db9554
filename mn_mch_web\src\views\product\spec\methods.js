import { listSpec, getSpec, addSpec, updateSpec, delSpec } from "@/api/product/spec";
import { parseTime } from "@/utils/ruoyi";

export default {
  /** 查询规格列表 */
  getList() {
    this.loading = true;
    const params = {
      ...this.queryParams
    };
    // 处理日期范围
    if (this.dateRange && this.dateRange.length === 2) {
      params.beginTime = this.dateRange[0];
      params.endTime = this.dateRange[1];
    }

    listSpec(params).then(response => {
      this.specList = response.rows;
      this.total = response.total;
      this.loading = false;
    });
  },
  /** 搜索按钮操作 */
  handleQuery() {
    this.queryParams.pageNum = 1;
    this.getList();
  },
  /** 重置按钮操作 */
  resetQuery() {
    this.dateRange = [];
    this.resetForm("queryForm");
    this.handleQuery();
  },
  /** 新增按钮操作 */
  handleAdd() {
    this.reset();
    this.open = true;
    this.title = "添加规格";
    this.form.operationType = 'add';
  },
  /** 查看详情按钮操作 */
  handleDetail(row) {
    this.detailForm = { ...row };
    // 解析规格值
    this.detailSpecValues = this.parseSpecValues(row.specValues);
    this.detailOpen = true;
  },
  /** 删除按钮操作 */
  handleDelete(row) {
    const specId = row.specId;
    this.$modal.confirm('是否确认删除规格ID为"' + specId + '"的数据项？').then(function () {
      return delSpec(specId);
    }).then(() => {
      this.getList();
      this.$modal.msgSuccess("删除成功");
    }).catch(() => { });
  },
  /** 取消按钮 */
  cancel() {
    this.open = false;
    this.reset();
  },
  // 添加规格值
  addSpecValue() {
    this.specValuesList.push({
      value: ''
    });
  },
  // 移除规格值
  removeSpecValue(index) {
    this.specValuesList.splice(index, 1);
  },
  // 表单重置
  reset() {
    this.form = {
      specId: undefined,
      specName: undefined,
      specValues: undefined,
      sortOrder: 0,
      createTime: undefined
    };
    this.specValuesList = [
      {
        value: ''
      }
    ];
    this.resetForm("form");
  },
  /** 修改按钮操作 */
  handleUpdate(row) {
    this.reset();
    const specId = row.specId;
    getSpec(specId).then(response => {
      this.form = response.data;
      this.form.operationType = 'update';

      // 解析规格值
      if (this.form.specValues) {
        try {
          const specValues = JSON.parse(this.form.specValues);
          this.specValuesList = specValues.map(value => ({ value }));
        } catch (e) {
          console.error("解析规格值失败", e);
          this.specValuesList = [{ value: '' }];
        }
      }

      this.open = true;
      this.title = "修改规格";
    });
  },
  /** 提交按钮 */
  submitForm() {
    this.$refs["form"].validate(valid => {
      if (valid) {
        // 过滤空值并构造规格值JSON数组
        const specValues = this.specValuesList
          .filter(item => item.value.trim() !== '')
          .map(item => item.value.trim());

        if (specValues.length === 0) {
          this.$message.warning("请至少添加一个规格值");
          return;
        }

        const submitData = { ...this.form };
        submitData.specValues = JSON.stringify(specValues);

        if (submitData.operationType === 'update') {
          updateSpec(submitData).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
        } else {
          addSpec(submitData).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      }
    });
  },
  /** 解析规格值 */
  parseSpecValues(specValues) {
    try {
      if (typeof specValues === 'string') {
        return JSON.parse(specValues);
      } else if (Array.isArray(specValues)) {
        return specValues;
      }
    } catch (e) {
      console.error('解析规格值失败', e);
    }
    return [];
  }
};