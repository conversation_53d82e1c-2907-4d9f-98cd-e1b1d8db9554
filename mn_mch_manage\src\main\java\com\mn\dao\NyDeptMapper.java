package com.mn.dao;

import com.mn.entity.NyDept;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.form.DeptForm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
public interface NyDeptMapper extends BaseMapper<NyDept> {

    List<NyDept> listDept(DeptForm dept);

    List<NyDept> selectChildrenDeptById(Integer deptId);

    int updateDeptChildren(@Param("depts") List<NyDept> depts);

    int countChildByDeptId(Integer deptId);

    int countUserByDeptId(Integer deptId);

    NyDept selectDeptByUid(String uid);

}
