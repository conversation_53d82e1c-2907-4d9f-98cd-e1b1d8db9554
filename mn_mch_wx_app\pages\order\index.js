import API from '../../api/request';
import Message from 'tdesign-miniprogram/message/index';

Page({
  data: {
    activeTab: 'ALL', // 当前选中的标签
    orderList: [], // 订单列表
    page: 1, // 当前页码
    pageSize: 10, // 每页数量
    hasMore: true, // 是否有更多数据
    isLoading: false, // 是否正在加载
  },

  onLoad() {
    this.fetchOrderList(true);
  },

  onPullDownRefresh() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.fetchOrderList(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadMore();
    }
  },

  // 切换标签
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.activeTab) {
      this.setData({
        activeTab: tab,
        page: 1,
        orderList: [],
        hasMore: true
      });
      this.fetchOrderList(true);
    }
  },

  // 获取订单列表
  async fetchOrderList(isRefresh = false) {
    if (this.data.isLoading) return;

    this.setData({ isLoading: true });

    try {
      const params = {
        orderPageNum: this.data.page,
        orderPageSize: this.data.pageSize
      };

      // 如果不是全部，则添加状态筛选
      if (this.data.activeTab !== 'ALL') {
        params.status = this.data.activeTab;
      }

      const res = await API.listOrder(params);
      
      if (res && res.data) {
        const formattedOrders = res.data.map(order => {
          // 解析商品信息
          let goodsList = [];
          try {
            goodsList = JSON.parse(order.goodsInfo || '[]');
          } catch (e) {
            console.error('解析商品信息失败', e);
          }

          // 格式化创建时间
          const createTime = new Date(order.createTime);
          const createTimeFormatted = `${createTime.getFullYear()}-${String(createTime.getMonth() + 1).padStart(2, '0')}-${String(createTime.getDate()).padStart(2, '0')} ${String(createTime.getHours()).padStart(2, '0')}:${String(createTime.getMinutes()).padStart(2, '0')}`;

          return {
            ...order,
            goodsList,
            goodsCount: goodsList.reduce((sum, good) => sum + good.good_number, 0),
            createTimeFormatted
          };
        });

        this.setData({
          orderList: isRefresh ? formattedOrders : [...this.data.orderList, ...formattedOrders],
          hasMore: formattedOrders.length === this.data.pageSize,
          page: this.data.page + 1
        });
      } else {
        this.setData({ hasMore: false });
      }
    } catch (error) {
      console.error('获取订单列表失败', error);
      Message.error({
        context: this,
        offset: [20, 32],
        content: '获取订单列表失败'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 加载更多
  loadMore() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.fetchOrderList();
    }
  },

  // 跳转到订单详情
  goToOrderDetail(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.navigateTo({
      url: `/pages/order/detail/index?orderId=${orderId}`
    });
  },

  // 支付订单
  async payOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;
    try {
      const res = await API.payOrder(orderId);
      if (res && res.data) {
        // 支付成功后刷新列表
        this.setData({
          page: 1
        });
        this.fetchOrderList(true);
        
        Message.success({
          context: this,
          offset: [20, 32],
          content: '支付成功'
        });
      }
    } catch (error) {
      console.error('支付失败', error);
      Message.error({
        context: this,
        offset: [20, 32],
        content: '支付失败'
      });
    }
  }
});