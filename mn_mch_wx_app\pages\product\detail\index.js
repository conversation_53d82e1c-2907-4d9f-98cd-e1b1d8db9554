import API from '../../../api/request';

Page({
  data: {
    productId: '',
    productInfo: {},
    skuList: [],
    imageList: [],
    selectedSkuId: '',
    selectedSku: null,
    minPrice: 0,
    maxPrice: 0,
    totalStock: 0,
    totalQuantity: 0,
    totalAmount: 0,
    showSkuPopup: false,
    popupType: '', // 'cart' 或 'order'
    specGroups: [], // 规格组
    isLoading: true
  },

  // 价格转换函数：分转元
  formatPrice: function(priceInCents) {
    if (!priceInCents && priceInCents !== 0) return '0.00';
    return (priceInCents / 100).toFixed(2);
  },

  onLoad: function(options) {
    if (options.id) {
      this.setData({
        productId: options.id
      });
      this.getProductDetail(options.id);
    }
  },

  // 获取产品详情
  getProductDetail: function(id) {
    wx.showLoading({
      title: '加载中...',
    });

    API.getProductDetail(id).then(res => {
      if (res.code === 0 && res.data) {
        const { productInfo, skuList } = res.data;

        // 处理图片列表
        let imageList = [];
        if (productInfo.mainImage) {
          imageList.push(productInfo.mainImage);
        }
        if (productInfo.subImages) {
          const subImages = productInfo.subImages.split(',');
          imageList = imageList.concat(subImages);
        }

        // 计算价格范围和库存
        let minPrice = productInfo.price;
        let maxPrice = productInfo.price;
        let totalStock = productInfo.stock;

        // 处理SKU列表，添加数量字段并解析规格数据
        if (skuList && skuList.length > 0) {
          const processedSkuList = skuList.map(sku => {
            // 解析specData
            let specValues = [];
            let skuName = sku.skuCode;

            if (sku.specData) {
              try {
                const specArray = JSON.parse(sku.specData);
                specValues = specArray.map(spec => spec.value);
                skuName = specValues.join(' '); // 用规格值组合作为显示名称
              } catch (e) {
                console.error('解析specData失败:', e);
              }
            }

            return {
              ...sku,
              quantity: 0,
              skuName: skuName,
              specValues: specValues,
              price: this.formatPrice(sku.price) // 转换价格：分转元
            };
          });

          // 提取规格组信息
          const specGroups = this.extractSpecGroups(skuList);

          minPrice = this.formatPrice(Math.min(...skuList.map(sku => sku.price)));
          maxPrice = this.formatPrice(Math.max(...skuList.map(sku => sku.price)));
          totalStock = processedSkuList.reduce((sum, sku) => sum + sku.stock, 0);

          this.setData({
            productInfo: {
              ...productInfo,
              price: this.formatPrice(productInfo.price) // 转换主产品价格
            },
            skuList: processedSkuList,
            imageList: imageList,
            minPrice: minPrice,
            maxPrice: maxPrice,
            totalStock: totalStock,
            specGroups: specGroups,
            isLoading: false
          });
        } else {
          this.setData({
            productInfo: {
              ...productInfo,
              price: this.formatPrice(productInfo.price) // 转换主产品价格
            },
            skuList: [],
            imageList: imageList,
            minPrice: this.formatPrice(minPrice),
            maxPrice: this.formatPrice(maxPrice),
            totalStock: totalStock,
            isLoading: false
          });
        }
      } else {
        wx.showToast({
          title: res.message || '获取产品详情失败',
          icon: 'none'
        });
      }
      wx.hideLoading();
    }).catch(err => {
      console.error('获取产品详情失败', err);
      wx.showToast({
        title: '获取产品详情失败',
        icon: 'none'
      });
      wx.hideLoading();
    });
  },

  // 提取规格组信息
  extractSpecGroups: function(skuList) {
    const specGroupMap = new Map();

    skuList.forEach(sku => {
      if (sku.specData) {
        try {
          const specArray = JSON.parse(sku.specData);
          specArray.forEach(spec => {
            if (!specGroupMap.has(spec.specName)) {
              specGroupMap.set(spec.specName, new Set());
            }
            specGroupMap.get(spec.specName).add(spec.value);
          });
        } catch (e) {
          console.error('解析specData失败:', e);
        }
      }
    });

    // 转换为数组格式
    const specGroups = [];
    specGroupMap.forEach((values, name) => {
      specGroups.push({
        name: name,
        values: Array.from(values)
      });
    });

    return specGroups;
  },

  // 显示规格选择弹窗
  showSkuPopup: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      showSkuPopup: true,
      popupType: type
    });
  },

  // 隐藏规格选择弹窗
  hideSkuPopup: function() {
    this.setData({
      showSkuPopup: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡
  },

  // 选择SKU
  selectSku: function(e) {
    const skuId = e.currentTarget.dataset.id;
    const selectedSku = this.data.skuList.find(sku => sku.skuId == skuId);

    if (selectedSku) {
      this.setData({
        selectedSkuId: skuId,
        selectedSku: selectedSku
      });
    }
  },

  // 减少SKU数量
  decreaseSkuQuantity: function(e) {
    const skuId = e.currentTarget.dataset.id;
    const skuList = this.data.skuList.map(sku => {
      if (sku.skuId == skuId && sku.quantity > 0) {
        sku.quantity -= 1;
      }
      return sku;
    });

    this.setData({
      skuList: skuList
    });
    this.calculateTotal();
  },

  // 增加SKU数量
  increaseSkuQuantity: function(e) {
    const skuId = e.currentTarget.dataset.id;
    const skuList = this.data.skuList.map(sku => {
      if (sku.skuId == skuId && sku.quantity < sku.stock) {
        sku.quantity += 1;
      }
      return sku;
    });

    this.setData({
      skuList: skuList
    });
    this.calculateTotal();
  },

  // 输入SKU数量
  onSkuQuantityInput: function(e) {
    const skuId = e.currentTarget.dataset.id;
    let value = parseInt(e.detail.value);

    const skuList = this.data.skuList.map(sku => {
      if (sku.skuId == skuId) {
        if (isNaN(value) || value < 0) {
          value = 0;
        } else if (value > sku.stock) {
          value = sku.stock;
          wx.showToast({
            title: '已达最大库存',
            icon: 'none'
          });
        }
        sku.quantity = value;
      }
      return sku;
    });

    this.setData({
      skuList: skuList
    });
    this.calculateTotal();
  },

  // 计算总计
  calculateTotal: function() {
    const skuList = this.data.skuList;
    let totalQuantity = 0;
    let totalAmount = 0;

    skuList.forEach(sku => {
      totalQuantity += sku.quantity;
      totalAmount += sku.quantity * parseFloat(sku.price); // sku.price已经是元为单位
    });

    this.setData({
      totalQuantity: totalQuantity,
      totalAmount: totalAmount.toFixed(2)
    });
  },

  // 确认选择
  confirmSelection: function() {
    if (this.data.totalQuantity === 0) {
      wx.showToast({
        title: '请选择商品规格和数量',
        icon: 'none'
      });
      return;
    }

    if (this.data.popupType === 'cart') {
      this.addToCart();
    } else {
      this.createOrder();
    }
  },

  // 加入购物车
  addToCart: function() {
    // 这里可以添加购物车逻辑
    wx.showToast({
      title: '已加入购物车',
      icon: 'success'
    });
    this.hideSkuPopup();
  },

  // 回到首页
  goHome: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 联系客服
  contactService: function() {
    wx.showModal({
      title: '联系客服',
      content: '是否要联系客服？',
      confirmText: '联系',
      cancelText: '取消',
      success: function(res) {
        if (res.confirm) {
          // 这里可以添加客服联系逻辑，比如跳转到客服页面或拨打电话
          wx.showToast({
            title: '正在为您转接客服',
            icon: 'success'
          });
        }
      }
    });
  },

  // 创建订单
  createOrder: function() {
    wx.showLoading({
      title: '创建订单中...',
    });

    // 收集选中的SKU信息
    const selectedSkus = this.data.skuList.filter(sku => sku.quantity > 0);

    if (selectedSkus.length === 0) {
      wx.hideLoading();
      wx.showToast({
        title: '请选择商品规格',
        icon: 'none'
      });
      return;
    }

    const orderData = {
      productId: this.data.productInfo.productId,
      skus: selectedSkus.map(sku => ({
        skuId: sku.skuId,
        quantity: sku.quantity
      })),
      totalAmount: this.data.totalAmount,
      totalQuantity: this.data.totalQuantity
    };

    API.createOrder(orderData).then(res => {
      wx.hideLoading();
      console.log('创建订单成功:', res);

      // API模块返回的是完整响应：{code: 0, data: {...}, msg: "成功"}
      if (res && res.code === 0 && res.data && res.data.orderNo) {
        this.hideSkuPopup();
        // 跳转到订单详情页，使用orderNo而不是orderId
        wx.navigateTo({
          url: `/pages/order/detail/index?orderId=${res.data.orderNo}`
        });
      } else {
        console.error('创建订单返回数据异常:', res);
        wx.showToast({
          title: res.msg || '创建订单失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('创建订单失败', err);
      wx.showToast({
        title: '创建订单失败',
        icon: 'none'
      });
    });
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.imageList
    });
  },

  onShareAppMessage: function() {
    return {
      title: this.data.productInfo.productName,
      path: `/pages/product/detail/index?id=${this.data.productId}`
    };
  }
});