<view class="order-detail-container">
  <!-- 订单状态 -->
  <view class="order-status-section {{orderDetail.status === 'SUCCEEDED' ? 'success' : (orderDetail.status === 'CLOSED' ? 'closed' : 'processing')}}">
    <view class="status-icon">
      <view class="css-icon status-icon-style">
        <view wx:if="{{orderDetail.status === 'SUCCEEDED'}}" class="icon-success"></view>
        <view wx:elif="{{orderDetail.status === 'CLOSED'}}" class="icon-close"></view>
        <view wx:else class="icon-waiting"></view>
      </view>
    </view>
    <view class="status-text">
      <text wx:if="{{orderDetail.status === 'SUCCEEDED'}}">支付成功</text>
      <text wx:elif="{{orderDetail.status === 'CLOSED'}}">订单已关闭</text>
      <text wx:else>等待支付</text>
    </view>
    <view class="status-desc" wx:if="{{orderDetail.status === 'PROCESSING'}}">
      <text>请在 {{countDown}} 内完成支付</text>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-info-section">
    <view class="section-title">订单信息</view>
    <view class="info-item">
      <text class="item-label">订单编号</text>
      <text class="item-value">{{orderDetail.orderId}}</text>
    </view>
    <view class="info-item">
      <text class="item-label">创建时间</text>
      <text class="item-value">{{createTimeFormatted}}</text>
    </view>
    <view class="info-item" wx:if="{{orderDetail.status === 'SUCCEEDED'}}">
      <text class="item-label">支付时间</text>
      <text class="item-value">{{orderDetail.payTime}}</text>
    </view>
    <view class="info-item">
      <text class="item-label">订单状态</text>
      <text class="item-value status-{{orderDetail.status}}">
        {{orderDetail.status === 'SUCCEEDED' ? '已付款' : (orderDetail.status === 'CLOSED' ? '已关闭' : '待付款')}}
      </text>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="goods-info-section" wx:if="{{goodsList && goodsList.length > 0}}">
    <view class="section-title">商品信息</view>
    <view class="goods-card" wx:for="{{goodsList}}" wx:key="index">
      <view class="goods-image-container">
        <image wx:if="{{item.good_img}}" class="goods-image" src="{{item.good_img}}" mode="aspectFill"></image>
        <view wx:else class="goods-image-placeholder">
          <view class="css-icon icon-product-placeholder"></view>
        </view>
      </view>
      <view class="goods-content">
        <view class="goods-name">{{item.good_name}}</view>
        <view class="goods-price-qty">
          <text class="goods-price">¥{{item.good_amount/100}}</text>
          <text class="goods-qty">x{{item.good_number}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 支付信息 -->
  <view class="payment-info-section">
    <view class="section-title">支付信息</view>
    <view class="payment-amount">
      <text class="amount-label">支付金额</text>
      <view class="amount-value">
        <text class="amount-symbol">¥</text>
        <text class="amount-number">{{orderDetail.amount/100}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-action-bar" wx:if="{{orderDetail.status === 'PROCESSING'}}">
    <button class="pay-btn" bindtap="payOrder">立即支付</button>
  </view>

  <!-- 使用统一的custom-tab-bar，删除旧的导航栏 -->

  <t-message id="t-message" />
</view>