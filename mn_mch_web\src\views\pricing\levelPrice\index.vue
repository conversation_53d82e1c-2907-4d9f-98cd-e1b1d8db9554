<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户等级" prop="levelId">
        <el-select v-model="queryParams.levelId" placeholder="请选择客户等级" clearable>
          <el-option
            v-for="level in levelList"
            :key="level.levelId"
            :label="level.levelName"
            :value="level.levelId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['pricing:levelPrice:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['pricing:levelPrice:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['pricing:levelPrice:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="priceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="价格ID" align="center" prop="priceId" />
      <el-table-column label="商品名称" align="center" prop="productName" />
      <el-table-column label="SKU编码" align="center" prop="skuCode" />
      <el-table-column label="客户等级" align="center" prop="levelName" />
      <el-table-column label="等级价格" align="center" prop="levelPrice">
        <template slot-scope="scope">
          ¥{{ scope.row.levelPrice }}
        </template>
      </el-table-column>
      <el-table-column label="折扣率" align="center" prop="discountRate">
        <template slot-scope="scope">
          {{ (scope.row.discountRate * 100).toFixed(2) }}%
        </template>
      </el-table-column>
      <el-table-column label="生效时间" align="center" prop="effectiveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.effectiveTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['pricing:levelPrice:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['pricing:levelPrice:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改等级价格对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="商品" prop="productId">
              <el-select v-model="form.productId" placeholder="请选择商品" @change="handleProductChange">
                <el-option
                  v-for="product in productList"
                  :key="product.productId"
                  :label="product.productName"
                  :value="product.productId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="SKU" prop="skuId">
              <el-select v-model="form.skuId" placeholder="请选择SKU">
                <el-option
                  v-for="sku in skuList"
                  :key="sku.skuId"
                  :label="sku.skuCode"
                  :value="sku.skuId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户等级" prop="levelId">
              <el-select v-model="form.levelId" placeholder="请选择客户等级">
                <el-option
                  v-for="level in levelList"
                  :key="level.levelId"
                  :label="level.levelName"
                  :value="level.levelId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="等级价格" prop="levelPrice">
              <el-input-number v-model="form.levelPrice" :precision="2" :step="1" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="折扣率" prop="discountRate">
              <el-input-number v-model="form.discountRate" :precision="4" :step="0.01" :max="1" :min="0" />
              <span style="margin-left: 10px; color: #999;">范围：0-1</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效时间" prop="effectiveTime">
              <el-date-picker
                v-model="form.effectiveTime"
                type="datetime"
                placeholder="选择生效时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLevelPrice, getLevelPrice, delLevelPrice, addLevelPrice, updateLevelPrice } from "@/api/pricing/price";
import { listCustomerLevel } from "@/api/pricing/price";
import { listProductInfo } from "@/api/product/info";
import { listProductSku } from "@/api/product/sku";

export default {
  name: "LevelPrice",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 等级价格表格数据
      priceList: [],
      // 客户等级列表
      levelList: [],
      // 商品列表
      productList: [],
      // SKU列表
      skuList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: null,
        levelId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        productId: [
          { required: true, message: "商品不能为空", trigger: "change" }
        ],
        skuId: [
          { required: true, message: "SKU不能为空", trigger: "change" }
        ],
        levelId: [
          { required: true, message: "客户等级不能为空", trigger: "change" }
        ],
        levelPrice: [
          { required: true, message: "等级价格不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getLevelList();
    this.getProductList();
  },
  methods: {
    /** 查询等级价格列表 */
    getList() {
      this.loading = true;
      listLevelPrice(this.queryParams).then(response => {
        this.priceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询客户等级列表 */
    getLevelList() {
      listCustomerLevel().then(response => {
        this.levelList = response.rows;
      });
    },
    /** 查询商品列表 */
    getProductList() {
      listProductInfo().then(response => {
        this.productList = response.rows;
      });
    },
    /** 商品变化时获取SKU列表 */
    handleProductChange(productId) {
      if (productId) {
        listProductSku({ productId: productId }).then(response => {
          this.skuList = response.rows;
        });
      } else {
        this.skuList = [];
      }
      this.form.skuId = null;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        priceId: null,
        productId: null,
        skuId: null,
        levelId: null,
        levelPrice: null,
        discountRate: null,
        effectiveTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.priceId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加等级价格";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const priceId = row.priceId || this.ids
      getLevelPrice(priceId).then(response => {
        this.form = response.data;
        this.handleProductChange(this.form.productId);
        this.open = true;
        this.title = "修改等级价格";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.priceId != null) {
            updateLevelPrice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLevelPrice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const priceIds = row.priceId || this.ids;
      this.$modal.confirm('是否确认删除等级价格编号为"' + priceIds + '"的数据项？').then(function() {
        return delLevelPrice(priceIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
