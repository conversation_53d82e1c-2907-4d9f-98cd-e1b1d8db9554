package com.mn.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mn.entity.MerchantInfo;
import com.mn.enums.BusinessType;
import com.mn.form.MerchantInfoForm;
import com.mn.model.TableDataInfo;
import com.mn.security.annotation.Log;
import com.mn.service.IMerchantInfoService;
import com.mn.util.AjaxResult;
import com.mn.util.SecurityUtils;
import com.mn.util.StringUtils;

/**
 * 商户信息管理Controller
 *
 * <AUTHOR>
 * @since 2025-01-28
 */
@RestController
@RequestMapping("/merchant/info")
public class MerchantInfoController extends BaseController {

    @Resource
    private IMerchantInfoService merchantInfoService;

    /**
     * 查询商户信息列表
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(MerchantInfoForm form) {
        startPage();
        form.setIsAdmin(SecurityUtils.getLoginUser().getUser().isAdmin());
        List<MerchantInfo> list = merchantInfoService.selectMerchantInfoList(form);
        return getDataTable(list);
    }

    /**
     * 获取商户信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:query')")
    @GetMapping(value = "/{merchantId}")
    public AjaxResult getInfo(@PathVariable("merchantId") Long merchantId) {
        // 检查用户权限
        if (!SecurityUtils.getLoginUser().getUser().isAdmin() &&
                !merchantInfoService.checkUserMerchantPermission(SecurityUtils.getUserId(), merchantId)) {
            return AjaxResult.error("没有权限访问该商户信息");
        }

        return AjaxResult.success(merchantInfoService.selectMerchantInfoDetail(merchantId));
    }

    /**
     * 新增商户信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:add')")
    @Log(title = "商户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MerchantInfo merchantInfo) {
        // 检查统一社会信用代码唯一性
        if (StringUtils.isNotEmpty(merchantInfo.getUnifiedSocialCreditCode()) &&
                !merchantInfoService.checkUnifiedSocialCreditCodeUnique(merchantInfo.getUnifiedSocialCreditCode(),
                        null)) {
            return AjaxResult.error("新增商户'" + merchantInfo.getMerchantShortName() + "'失败，统一社会信用代码已存在");
        }

        return toAjax(merchantInfoService.insertMerchantInfo(merchantInfo));
    }

    /**
     * 修改商户信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:edit')")
    @Log(title = "商户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MerchantInfo merchantInfo) {
        // 检查用户权限
        if (!SecurityUtils.getLoginUser().getUser().isAdmin() &&
                !merchantInfoService.checkUserMerchantPermission(SecurityUtils.getUserId(),
                        merchantInfo.getMerchantId())) {
            return AjaxResult.error("没有权限修改该商户信息");
        }

        // 检查统一社会信用代码唯一性
        if (StringUtils.isNotEmpty(merchantInfo.getUnifiedSocialCreditCode()) &&
                !merchantInfoService.checkUnifiedSocialCreditCodeUnique(merchantInfo.getUnifiedSocialCreditCode(),
                        merchantInfo.getMerchantId())) {
            return AjaxResult.error("修改商户'" + merchantInfo.getMerchantShortName() + "'失败，统一社会信用代码已存在");
        }

        return toAjax(merchantInfoService.updateMerchantInfo(merchantInfo));
    }

    /**
     * 删除商户信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:remove')")
    @Log(title = "商户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{merchantIds}")
    public AjaxResult remove(@PathVariable Long[] merchantIds) {
        // 检查用户权限
        if (!SecurityUtils.getLoginUser().getUser().isAdmin()) {
            for (Long merchantId : merchantIds) {
                if (!merchantInfoService.checkUserMerchantPermission(SecurityUtils.getUserId(), merchantId)) {
                    return AjaxResult.error("没有权限删除部分商户信息");
                }
            }
        }

        return toAjax(merchantInfoService.deleteMerchantInfoByIds(merchantIds));
    }

    /**
     * 审核商户信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:approve')")
    @Log(title = "商户管理", businessType = BusinessType.APPROVE)
    @PutMapping("/approve")
    public AjaxResult approve(@RequestBody MerchantInfo merchantInfo) {
        return toAjax(merchantInfoService.approveMerchantInfo(
                merchantInfo.getMerchantId(),
                merchantInfo.getStatus(),
                merchantInfo.getRemark()));
    }

    /**
     * 根据集团ID查询商户列表
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:query')")
    @GetMapping("/dept/{deptId}")
    public AjaxResult getMerchantsByDeptId(@PathVariable("deptId") Integer deptId) {
        List<MerchantInfo> list = merchantInfoService.selectMerchantInfoByDeptId(deptId);
        return AjaxResult.success(list);
    }

    /**
     * 查询当前用户有权限的商户列表
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:query')")
    @GetMapping("/user/merchants")
    public AjaxResult getUserMerchants() {
        List<MerchantInfo> list = merchantInfoService.selectMerchantInfoByUserId(SecurityUtils.getUserId());
        return AjaxResult.success(list);
    }
}
