import { listProductInfo, getProductInfo, addProductInfo, updateProductInfo, delProductInfo } from "@/api/product/info";
import { parseTime } from "@/utils/ruoyi";
import { listSpec } from "@/api/product/spec";
import { listCategory } from "@/api/product/category"; // 添加商品分类API导入
import request from "@/utils/request";

export default {
    /** 查询商品列表 */
    getList() {
        this.loading = true;
        const params = {
            ...this.queryParams
        };
        listProductInfo(params).then(response => {
            this.productList = response.rows;
            this.total = response.total;
            this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加商品";
        this.form.operationType = 'add';
        // 获取规格列表
        this.getSpecOptions();
        this.getCategoryOptions();
    },

    /** 查看详情按钮操作 */
    async handleDetail(row) {
        this.detailForm = { ...row };

        // 如果有规格数据，加载SKU列表
        if (row.specData) {
            try {
                // 从后端获取SKU列表
                const response = await request({
                    url: `/product-sku/listByProductId/${row.productId}`,
                    method: 'get'
                });
                if (response && response.code === 200) {
                    // 处理价格显示（后端存储单位为分，前端显示为元）
                    this.detailSkuList = response.data.map(item => {
                        // 解析规格数据
                        let specDataObj = {};
                        if (item.specData) {
                            try {
                                specDataObj = JSON.parse(item.specData);
                            } catch (e) {
                                console.error("解析规格数据失败", e);
                            }
                        }

                        return {
                            ...item,
                            specDataObj: specDataObj
                        };
                    });

                    // 提取规格列
                    this.extractDetailSpecColumns();
                }
            } catch (error) {
                console.error("获取SKU列表失败", error);
                this.$message.error("获取SKU列表失败");
            }
        }

        this.detailOpen = true;
    },

    /** 提取详情规格列 */
    extractDetailSpecColumns() {
        if (!this.detailSkuList || this.detailSkuList.length === 0) {
            this.detailSpecColumns = [];
            return;
        }

        // 从第一个SKU中提取规格信息
        const firstSku = this.detailSkuList[0];
        if (!firstSku.specDataObj) {
            return;
        }

        const specs = [];
        // 如果specDataObj是数组形式
        if (Array.isArray(firstSku.specDataObj)) {
            firstSku.specDataObj.forEach(spec => {
                if (spec.specId && spec.specName) {
                    specs.push({
                        specId: spec.specId,
                        specName: spec.specName
                    });
                }
            });
        }
        // 如果specDataObj是对象形式
        else {
            Object.keys(firstSku.specDataObj).forEach(key => {
                const specInfo = key.split(':');
                if (specInfo.length === 2) {
                    specs.push({
                        specId: specInfo[0],
                        specName: specInfo[1]
                    });
                }
            });
        }

        this.detailSpecColumns = specs;
    },

    /** 根据规格ID获取规格值 */
    getSpecValueBySpecId(sku, specId) {
        if (!sku.specDataObj) {
            return '';
        }

        // 如果specDataObj是数组形式
        if (Array.isArray(sku.specDataObj)) {
            const spec = sku.specDataObj.find(s => s.specId == specId);
            return spec ? spec.value : '';
        }
        // 如果specDataObj是对象形式
        else {
            for (const key in sku.specDataObj) {
                const specInfo = key.split(':');
                if (specInfo.length === 2 && specInfo[0] == specId) {
                    return sku.specDataObj[key];
                }
            }
            return '';
        }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
        const productId = row.productId;
        this.$modal.confirm('是否确认删除商品ID为"' + productId + '"的数据项？').then(function () {
            return delProductInfo(productId);
        }).then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
    },
    /** 取消按钮 */
    cancel() {
        this.open = false;
        this.reset();
    },
    // 表单重置
    reset() {
        this.form = {
            productId: undefined,
            categoryId: undefined,
            productName: undefined,
            productCode: undefined,
            productBrief: undefined,
            mainImage: undefined,
            specData: undefined,
            price: 0,
            stock: 0,
            sales: 0,
            unit: '件',
            weight: 0,

            status: 1,
            sortOrder: 0,
            createTime: undefined
        };

        this.resetForm("form");
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
        this.reset();
        const productId = row.productId;

        try {
            const response = await getProductInfo(productId);
            this.form = response.data;
            this.form.operationType = 'update';

            this.open = true;
            this.title = "修改商品";

            // 等待DOM更新后初始化SKU编辑器
            this.$nextTick(() => {
                if (this.$refs.skuEditor) {
                    // 设置productId会触发watch，自动重新加载SKU列表
                    this.$refs.skuEditor.productId = productId;
                }
            });
        } catch (error) {
            console.error("获取商品信息失败", error);
            this.$message.error("获取商品信息失败");
        }
    },
    /** 提交按钮 */
    async submitForm() {
        try {
            const valid = await this.$refs["form"].validate();
            if (valid) {
                const submitData = { ...this.form };

                // 先保存商品基本信息
                if (submitData.operationType === 'update') {
                    await updateProductInfo(submitData);
                } else {
                    const response = await addProductInfo(submitData);
                    // 如果是新增，需要设置productId
                    if (response.data) {
                        this.form.productId = response.data.productId;
                        this.$refs.skuEditor.productId = response.data.productId;
                    }
                }

                // 保存SKU数据
                await this.$refs.skuEditor.saveSkuData();

                this.$modal.msgSuccess(submitData.operationType === 'update' ? "修改成功" : "新增成功");
                this.open = false;
                this.getList();
            }
        } catch (error) {
            console.error("提交表单失败", error);
            this.$modal.msgError("提交失败：" + (error.message || "未知错误"));
        }
    },
    /** 获取规格列表 */
    getSpecOptions() {
        listSpec().then(response => {
            this.specOptions = response.rows.map(item => ({
                specId: item.specId,
                specName: item.specName
            }));
        });
    },

    /** 格式化状态 */
    formatStatus(row) {
        return row.status === 1 ? '上架' : '下架';
    },
    /** 格式化是否启用规格 */
    formatHasSpec(row) {
        return row.specData ? '是' : '否';
    },
    /** 格式化时间 */
    formatTime(time) {
        return time ? parseTime(time) : '-';
    },
    /** 获取商品分类选项 */
    /** 查询商品分类下拉树结构 */
    getCategoryOptions() {
        listCategory().then(response => {
            this.categoryOptions = this.handleTree(response.data, "categoryId");
        });
    },


    /** 图片上传成功处理 */
    handleImageSuccess(response) {
        if (response.code === 200) {
            // 根据实际返回的响应格式设置图片URL
            // 响应格式: { code: 200, msg: "操作成功", url: "http://...", fileName: "http://...", newFileName: "http://...", originalFilename: "http://..." }
            this.form.mainImage = response.url || response.newFileName || response.fileName;
            this.$modal.msgSuccess(response.msg || "上传成功");
        } else {
            this.$modal.msgError(response.msg || "上传失败");
        }
    },

    /** 图片上传前的校验 */
    beforeImageUpload(file) {
        const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
        const isLt2M = file.size / 1024 / 1024 < 2;

        if (!isJPG) {
            this.$message.error('上传图片只能是 JPG 或 PNG 格式!');
            return false;
        }
        if (!isLt2M) {
            this.$message.error('上传图片大小不能超过 2MB!');
            return false;
        }
        return true;
    },

    /** 获取上传文件的请求头 */
    getHeaders() {
        return {
            Authorization: this.$store.getters.token
        };
    },

    /** 获取分类名称 */
    getCategoryName(categoryId) {
        // 递归查找分类名称
        const findCategoryName = (categories, id) => {
            for (const category of categories) {
                if (category.categoryId === id) {
                    return category.categoryName;
                }
                if (category.children && category.children.length > 0) {
                    const name = findCategoryName(category.children, id);
                    if (name) return name;
                }
            }
            return null;
        };

        const name = findCategoryName(this.categoryOptions, categoryId);
        return name || '-';
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
        return {
            id: node.categoryId,
            label: node.categoryName,
            children: node.children
        };
    },
};


