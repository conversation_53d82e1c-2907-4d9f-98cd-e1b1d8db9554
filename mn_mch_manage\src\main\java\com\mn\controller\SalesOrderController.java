package com.mn.controller;

import com.mn.entity.PartnerInfo;
import com.mn.entity.SalesOrder;
import com.mn.form.SalesOrderForm;
import com.mn.model.TableDataInfo;
import com.mn.service.IPartnerInfoService;
import com.mn.service.ISalesOrderService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@RestController
@RequestMapping("/sales-order")
@Slf4j
public class SalesOrderController extends BaseController {

    @Resource
    private ISalesOrderService salesOrderService;

    @Resource
    private IPartnerInfoService partnerInfoService;

    /**
     * 获取销售单列表
     */
    @PreAuthorize("@ss.hasPermi('sales:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(SalesOrderForm form) {
        startPage();
        form.setDeptId(getDeptId());
        List<SalesOrder> list = salesOrderService.selectSalesOrderList(form);
        return getDataTable(list);
    }

    /**
     * 根据销售单ID获取详细信息
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable Long orderId) {
        SalesOrder salesOrder = salesOrderService.getById(orderId);
        if(null !=  salesOrder.getPartnerId())
        {
            PartnerInfo partnerInfo = partnerInfoService.getById(salesOrder.getPartnerId());
            salesOrder.setPartnerName(partnerInfo.getPartnerName());
        }
        return success(salesOrder);
    }

    /**
     * 新增销售单
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody SalesOrder salesOrder) {
        try {
            salesOrder.setCreateBy(getUsername());
            salesOrder.setCreateTime(DateUtils.getNowDate());
            salesOrder.setDeptId(getDeptId());
            // 执行插入操作
            salesOrderService.insertSalesOrder(salesOrder);
            // 返回新插入的销售单ID
            return AjaxResult.success("新增成功", salesOrder.getOrderId());
        } catch (Exception e) {
            log.error("新增销售单失败", e);
            return AjaxResult.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改销售单
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:edit')")
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody SalesOrder salesOrder) {
        salesOrder.setUpdateBy(getUsername());
        salesOrder.setUpdateTime(DateUtils.getNowDate());
        return toAjax(salesOrderService.updateSalesOrder(salesOrder));
    }

    /**
     * 删除销售单
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:remove')")
    @PostMapping("/del")
    public AjaxResult remove(Long orderId) {
        return toAjax(salesOrderService.deleteBySalesOrderId(orderId));
    }

    @PostMapping("/checkSalesOrder")
    public AjaxResult checkSalesOrder(Long orderId) {
        salesOrderService.checkSalesOrder(orderId);
        return success();
    }

    @PostMapping("/cancelCheck")
    public AjaxResult cancelCheck(Long orderId) {
        salesOrderService.cancelCheck(orderId);
        return success();
    }

    /**
     * 获取销售统计总览
     */
    //@PreAuthorize("@ss.hasPermi('report:count')")
    @GetMapping("/statistics/overview")
    public AjaxResult getStatisticsOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            Map<String, Object> overview = salesOrderService.getStatisticsOverview(getDeptId(), startDate, endDate);
            return success(overview);
        } catch (Exception e) {
            log.error("获取销售统计总览失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取按时间维度的销售统计
     */
    //@PreAuthorize("@ss.hasPermi('report:count')")
    @GetMapping("/statistics/byTime")
    public AjaxResult getStatisticsByTime(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "day") String timeType) {
        try {
            List<Map<String, Object>> statistics = salesOrderService.getStatisticsByTime(getDeptId(), startDate, endDate, timeType);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取按时间维度销售统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取按商品维度的销售统计
     */
    //@PreAuthorize("@ss.hasPermi('report:count')")
    @GetMapping("/statistics/byProduct")
    public AjaxResult getStatisticsByProduct(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> statistics = salesOrderService.getStatisticsByProduct(getDeptId(), startDate, endDate, limit);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取按商品维度销售统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取按客户维度的销售统计
     */
    //@PreAuthorize("@ss.hasPermi('report:count')")
    @GetMapping("/statistics/byCustomer")
    public AjaxResult getStatisticsByCustomer(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> statistics = salesOrderService.getStatisticsByCustomer(getDeptId(), startDate, endDate, limit);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取按客户维度销售统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取支付状态统计
     */
    //@PreAuthorize("@ss.hasPermi('report:count')")
    @GetMapping("/statistics/paymentStatus")
    public AjaxResult getPaymentStatusStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            List<Map<String, Object>> statistics = salesOrderService.getPaymentStatusStatistics(getDeptId(), startDate, endDate);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取支付状态统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }

    // ==================== 财务报表相关接口 ====================

    /**
     * 获取财务报表数据
     */
    //@PreAuthorize("@ss.hasPermi('finance:report:list')")
    @GetMapping("/finance/report")
    public AjaxResult getFinanceReport(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String reportType,
            @RequestParam(required = false) Integer partnerId) {
        try {
            Integer deptId = getDeptId();
            Map<String, Object> result = salesOrderService.getFinanceReport(deptId, startDate, endDate, reportType, partnerId);
            return success(result);
        } catch (Exception e) {
            log.error("获取财务报表数据失败", e);
            return error("获取财务报表数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取收款明细报表
     */
    //@PreAuthorize("@ss.hasPermi('finance:report:list')")
    @GetMapping("/finance/payment-details")
    public TableDataInfo getPaymentDetails(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Integer partnerId,
            @RequestParam(required = false) String orderNo) {
        try {
            startPage();
            Integer deptId = getDeptId();
            List<Map<String, Object>> result = salesOrderService.getPaymentDetails(deptId, startDate, endDate, partnerId, orderNo);
            return getDataTable(result);
        } catch (Exception e) {
            log.error("获取收款明细失败", e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 获取应收账款报表
     */
    //@PreAuthorize("@ss.hasPermi('finance:report:list')")
    @GetMapping("/finance/receivables")
    public TableDataInfo getReceivables(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Integer partnerId) {
        try {
            startPage();
            Integer deptId = getDeptId();
            List<Map<String, Object>> result = salesOrderService.getReceivables(deptId, startDate, endDate, partnerId);
            return getDataTable(result);
        } catch (Exception e) {
            log.error("获取应收账款失败", e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 获取月度财务汇总
     */
    //@PreAuthorize("@ss.hasPermi('finance:report:list')")
    @GetMapping("/finance/monthly-summary")
    public AjaxResult getMonthlySummary(
            @RequestParam(required = false) String year) {
        try {
            Integer deptId = getDeptId();
            List<Map<String, Object>> result = salesOrderService.getMonthlySummary(deptId, year);
            return success(result);
        } catch (Exception e) {
            log.error("获取月度财务汇总失败", e);
            return error("获取月度财务汇总失败: " + e.getMessage());
        }
    }

    /**
     * 导出财务报表
     */
    //@PreAuthorize("@ss.hasPermi('finance:report:export')")
    @PostMapping("/finance/export")
    public void exportFinanceReport(HttpServletResponse response,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String reportType,
            @RequestParam(required = false) Integer partnerId) {
        try {
            Integer deptId = getDeptId();
            salesOrderService.exportFinanceReport(response, deptId, startDate, endDate, reportType, partnerId);
        } catch (Exception e) {
            log.error("导出财务报表失败", e);
        }
    }
}
