{"name": "task_manage", "version": "0.1.0", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "serve": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "ant-design-vue": "^1.7.8", "axios": "0.24.0", "bpmn-js-token-simulation": "0.10.0", "clipboard": "2.0.8", "core-js": "3.25.3", "echarts": "5.4.0", "element-ui": "2.15.12", "file-saver": "^2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "md5": "^2.3.0", "nprogress": "0.2.0", "qrcodejs2": "^0.0.2", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-plugin-hiprint": "^0.0.52", "vue-qr": "^4.0.9", "vue-quill-editor": "^3.0.6", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xlsx": "^0.18.5", "xml-js": "1.6.11"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "bpmn-js": "7.5.0", "bpmn-js-properties-panel": "0.37.2", "camunda-bpmn-moddle": "4.4.1", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}