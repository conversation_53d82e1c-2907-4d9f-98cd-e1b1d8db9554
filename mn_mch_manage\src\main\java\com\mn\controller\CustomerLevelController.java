package com.mn.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mn.util.AjaxResult;
import com.mn.model.TableDataInfo;
import com.mn.entity.CustomerLevel;
import com.mn.service.ICustomerLevelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 客户等级管理控制器
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@RestController
@RequestMapping("/pricing/level")
@Slf4j
public class CustomerLevelController extends BaseController {

    @Autowired
    private ICustomerLevelService customerLevelService;

    /**
     * 查询客户等级列表
     */
    @PreAuthorize("@ss.hasPermi('pricing:level:list')")
    @GetMapping("/list")
    public TableDataInfo list(CustomerLevel customerLevel) {
        startPage();
        QueryWrapper<CustomerLevel> queryWrapper = new QueryWrapper<>();
        
        if (customerLevel.getLevelName() != null && !customerLevel.getLevelName().isEmpty()) {
            queryWrapper.like("level_name", customerLevel.getLevelName());
        }
        if (customerLevel.getStatus() != null) {
            queryWrapper.eq("status", customerLevel.getStatus());
        }
        
        queryWrapper.orderByAsc("level_id");
        List<CustomerLevel> list = customerLevelService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 获取客户等级详细信息
     */
    @PreAuthorize("@ss.hasPermi('pricing:level:query')")
    @GetMapping(value = "/{levelId}")
    public AjaxResult getInfo(@PathVariable("levelId") Integer levelId) {
        return success(customerLevelService.getById(levelId));
    }

    /**
     * 新增客户等级
     */
    @PreAuthorize("@ss.hasPermi('pricing:level:add')")
    @PostMapping
    public AjaxResult add(@RequestBody CustomerLevel customerLevel) {
        try {
            customerLevel.setCreateBy(getUsername());
            boolean result = customerLevelService.save(customerLevel);
            return result ? success() : error("新增客户等级失败");
        } catch (Exception e) {
            log.error("新增客户等级失败", e);
            return error("新增客户等级失败：" + e.getMessage());
        }
    }

    /**
     * 修改客户等级
     */
    @PreAuthorize("@ss.hasPermi('pricing:level:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody CustomerLevel customerLevel) {
        try {
            customerLevel.setUpdateBy(getUsername());
            boolean result = customerLevelService.updateById(customerLevel);
            return result ? success() : error("修改客户等级失败");
        } catch (Exception e) {
            log.error("修改客户等级失败", e);
            return error("修改客户等级失败：" + e.getMessage());
        }
    }

    /**
     * 删除客户等级
     */
    @PreAuthorize("@ss.hasPermi('pricing:level:remove')")
    @DeleteMapping("/{levelIds}")
    public AjaxResult remove(@PathVariable Integer[] levelIds) {
        try {
            boolean result = customerLevelService.removeByIds(Arrays.asList(levelIds));
            return result ? success() : error("删除客户等级失败");
        } catch (Exception e) {
            log.error("删除客户等级失败", e);
            return error("删除客户等级失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有启用的客户等级
     */
    @GetMapping("/active")
    public AjaxResult getActiveCustomerLevels() {
        try {
            QueryWrapper<CustomerLevel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", true);
            queryWrapper.orderByAsc("level_order");
            List<CustomerLevel> list = customerLevelService.list(queryWrapper);
            return success(list);
        } catch (Exception e) {
            log.error("获取启用的客户等级失败", e);
            return error("获取启用的客户等级失败：" + e.getMessage());
        }
    }

    /**
     * 导出客户等级列表
     */
    @PreAuthorize("@ss.hasPermi('pricing:level:export')")
    @PostMapping("/export")
    public void export(CustomerLevel customerLevel) {
        QueryWrapper<CustomerLevel> queryWrapper = new QueryWrapper<>();

        if (customerLevel.getLevelName() != null && !customerLevel.getLevelName().isEmpty()) {
            queryWrapper.like("level_name", customerLevel.getLevelName());
        }
        if (customerLevel.getStatus() != null) {
            queryWrapper.eq("status", customerLevel.getStatus());
        }

        List<CustomerLevel> list = customerLevelService.list(queryWrapper);
        // 这里可以添加导出逻辑
        log.info("导出客户等级列表，共{}条记录", list.size());
    }
}
