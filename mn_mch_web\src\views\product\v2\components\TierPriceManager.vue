<template>
  <div class="tier-price-manager">
    <div class="manager-header">
      <h4>阶梯价格设置</h4>
      <el-button type="primary" size="small" @click="handleAdd">
        <i class="el-icon-plus"></i> 添加阶梯价格
      </el-button>
    </div>

    <div class="tips">
      <el-alert
        title="阶梯价格说明"
        type="info"
        :closable="false"
        show-icon
      >
        <p>阶梯价格是根据购买数量设置的优惠价格，数量越多价格越优惠。</p>
        <p>可以针对特定客户等级设置，也可以设置为全部等级通用。</p>
        <p>阶梯价格优先级最高，会覆盖基础价格和等级价格。</p>
      </el-alert>
    </div>

    <el-table :data="tierPriceList" border style="width: 100%">
      <el-table-column label="客户等级" width="150">
        <template slot-scope="scope">
          <el-select 
            v-model="scope.row.levelId" 
            placeholder="选择客户等级"
            style="width: 100%"
            clearable
          >
            <el-option label="全部等级" :value="null" />
            <el-option
              v-for="level in customerLevels"
              :key="level.levelId"
              :label="level.levelName"
              :value="level.levelId"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="SKU" width="120">
        <template slot-scope="scope">
          <el-select 
            v-model="scope.row.skuId" 
            placeholder="选择SKU"
            style="width: 100%"
            clearable
          >
            <el-option label="全部SKU" :value="null" />
            <el-option
              v-for="sku in skuList"
              :key="sku.skuId"
              :label="getSkuDisplay(sku)"
              :value="sku.skuId"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="最小数量" width="120">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.minQuantity"
            :min="1"
            placeholder="最小数量"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="最大数量" width="120">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.maxQuantity"
            :min="scope.row.minQuantity || 1"
            placeholder="最大数量"
            style="width: 100%"
          />
          <div class="quantity-tip">留空表示无上限</div>
        </template>
      </el-table-column>
      <el-table-column label="阶梯价格(元)" width="150">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.tierPriceYuan"
            :min="0"
            :precision="2"
            placeholder="阶梯价格"
            @change="handlePriceChange(scope.row)"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="折扣率" width="120">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.discountRate"
            :min="0"
            :max="1"
            :precision="4"
            placeholder="折扣率"
            @change="handleDiscountRateChange(scope.row)"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="生效日期" width="150">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.effectiveDate"
            type="date"
            placeholder="生效日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="失效日期" width="150">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.expireDate"
            type="date"
            placeholder="失效日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleDelete(scope.$index)"
            style="color: #f56c6c"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="tierPriceList.length === 0" class="empty-state">
      <i class="el-icon-s-data"></i>
      <p>暂无阶梯价格，点击"添加阶梯价格"按钮添加</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "TierPriceManager",
  props: {
    value: {
      type: Array,
      default: () => []
    },
    skuList: {
      type: Array,
      default: () => []
    },
    hasSpec: {
      type: Boolean,
      default: false
    },
    customerLevels: {
      type: Array,
      default: () => []
    },
    basePriceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tierPriceList: []
    };
  },
  watch: {
    value: {
      handler(val) {
        this.tierPriceList = val.map(item => ({
          ...item,
          tierPriceYuan: item.tierPrice ? (item.tierPrice / 100) : 0,
          effectiveDate: this.formatDateFromTimestamp(item.effectiveDate),
          expireDate: this.formatDateFromTimestamp(item.expireDate)
        }));
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /** 添加阶梯价格 */
    handleAdd() {
      this.tierPriceList.push({
        levelId: null,
        skuId: null,
        minQuantity: 1,
        maxQuantity: null,
        tierPrice: 0,
        tierPriceYuan: 0,
        discountRate: null,
        effectiveDate: this.getCurrentDate(),
        expireDate: null,
        status: true
      });
      this.emitChange();
    },
    /** 删除阶梯价格 */
    handleDelete(index) {
      this.tierPriceList.splice(index, 1);
      this.emitChange();
    },
    /** 价格变化处理 */
    handlePriceChange(row) {
      row.tierPrice = Math.round(row.tierPriceYuan * 100);

      // 根据价格计算折扣率
      const basePrice = this.getBasePriceForSku(row.skuId);
      if (basePrice > 0 && row.tierPriceYuan > 0) {
        row.discountRate = parseFloat((row.tierPriceYuan / (basePrice / 100)).toFixed(4));
      }

      this.emitChange();
    },
    /** 折扣率变化处理 */
    handleDiscountRateChange(row) {
      // 根据折扣率计算价格
      const basePrice = this.getBasePriceForSku(row.skuId);
      if (basePrice > 0 && row.discountRate > 0) {
        row.tierPriceYuan = parseFloat(((basePrice / 100) * row.discountRate).toFixed(2));
        row.tierPrice = Math.round(row.tierPriceYuan * 100);
      }

      this.emitChange();
    },
    /** 获取SKU的基础价格 */
    getBasePriceForSku(skuId) {
      if (!skuId || !this.basePriceList) return 0;

      const basePrice = this.basePriceList.find(item => item.skuId === skuId);
      return basePrice ? basePrice.basePrice : 0;
    },
    /** 获取SKU显示名称 */
    getSkuDisplay(sku) {
      return sku.skuCode || `SKU-${sku.skuId}`;
    },
    /** 获取当前日期 */
    getCurrentDate() {
      const now = new Date();
      return now.getFullYear() + '-' +
             String(now.getMonth() + 1).padStart(2, '0') + '-' +
             String(now.getDate()).padStart(2, '0');
    },
    /** 格式化时间戳为日期字符串 */
    formatDateFromTimestamp(timestamp) {
      if (!timestamp) return null;

      // 如果已经是字符串格式，直接返回
      if (typeof timestamp === 'string') {
        return timestamp;
      }

      // 如果是时间戳，转换为日期字符串
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) return null;

      return date.getFullYear() + '-' +
             String(date.getMonth() + 1).padStart(2, '0') + '-' +
             String(date.getDate()).padStart(2, '0');
    },
    /** 发送变化事件 */
    emitChange() {
      const result = this.tierPriceList.map(item => ({
        ...item,
        tierPrice: Math.round((item.tierPriceYuan || 0) * 100)
      }));
      this.$emit('input', result);
    }
  }
};
</script>

<style lang="scss" scoped>
.tier-price-manager {
  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h4 {
      margin: 0;
      color: #303133;
    }
  }
  
  .tips {
    margin-bottom: 20px;
    
    p {
      margin: 5px 0;
      font-size: 13px;
    }
  }
  
  .quantity-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #909399;
    
    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
}
</style>
