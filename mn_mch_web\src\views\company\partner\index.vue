<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px" size="small">
      <el-form-item label="客户名称" prop="partnerName">
        <el-input v-model="queryParams.partnerName" placeholder="客户名称" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item label="联系人" prop="contactPerson">
        <el-input v-model="queryParams.contactPerson" placeholder="联系人" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item label="客户编码" prop="partnerCode">
        <el-input v-model="queryParams.partnerCode" placeholder="请输客户编码" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item label="客户等级" prop="customerLevelId">
        <el-select v-model="queryParams.customerLevelId" placeholder="请选择客户等级" clearable style="width: 200px">
          <el-option
            v-for="level in levelList"
            :key="level.levelId"
            :label="level.levelName"
            :value="level.levelId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 200px">
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增客户</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="partnerList" border stripe highlight-current-row>
      <el-table-column label="客户名称" align="center" prop="partnerName" min-width="150">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.partnerName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="客户编码" align="center" prop="partnerCode" min-width="120" />
      <el-table-column label="客户等级" align="center" prop="levelName" min-width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.levelName" type="primary">{{ scope.row.levelName }}</el-tag>
          <span v-else style="color: #999;">未设置</span>
        </template>
      </el-table-column>
      <el-table-column label="联系人" align="center" prop="contactPerson" min-width="100" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" min-width="120" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <div class="time-info">
            <div>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</div>
            <div class="time-detail">{{ parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" icon="el-icon-view" circle @click="handleDetail(scope.row)"
            title="查看详情"></el-button>
          <el-button size="mini" type="warning" icon="el-icon-edit" circle @click="handleUpdate(scope.row)"
            title="修改信息"></el-button>
          <el-button size="mini" type="danger" icon="el-icon-delete" circle @click="handleDelete(scope.row)"
            title="删除信息"></el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改合作伙伴对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <!-- 基本信息卡片 -->
        <el-card class="box-card mb-20">
          <div slot="header" class="card-header">
            <span><i class="el-icon-document"></i> 基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户名称" prop="partnerName">
                <el-input v-model="form.partnerName" placeholder="请输入客户名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户编码" prop="partnerCode">
                <el-input v-model="form.partnerCode" placeholder="请输入客户编码" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系人" prop="contactPerson">
                <el-input v-model="form.contactPerson" placeholder="请输入联系人姓名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="form.contactPhone" placeholder="请输入联系电话" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系邮箱" prop="contactEmail">
                <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户等级" prop="customerLevelId">
                <el-select v-model="form.customerLevelId" placeholder="请选择客户等级" clearable>
                  <el-option
                    v-for="level in levelList"
                    :key="level.levelId"
                    :label="level.levelName"
                    :value="level.levelId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio :label="1">启用</el-radio>
                  <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 财务信息卡片 -->
        <el-card class="box-card mb-20">
          <div slot="header" class="card-header">
            <span><i class="el-icon-money"></i> 财务信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="税号" prop="taxNumber">
                <el-input v-model="form.taxNumber" placeholder="请输入税号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户银行" prop="bankName">
                <el-input v-model="form.bankName" placeholder="请输入开户银行" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="银行账号" prop="bankAccount">
                <el-input v-model="form.bankAccount" placeholder="请输入银行账号" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 地址信息卡片 -->
        <el-card class="box-card mb-20">
          <div slot="header" class="card-header">
            <span><i class="el-icon-location"></i> 地址信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="详细地址" prop="address">
                <el-input v-model="form.address" type="textarea" :rows="3" placeholder="请输入详细地址" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 备注信息卡片 -->
        <el-card class="box-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-notebook-2"></i> 备注信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 合作伙伴详情对话框 -->
    <el-dialog title="客户详情" :visible.sync="detailOpen" width="700px" append-to-body>
      <!-- 基本信息 -->
      <div class="partner-detail-header">
        <div class="partner-basic-info">
          <div class="partner-name">
            <span class="label">客户名称：</span>
            <span class="value">{{ detailForm.partnerName }}</span>
          </div>
          <div class="partner-code">
            <span class="label">客户编码：</span>
            <span class="value">{{ detailForm.partnerCode }}</span>
          </div>
          <div class="partner-level">
            <span class="label">客户等级：</span>
            <el-tag v-if="detailForm.levelName" type="primary">{{ detailForm.levelName }}</el-tag>
            <span v-else style="color: #999;">未设置</span>
          </div>
          <div class="partner-status">
            <span class="label">状态：</span>
            <el-tag :type="detailForm.status === 1 ? 'success' : 'danger'">
              {{ detailForm.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </div>
          <div class="partner-time">
            <i class="el-icon-time"></i>
            <span>创建时间：{{ parseTime(detailForm.createTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 联系信息 -->
      <div class="partner-info-section">
        <div class="section-header">
          <i class="el-icon-user"></i>
          <span>联系信息</span>
        </div>
        <div class="partner-info-container">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="联系人">{{ detailForm.contactPerson || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ detailForm.contactPhone || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="联系邮箱">{{ detailForm.contactEmail || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="详细地址">{{ detailForm.address || '暂无' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 财务信息 -->
      <div class="partner-info-section">
        <div class="section-header">
          <i class="el-icon-money"></i>
          <span>财务信息</span>
        </div>
        <div class="partner-info-container">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="税号">{{ detailForm.taxNumber || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="开户银行">{{ detailForm.bankName || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="银行账号" :span="2">{{ detailForm.bankAccount || '暂无' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="partner-info-section" v-if="detailForm.remark">
        <div class="section-header">
          <i class="el-icon-notebook-2"></i>
          <span>备注信息</span>
        </div>
        <div class="partner-info-container">
          <div class="remark-content">{{ detailForm.remark }}</div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" icon="el-icon-close" @click="detailOpen = false">关闭详情</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";
import partnerMethods from "./methods";

export default {
  name: "CompanyPartner",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 合作伙伴表格数据
      partnerList: [],
      // 客户等级列表
      levelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        partnerName: undefined,
        contactPerson: undefined,
        partnerCode: undefined,
        customerLevelId: undefined,
        status: undefined
      },
      // 表单参数
      form: {
        partnerId: undefined,
        partnerName: undefined,
        partnerCode: undefined,
        contactPerson: undefined,
        contactPhone: undefined,
        contactEmail: undefined,
        address: undefined,
        taxNumber: undefined,
        bankName: undefined,
        bankAccount: undefined,
        customerLevelId: undefined,
        status: 1,
        remark: undefined,
        createTime: undefined,
        updateTime: undefined,
        createBy: undefined,
        updateBy: undefined,
        operationType: undefined // 操作类型：add-新增，update-修改
      },
      // 详情表单
      detailForm: {},
      // 表单校验规则
      rules: {
        partnerName: [
          { required: true, message: "请输入客户名称", trigger: "blur" },
          { min: 2, max: 100, message: "客户名称长度必须在2到100个字符之间", trigger: "blur" }
        ],
        status: [
          { required: true, message: "请选择状态", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getLevelList();
  },
  methods: {
    parseTime,
    ...partnerMethods // 展开partnerMethods对象
  }
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>