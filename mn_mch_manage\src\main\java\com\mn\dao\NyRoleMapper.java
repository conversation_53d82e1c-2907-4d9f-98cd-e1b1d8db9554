package com.mn.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.entity.NyRole;
import com.mn.form.RoleForm;
import com.mn.model.NyRoleMenu;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
public interface NyRoleMapper extends BaseMapper<NyRole> {

    List<NyRole> selectRoleList(RoleForm form);

    List<NyRole> selectRoleAll();

    int countUserRoleByRoleId(Integer roleId);

    int deleteRoleMenu(Integer roleId);

    int deleteRoleMenuByRoleId(Integer roleId);

    int batchRoleMenu(List<NyRoleMenu> roleMenuList);

    List<Integer> selectRoleListByUserId(Integer userId);

    List<NyRole> selectRoleByUserId(Integer userId);

    NyRole selectRoleByRoleName(String roleName);

    List<NyRole> companySelectRoleAll();

}
