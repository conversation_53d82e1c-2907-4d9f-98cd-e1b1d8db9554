package com.mn.dao;

import com.mn.entity.SalesOrderPay;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.form.SalesOrderPayForm;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
public interface SalesOrderPayMapper extends BaseMapper<SalesOrderPay> {

    List<SalesOrderPay> selectSalesOrderPayList(SalesOrderPayForm form);
    
    List<SalesOrderPay> selectSalesOrderPayByOutPaymentId(String outPaymentId);

    String getOrderNoByoutPaymentId(String outPaymentId);
}
