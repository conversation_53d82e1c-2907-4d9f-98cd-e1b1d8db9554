<template>
  <div class="sku-price-manager">
    <!-- 业务流程指引 -->
    <el-card class="process-guide">
      <div slot="header" class="card-header">
        <span><i class="el-icon-guide"></i> 商品规格与价格管理流程</span>
      </div>
      
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="选择规格" description="选择商品需要的规格类型"></el-step>
        <el-step title="生成SKU" description="根据规格组合生成SKU"></el-step>
        <el-step title="设置价格" description="为每个SKU设置价格信息"></el-step>
      </el-steps>
    </el-card>

    <!-- SKU管理区域 -->
    <el-card class="sku-section">
      <div slot="header" class="card-header">
        <span><i class="el-icon-collection-tag"></i> 第一步：SKU规格管理</span>
        <el-tag v-if="skuList.length > 0" type="success">{{ skuList.length }} 个SKU</el-tag>
      </div>
      
      <sku-manager
        v-model="skuList"
        :product-specs="productSpecs"
        :product-code="productCode"
        :selected-specs="selectedSpecs"
        @spec-change="handleSpecChange"
        @input="handleSkuChange"
      />
    </el-card>

    <!-- 价格管理区域 -->
    <el-card class="price-section" v-if="skuList.length > 0">
      <div slot="header" class="card-header">
        <span><i class="el-icon-coin"></i> 第二步：价格管理</span>
        <el-tag type="primary">基于 {{ skuList.length }} 个SKU</el-tag>
      </div>

      <el-tabs v-model="activePriceTab" type="card">
        <!-- 基础价格 -->
        <el-tab-pane label="基础价格" name="base">
          <base-price-manager
            v-model="basePriceList"
            :has-spec="hasSpec"
            :sku-list="skuList"
          />
        </el-tab-pane>

        <!-- 等级价格 -->
        <el-tab-pane label="等级价格" name="level">
          <level-price-manager
            v-model="levelPriceList"
            :has-spec="hasSpec"
            :sku-list="skuList"
            :customer-levels="customerLevels"
            :base-price-list="basePriceList"
          />
        </el-tab-pane>

        <!-- 阶梯价格 -->
        <el-tab-pane label="阶梯价格" name="tier">
          <tier-price-manager
            v-model="tierPriceList"
            :has-spec="hasSpec"
            :sku-list="skuList"
            :base-price-list="basePriceList"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons" v-if="skuList.length > 0">
      <el-button type="success" @click="validateAndComplete">
        <i class="el-icon-check"></i> 验证并完成设置
      </el-button>
      <el-button @click="resetAll">
        <i class="el-icon-refresh-left"></i> 重置所有设置
      </el-button>
    </div>
  </div>
</template>

<script>
import SkuManager from './SkuManager.vue';
import BasePriceManager from './BasePriceManager.vue';
import LevelPriceManager from './LevelPriceManager.vue';
import TierPriceManager from './TierPriceManager.vue';

export default {
  name: "SkuPriceManager",
  components: {
    SkuManager,
    BasePriceManager,
    LevelPriceManager,
    TierPriceManager
  },
  props: {
    // SKU数据
    skuValue: {
      type: Array,
      default: () => []
    },
    // 基础价格数据
    basePriceValue: {
      type: Array,
      default: () => []
    },
    // 等级价格数据
    levelPriceValue: {
      type: Array,
      default: () => []
    },
    // 阶梯价格数据
    tierPriceValue: {
      type: Array,
      default: () => []
    },
    // 商品规格
    productSpecs: {
      type: Array,
      default: () => []
    },
    // 商品编码
    productCode: {
      type: String,
      default: ""
    },
    // 是否有规格
    hasSpec: {
      type: Boolean,
      default: false
    },
    // 客户等级
    customerLevels: {
      type: Array,
      default: () => []
    },
    // 选中的规格ID数组
    selectedSpecs: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 当前步骤
      currentStep: 0,
      // 活动的价格标签页
      activePriceTab: 'base',
      // SKU列表
      skuList: [],
      // 价格列表
      basePriceList: [],
      levelPriceList: [],
      tierPriceList: []
    };
  },
  watch: {
    skuValue: {
      handler(val) {
        this.skuList = [...val];
        this.updateCurrentStep();
      },
      immediate: true,
      deep: true
    },
    basePriceValue: {
      handler(val) {
        this.basePriceList = [...val];
      },
      immediate: true,
      deep: true
    },
    levelPriceValue: {
      handler(val) {
        this.levelPriceList = [...val];
      },
      immediate: true,
      deep: true
    },
    tierPriceValue: {
      handler(val) {
        this.tierPriceList = [...val];
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /** 处理规格变化 */
    handleSpecChange(specData) {
      this.updateCurrentStep();
      this.$emit('spec-change', specData);
    },

    /** 处理SKU变化 */
    handleSkuChange(skuList) {
      this.skuList = skuList;
      this.updateCurrentStep();
      this.$emit('sku-change', skuList);
    },

    /** 更新当前步骤 */
    updateCurrentStep() {
      if (this.skuList.length === 0) {
        this.currentStep = 0;
      } else if (this.basePriceList.length === 0) {
        this.currentStep = 1;
      } else {
        this.currentStep = 2;
      }
    },

    /** 验证并完成设置 */
    validateAndComplete() {
      // 验证SKU
      if (this.skuList.length === 0) {
        this.$message.warning('请先添加SKU');
        return;
      }

      // 验证SKU编码
      const invalidSkus = this.skuList.filter(sku => !sku.skuCode || sku.codeCheckResult === false);
      if (invalidSkus.length > 0) {
        this.$message.warning('存在无效的SKU编码，请检查');
        return;
      }

      // 验证基础价格
      if (this.basePriceList.length === 0) {
        this.$message.warning('请设置基础价格');
        return;
      }

      this.$message.success('验证通过，设置完成！');
      this.$emit('complete', {
        skuList: this.skuList,
        basePriceList: this.basePriceList,
        levelPriceList: this.levelPriceList,
        tierPriceList: this.tierPriceList
      });
    },

    /** 重置所有设置 */
    resetAll() {
      this.$confirm('确定要重置所有设置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.skuList = [];
        this.basePriceList = [];
        this.levelPriceList = [];
        this.tierPriceList = [];
        this.currentStep = 0;
        this.$emit('reset');
        this.$message.success('已重置所有设置');
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.sku-price-manager {
  .process-guide {
    margin-bottom: 20px;
  }

  .sku-section {
    margin-bottom: 20px;
  }

  .price-section {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-weight: 600;
      color: #303133;
    }
  }

  .action-buttons {
    text-align: center;
    padding: 20px 0;

    .el-button {
      margin: 0 10px;
    }
  }

  ::v-deep .el-steps {
    margin: 20px 0;
  }
}
</style>
