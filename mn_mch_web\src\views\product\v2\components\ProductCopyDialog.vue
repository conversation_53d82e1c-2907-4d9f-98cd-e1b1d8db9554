<template>
  <el-dialog
    title="复制商品"
    :visible.sync="dialogVisible"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="原商品名称">
        <el-input v-model="product && product.productName" disabled />
      </el-form-item>
      <el-form-item label="原商品编码">
        <el-input v-model="product && product.productCode" disabled />
      </el-form-item>
      <el-form-item label="新商品名称" prop="newProductName">
        <el-input
          v-model="form.newProductName"
          placeholder="请输入新商品名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="新商品编码" prop="newProductCode">
        <el-input
          v-model="form.newProductCode"
          placeholder="请输入新商品编码"
          maxlength="50"
          show-word-limit
          @blur="checkProductCode"
        />
        <div v-if="codeCheckResult !== null" class="code-check-result">
          <i :class="codeCheckResult ? 'el-icon-success' : 'el-icon-error'" 
             :style="{ color: codeCheckResult ? '#67c23a' : '#f56c6c' }"></i>
          <span :style="{ color: codeCheckResult ? '#67c23a' : '#f56c6c' }">
            {{ codeCheckResult ? '编码可用' : '编码已存在' }}
          </span>
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { copyProductV2, checkProductV2Code } from "@/api/product/v2";

export default {
  name: "ProductCopyDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    product: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      codeCheckResult: null,
      form: {
        productId: null,
        newProductName: '',
        newProductCode: ''
      },
      rules: {
        newProductName: [
          { required: true, message: "新商品名称不能为空", trigger: "blur" },
          { min: 1, max: 100, message: "商品名称长度在 1 到 100 个字符", trigger: "blur" }
        ],
        newProductCode: [
          { required: true, message: "新商品编码不能为空", trigger: "blur" },
          { min: 1, max: 50, message: "商品编码长度在 1 到 50 个字符", trigger: "blur" },
          { pattern: /^[a-zA-Z0-9_-]+$/, message: "商品编码只能包含字母、数字、下划线和横线", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  watch: {
    visible(val) {
      if (val && this.product && this.product.productId) {
        this.initForm();
      }
    },
    'form.newProductCode'() {
      this.codeCheckResult = null;
    }
  },
  methods: {
    /** 初始化表单 */
    initForm() {
      if (this.product) {
        this.form = {
          productId: this.product.productId,
          newProductName: this.product.productName + '_副本',
          newProductCode: this.product.productCode + '_COPY'
        };
      }
      this.codeCheckResult = null;
    },
    /** 检查商品编码 */
    checkProductCode() {
      if (!this.form.newProductCode) {
        this.codeCheckResult = null;
        return;
      }
      
      checkProductV2Code(this.form.newProductCode).then(response => {
        this.codeCheckResult = response.data;
      }).catch(() => {
        this.codeCheckResult = false;
      });
    },
    /** 提交表单 */
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          return;
        }
        
        if (this.codeCheckResult === false) {
          this.$modal.msgError("商品编码已存在，请修改后重试");
          return;
        }
        
        this.loading = true;
        copyProductV2(this.form).then(response => {
          this.$modal.msgSuccess("复制成功");
          this.$emit('success');
          this.handleClose();
        }).catch(() => {
          this.loading = false;
        });
      });
    },
    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false;
      this.loading = false;
      this.codeCheckResult = null;
      this.$refs.form && this.$refs.form.resetFields();
    }
  }
};
</script>

<style lang="scss" scoped>
.code-check-result {
  margin-top: 5px;
  font-size: 12px;
  
  i {
    margin-right: 5px;
  }
}
</style>
