.payment-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

.bottom-btn-area {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.safe-area-bottom {
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.payment-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: relative;
  height: 88rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon image {
  width: 36rpx;
  height: 36rpx;
}

.title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

/* 商户信息卡片样式 */
.merchant-card {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.merchant-header {
  display: flex;
  align-items: center;
}

.merchant-logo {
  width: 80rpx;
  height: 80rpx;
  background-color: #f0f2f5;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  overflow: hidden;
}

.merchant-logo image {
  width: 60rpx;
  height: 60rpx;
}

.merchant-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.merchant-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.merchant-id {
  font-size: 24rpx;
  color: #999;
}

/* 支付信息卡片样式 */
.payment-card {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 金额输入区样式 */
.amount-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f2f5;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.amount-input {
  display: flex;
  align-items: center;
}

.currency-symbol {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.amount-value {
  flex: 1;
  font-size: 60rpx;
  height: 80rpx;
  color: #333;
  font-weight: 500;
}

/* 备注区域样式 */
.remark-section {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remark-label {
  font-size: 28rpx;
  color: #666;
}

.remark-content {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.remark-placeholder {
  color: #999;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 10rpx;
}

/* 底部按钮区域 */
.bottom-btn-area {
  margin-top: auto;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.payment-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.payment-btn.active {
  background: linear-gradient(135deg, #07c160, #10ad7a);
  color: white;
}

.payment-btn.disabled {
  background-color: #e0e0e0;
  color: #999;
}

.payment-tips {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.link {
  color: #07c160;
}