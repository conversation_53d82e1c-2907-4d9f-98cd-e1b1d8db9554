package com.mn.dao;

import com.mn.entity.ProductCategory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.form.ProductCategoryForm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface ProductCategoryMapper extends BaseMapper<ProductCategory> {

    List<ProductCategory> listCategory(ProductCategoryForm form);
    
    List<ProductCategory> selectChildrenCategoryById(Long categoryId);
    
    int updateCategoryChildren(@Param("categories") List<ProductCategory> categories);
    
    int countChildByCategoryId(Long categoryId);
    
    int countProductByCategoryId(Long categoryId);
}
