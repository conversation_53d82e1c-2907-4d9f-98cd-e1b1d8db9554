// API接口基础地址
const BASE_URL = 'https://mall.meloon.cn:18443/webapi';
//const BASE_URL = 'http://127.0.0.1:16060';

// 默认超时时间（毫秒）
const TIMEOUT = 10000;

// 存储等待中的请求队列
let pendingRequests = [];
let isRefreshingToken = false;

// 检查并刷新token
const refreshToken = async () => {
  if (isRefreshingToken) {
    // 如果正在刷新token，返回一个promise等待刷新完成
    return new Promise((resolve, reject) => {
      pendingRequests.push({ resolve, reject });
    });
  }

  isRefreshingToken = true;
  const app = getApp();

  try {
    await app.checkAndGetToken();
    const newToken = wx.getStorageSync('token');
    if (!newToken) {
      throw new Error('Token refresh failed');
    }

    // 处理等待队列中的请求
    pendingRequests.forEach(({ resolve }) => resolve(newToken));
    pendingRequests = [];
    return newToken;
  } catch (error) {
    pendingRequests.forEach(({ reject }) => reject(error));
    pendingRequests = [];
    throw error;
  } finally {
    isRefreshingToken = false;
  }
};

// 通用请求方法
const request = async (options) => {
  // 如果是获取token的请求，直接发起
  if (options.url.includes('/getToken')) {
    return sendRequest(options);
  }

  try {
    let token = wx.getStorageSync('token');
    if (!token) {
      token = await refreshToken();
    }

    const response = await sendRequest({
      ...options,
      header: {
        ...options.header,
        'Authorization': token
      }
    });

    return response;
  } catch (error) {
    if (error.isTokenError) {
      // token失效，尝试刷新token并重试请求
      try {
        await refreshToken();
        return request(options); // 使用新token重试请求
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        throw refreshError;
      }
    }
    throw error;
  }
};

// 发送请求的核心函数
const sendRequest = (options) => {
  return new Promise((resolve, reject) => {
    const requestPromise = new Promise((innerResolve, innerReject) => {
      wx.request({
        ...options,
        url: `${BASE_URL}${options.url}`,
        success: (res) => {
          if (res.data.code === 0) {
            innerResolve(res.data);
          } else if (res.data.code === -2) {
            // 标记token错误，让外层处理刷新
            const error = new Error('Token invalid');
            error.isTokenError = true;
            innerReject(error);
          } else {
            wx.showToast({
              title: res.data.msg || '请求失败',
              icon: 'none'
            });
            innerReject(new Error(res.data.msg));
          }
        },
        fail: (error) => {
          innerReject(error);
        }
      });
    });

    const timeoutPromise = new Promise((_, innerReject) => {
      setTimeout(() => {
        innerReject(new Error('请求超时'));
      }, TIMEOUT);
    });

    Promise.race([requestPromise, timeoutPromise])
      .then(resolve)
      .catch(reject);
  });
};

// API接口集合
const API = {
  // 获取token
  getToken: (code) => {
    return request({
      url: '/getToken?code=' + code,
      method: 'GET'
    }).then(res => {
      if (res.data) {
        // 将token存储到本地
        wx.setStorageSync('token', res.data.token);
        wx.setStorageSync('nickName', res.data.nickName);
        wx.setStorageSync('avatar', res.data.avatar);
        return res.data.token;
      }
      throw new Error('获取token失败');
    });
  },


  // 获取商品列表
  listProductInfo: (params) => {
    return request({
      url: '/mch/listProductInfo',
      method: 'GET',
      data: params
    });
  },

  // 获取商品详情
  getProductInfo: (productId) => {
    return request({
      url: '/mch/getProductInfo?productId=' + productId,
      method: 'GET'
    });
  },

  // ==================== 产品目录相关接口 ====================

  // 获取产品分类列表
  getProductCategories: () => {
    return request({
      url: '/mch/getProductCategories',
      method: 'GET'
    });
  },

  // 根据分类获取产品列表
  getProductsByCategory: (params) => {
    const queryString = Object.keys(params)
      .filter(key => params[key] !== null && params[key] !== undefined && params[key] !== '')
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    return request({
      url: `/mch/getProductsByCategory?${queryString}`,
      method: 'GET'
    });
  },

  // 获取产品详情（包含SKU信息）
  getProductDetail: (productId) => {
    return request({
      url: '/mch/getProductDetail?productId=' + productId,
      method: 'GET'
    });
  },

  // 搜索产品
  searchProducts: (params) => {
    const queryString = Object.keys(params)
      .filter(key => params[key] !== null && params[key] !== undefined && params[key] !== '')
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    return request({
      url: `/mch/searchProducts?${queryString}`,
      method: 'GET'
    });
  },

  // 创建订单
  createOrder: (data) => {
    return request({
      url: '/mch/createOrder',
      method: 'POST',
      data
    });
  },

  // 获取订单列表
  listOrder: (params) => {
    return request({
      url: '/mch/listOrder',
      method: 'POST',
      data: params
    });
  },

  // 获取订单详情
  getOrderDetail: (orderId) => {
    return request({
      url: '/mch/getOrderDetail?orderId=' + orderId,
      method: 'GET'
    });
  },

  // 获取商户信息
  getMerchantInfo: (uid) => {
    return request({
      url: '/mch/getMchInfo?uid=' + uid,
      method: 'GET'
    });
  },

  // 查询订单状态
  queryOrder: (outPaymentId) => {
    return request({
      url: '/mch/queryOrder?outPaymentId=' + outPaymentId,
      method: 'GET'
    });
  },

  // 支付订单
  payOrder: (orderId) => {
    return request({
      url: '/mch/payOrder?orderId=' + orderId,
      method: 'GET'
    });
  },

  // 用户主动支付
  payByUser: (params) => {
    return request({
      url: '/mch/payByUser',
      method: 'POST',
      data: params
    });
  },

  // 更新用户信息
  updateUserInfo: (data) => {
    return request({
      url: '/wx/updateUserInfo',
      method: 'POST',
      data
    });
  },

  //图像上传接口
  uploadUserImg: (data) => {
    return request({
      url: '/wx/uploadUserImg',
      method: 'POST',
      data
    });
  },

  // 获取首页统计数据
  getDashboardStats: () => {
    return request({
      url: '/mch/getDashboardStats',
      method: 'GET'
    });
  },

  // ==================== 价格计算相关接口 ====================

  // 计算商品价格
  calculatePrice: (data) => {
    return request({
      url: '/price/calculate',
      method: 'POST',
      data
    });
  },

  // 批量计算商品价格
  batchCalculatePrice: (data) => {
    return request({
      url: '/price/batchCalculate',
      method: 'POST',
      data
    });
  },

  // 获取商品价格信息
  getProductPrice: (params) => {
    const queryString = Object.keys(params)
      .filter(key => params[key] !== null && params[key] !== undefined && params[key] !== '')
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    return request({
      url: `/price/getPrice?${queryString}`,
      method: 'GET'
    });
  },
};

export default API;