<template>
  <div class="dashboard-container">
    <!-- 查询条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
        <el-form-item label="日期范围" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleDateChange">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="时间维度" prop="timeType">
          <el-select v-model="queryParams.timeType" placeholder="选择时间维度" @change="handleQuery">
            <el-option label="按天" value="day"></el-option>
            <el-option label="按周" value="week"></el-option>
            <el-option label="按月" value="month"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览卡片 -->
    <el-row :gutter="20" class="overview-row" v-if="overview">
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon" style="background-color: #409EFF;">
              <i class="el-icon-document"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">总订单数</div>
              <div class="overview-value">{{ overview.totalOrders || 0 }}</div>
              <div class="overview-desc">订单总量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon" style="background-color: #67C23A;">
              <i class="el-icon-money"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">销售总额</div>
              <div class="overview-value">¥{{ formatAmountFromCents(overview.totalAmount) }}</div>
              <div class="overview-desc">销售收入</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon" style="background-color: #E6A23C;">
              <i class="el-icon-goods"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">商品销量</div>
              <div class="overview-value">{{ overview.totalQuantity || 0 }}</div>
              <div class="overview-desc">商品数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon" style="background-color: #F56C6C;">
              <i class="el-icon-user"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">客户数量</div>
              <div class="overview-value">{{ overview.customerCount || 0 }}</div>
              <div class="overview-desc">活跃客户</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <!-- 销售趋势图 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <div slot="header" class="chart-header">
            <span>销售趋势</span>
            <el-button type="text" size="mini" @click="loadTimeStatistics">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
          <div ref="salesTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 支付状态分布 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <div slot="header" class="chart-header">
            <span>支付状态分布</span>
            <el-button type="text" size="mini" @click="loadPaymentStatusStatistics">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
          <div ref="paymentStatusChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 排行榜区域 -->
    <el-row :gutter="20" class="table-row">
      <!-- 商品销售排行 -->
      <el-col :span="12">
        <el-card class="table-card" shadow="never">
          <div slot="header" class="table-header">
            <span>商品销售排行</span>
            <el-button type="text" size="mini" @click="loadProductStatistics">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
          <div v-if="productStatistics.length === 0 && !loading" class="empty-data">
            <i class="el-icon-box"></i>
            <p>暂无商品销售数据</p>
          </div>
          <el-table v-else :data="productStatistics" v-loading="loading" size="mini" stripe height="300">
            <el-table-column type="index" label="排名" width="60" align="center">
              <template slot-scope="scope">
                <span :style="{ color: scope.$index < 3 ? '#f56c6c' : '#909399', fontWeight: scope.$index < 3 ? 'bold' : 'normal' }">
                  {{ scope.$index + 1 }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="productName" label="商品名称" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.product_name || scope.row.productName || scope.row.name || '未知商品' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="totalQuantity" label="销量" width="80" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.totalQuantity || scope.row.quantity || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="totalAmount" label="销售额" width="100" align="right">
              <template slot-scope="scope">
                <span style="color: #67C23A; font-weight: bold;">
                  ¥{{ formatAmountFromCents(scope.row.totalAmount || scope.row.amount || 0) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 客户销售排行 -->
      <el-col :span="12">
        <el-card class="table-card" shadow="never">
          <div slot="header" class="table-header">
            <span>客户销售排行</span>
            <el-button type="text" size="mini" @click="loadCustomerStatistics">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
          <el-table :data="customerStatistics" v-loading="loading" size="mini" stripe height="300">
            <el-table-column type="index" label="排名" width="60" align="center">
              <template slot-scope="scope">
                <span :style="{ color: scope.$index < 3 ? '#f56c6c' : '#909399', fontWeight: scope.$index < 3 ? 'bold' : 'normal' }">
                  {{ scope.$index + 1 }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="customerName" label="客户名称" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.partner_name || scope.row.customerName || scope.row.partnerName || scope.row.name || '未知客户' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="orderCount" label="订单数" width="80" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.orderCount || scope.row.count || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="totalAmount" label="销售额" width="100" align="right">
              <template slot-scope="scope">
                <span style="color: #67C23A; font-weight: bold;">
                  ¥{{ formatAmountFromCents(scope.row.totalAmount || scope.row.amount || 0) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  getStatisticsOverview,
  getStatisticsByTime,
  getStatisticsByProduct,
  getStatisticsByCustomer,
  getPaymentStatusStatistics
} from "@/api/sales/order";
import * as echarts from 'echarts';

export default {
  name: "Dashboard",
  data() {
    return {
      // 查询参数
      queryParams: {
        startDate: null,
        endDate: null,
        timeType: 'day'
      },
      // 日期范围
      dateRange: [],
      // 统计概览数据
      overview: null,
      // 时间维度统计数据
      timeStatistics: [],
      // 商品统计数据
      productStatistics: [],
      // 客户统计数据
      customerStatistics: [],
      // 支付状态统计数据
      paymentStatusStatistics: [],
      // 加载状态
      loading: false,
      // 图表实例
      salesTrendChart: null,
      paymentStatusChart: null
    };
  },
  created() {
    this.initDefaultDate();
    this.loadAllData();
  },
  mounted() {
    this.initCharts();
  },
  beforeDestroy() {
    if (this.salesTrendChart) {
      this.salesTrendChart.dispose();
    }
    if (this.paymentStatusChart) {
      this.paymentStatusChart.dispose();
    }
  },
  methods: {
    /** 初始化默认日期 */
    initDefaultDate() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30); // 默认30天
      this.dateRange = [this.formatDate(start), this.formatDate(end)];
      this.queryParams.startDate = this.formatDate(start);
      this.queryParams.endDate = this.formatDate(end);
    },

    /** 格式化日期 */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    /** 日期范围变化 */
    handleDateChange(val) {
      if (val) {
        this.queryParams.startDate = val[0];
        this.queryParams.endDate = val[1];
      } else {
        this.queryParams.startDate = null;
        this.queryParams.endDate = null;
      }
    },

    /** 查询 */
    handleQuery() {
      this.loadAllData();
    },

    /** 重置 */
    resetQuery() {
      this.resetForm("queryForm");
      this.initDefaultDate();
      this.queryParams.timeType = 'day';
      this.handleQuery();
    },

    /** 加载所有数据 */
    async loadAllData() {
      this.loading = true;
      try {
        await Promise.all([
          this.loadOverview(),
          this.loadTimeStatistics(),
          this.loadProductStatistics(),
          this.loadCustomerStatistics(),
          this.loadPaymentStatusStatistics()
        ]);
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$modal.msgError('加载数据失败');
      } finally {
        this.loading = false;
      }
    },

    /** 加载统计概览 */
    async loadOverview() {
      try {
        const response = await getStatisticsOverview({
          startDate: this.queryParams.startDate,
          endDate: this.queryParams.endDate
        });
        //('统计概览API响应:', response);
        if (response.code === 200) {
          this.overview = response.data;
          //console.log('统计概览数据:', this.overview);
        } else {
          console.warn('统计概览API返回错误:', response);
          this.overview = {
            totalOrders: 0,
            totalAmount: 0,
            totalQuantity: 0,
            customerCount: 0
          };
        }
      } catch (error) {
        console.error('加载统计概览失败:', error);
        this.overview = {
          totalOrders: 0,
          totalAmount: 0,
          totalQuantity: 0,
          customerCount: 0
        };
      }
    },

    /** 加载时间维度统计 */
    async loadTimeStatistics() {
      try {
        const response = await getStatisticsByTime({
          startDate: this.queryParams.startDate,
          endDate: this.queryParams.endDate,
          timeType: this.queryParams.timeType
        });
        //console.log('时间维度统计API响应:', response);
        if (response.code === 200) {
          this.timeStatistics = response.data || [];
          //console.log('时间维度统计数据:', this.timeStatistics);
          this.updateSalesTrendChart();
        } else {
          console.warn('时间维度统计API返回错误:', response);
          this.timeStatistics = [];
          this.updateSalesTrendChart();
        }
      } catch (error) {
        console.error('加载时间维度统计失败:', error);
        this.timeStatistics = [];
        this.updateSalesTrendChart();
      }
    },

    /** 加载商品统计 */
    async loadProductStatistics() {
      try {
        const response = await getStatisticsByProduct({
          startDate: this.queryParams.startDate,
          endDate: this.queryParams.endDate,
          limit: 10
        });
        //('商品统计API响应:', response);
        if (response.code === 200) {
          this.productStatistics = response.data || [];
          //console.log('商品统计数据:', this.productStatistics);
        } else {
          console.warn('商品统计API返回错误:', response);
          this.productStatistics = [];
        }
      } catch (error) {
        console.error('加载商品统计失败:', error);
        this.productStatistics = [];
      }
    },

    /** 加载客户统计 */
    async loadCustomerStatistics() {
      try {
        const response = await getStatisticsByCustomer({
          startDate: this.queryParams.startDate,
          endDate: this.queryParams.endDate,
          limit: 10
        });
        //console.log('客户统计API响应:', response);
        if (response.code === 200) {
          this.customerStatistics = response.data || [];
          //console.log('客户统计数据:', this.customerStatistics);
        } else {
          console.warn('客户统计API返回错误:', response);
          this.customerStatistics = [];
        }
      } catch (error) {
        console.error('加载客户统计失败:', error);
        this.customerStatistics = [];
      }
    },

    /** 加载支付状态统计 */
    async loadPaymentStatusStatistics() {
      try {
        const response = await getPaymentStatusStatistics({
          startDate: this.queryParams.startDate,
          endDate: this.queryParams.endDate
        });
        //console.log('支付状态统计API响应:', response);
        if (response.code === 200) {
          this.paymentStatusStatistics = response.data || [];
          //console.log('支付状态统计数据:', this.paymentStatusStatistics);
          this.updatePaymentStatusChart();
        } else {
          console.warn('支付状态统计API返回错误:', response);
          this.paymentStatusStatistics = [];
          this.updatePaymentStatusChart();
        }
      } catch (error) {
        console.error('加载支付状态统计失败:', error);
        this.paymentStatusStatistics = [];
        this.updatePaymentStatusChart();
      }
    },

    /** 初始化图表 */
    initCharts() {
      this.$nextTick(() => {
        if (this.$refs.salesTrendChart) {
          this.salesTrendChart = echarts.init(this.$refs.salesTrendChart);
        }
        if (this.$refs.paymentStatusChart) {
          this.paymentStatusChart = echarts.init(this.$refs.paymentStatusChart);
        }
      });
    },

    /** 更新销售趋势图 */
    updateSalesTrendChart() {
      if (!this.salesTrendChart) return;

      // 如果没有数据，显示空状态
      if (!this.timeStatistics.length) {
        const option = {
          title: {
            text: '销售趋势',
            left: 'center',
            textStyle: {
              fontSize: 14,
              color: '#666'
            }
          },
          graphic: {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 14,
              fill: '#999'
            }
          }
        };
        this.salesTrendChart.setOption(option);
        return;
      }

      const xData = this.timeStatistics.map(item => {
        // 处理不同的时间字段
        return item.timeLabel || item.date || item.orderDate || '未知时间';
      });

      const yData = this.timeStatistics.map(item => {
        // 处理不同的金额字段，确保转换为元
        const amount = item.totalAmount || item.amount || item.actualAmount || 0;
        return parseFloat(amount) / 100;
      });

      //console.log('销售趋势图数据:', { xData, yData, rawData: this.timeStatistics });

      const option = {
        title: {
          text: '销售趋势',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            if (params && params.length > 0) {
              const param = params[0];
              return param.name + '<br/>' +
                     param.seriesName + ': ¥' + param.value.toLocaleString('zh-CN', {
                       minimumFractionDigits: 2,
                       maximumFractionDigits: 2
                     });
            }
            return '';
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            rotate: xData.length > 5 ? 45 : 0,
            fontSize: 12
          },
          boundaryGap: xData.length === 1 ? true : false
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              return '¥' + value.toLocaleString();
            },
            fontSize: 12
          }
        },
        series: [{
          name: '销售额',
          type: 'line',
          data: yData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 2,
            color: '#409EFF'
          },
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(64, 158, 255, 0.3)'
              }, {
                offset: 1, color: 'rgba(64, 158, 255, 0.1)'
              }]
            }
          }
        }]
      };

      this.salesTrendChart.setOption(option, true);
    },

    /** 更新支付状态图 */
    updatePaymentStatusChart() {
      if (!this.paymentStatusChart) return;

      // 如果没有数据，显示空状态
      if (!this.paymentStatusStatistics.length) {
        const option = {
          title: {
            text: '支付状态分布',
            left: 'center',
            textStyle: {
              fontSize: 14,
              color: '#666'
            }
          },
          graphic: {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 14,
              fill: '#999'
            }
          }
        };
        this.paymentStatusChart.setOption(option);
        return;
      }

      const data = this.paymentStatusStatistics.map((item, index) => {
        // 定义颜色数组
        const colors = ['#67C23A', '#E6A23C', '#F56C6C', '#409EFF', '#909399'];

        // 状态名称转换
        const statusMap = {
          'PAID': '已付款',
          'UNPAID': '未付款',
          'PAYING': '付款中',
          'REFUND': '已退款',
          'CANCELLED': '已取消'
        };

        const statusName = item.statusName || item.paymentStatus || item.status || '未知状态';
        const displayName = statusMap[statusName] || statusName;

        return {
          name: displayName,
          value: item.orderCount || item.count || 0,
          itemStyle: {
            color: colors[index % colors.length]
          }
        };
      });

      //console.log('支付状态图数据:', { data, rawData: this.paymentStatusStatistics });

      const option = {
        title: {
          text: '支付状态分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return params.seriesName + '<br/>' +
                   params.name + ': ' + params.value + '笔 (' + params.percent + '%)';
          }
        },
        legend: {
          orient: 'horizontal',
          bottom: '10',
          left: 'center',
          textStyle: {
            fontSize: 12
          }
        },
        series: [{
          name: '支付状态',
          type: 'pie',
          radius: ['30%', '60%'],
          center: ['50%', '45%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: '{b}: {c}笔'
          },
          labelLine: {
            show: true
          }
        }]
      };

      this.paymentStatusChart.setOption(option, true);
    },

    /** 格式化金额（从分转换为元） */
    formatAmountFromCents(amountInCents) {
      if (!amountInCents) return '0.00';
      const amountInYuan = parseFloat(amountInCents) / 100;
      return amountInYuan.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);

  .filter-card {
    margin-bottom: 20px;

    .el-form {
      margin-bottom: 0;
    }
  }

  .overview-row {
    margin-bottom: 20px;

    .overview-card {
      height: 120px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .overview-content {
        display: flex;
        align-items: center;
        height: 100%;
        padding: 20px;

        .overview-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;

          i {
            font-size: 24px;
            color: white;
          }
        }

        .overview-info {
          flex: 1;

          .overview-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
          }

          .overview-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
          }

          .overview-desc {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }

  .chart-row {
    margin-bottom: 20px;

    .chart-card {
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-weight: bold;
          color: #333;
        }
      }

      .chart-container {
        height: 350px;
        width: 100%;
      }
    }
  }

  .table-row {
    .table-card {
      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-weight: bold;
          color: #333;
        }
      }

      .el-table {
        .el-table__header {
          th {
            background-color: #fafafa;
            color: #333;
            font-weight: bold;
          }
        }

        .el-table__body {
          tr:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dashboard-container {
    .overview-row {
      .el-col {
        margin-bottom: 10px;
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;

    .overview-row {
      .overview-card {
        height: auto;

        .overview-content {
          flex-direction: column;
          text-align: center;
          padding: 15px;

          .overview-icon {
            margin-right: 0;
            margin-bottom: 10px;
          }
        }
      }
    }

    .chart-row {
      .el-col {
        margin-bottom: 20px;
      }
    }

    .table-row {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}
</style>

