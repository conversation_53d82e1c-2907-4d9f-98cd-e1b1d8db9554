package com.mn.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.mn.service.ISalesOrderService;
import com.mn.util.MeloonException;
import com.mn.util.MisRspModel;
import com.tenpay.business.entpay.mse.sdk.exception.EntpayException;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/pay")
@Slf4j
public class QrCodeController {

    @Resource
    private ISalesOrderService salesOrderService;

    @GetMapping("/qrCodePay")
    public MisRspModel qrCodePay(String orderNo, Integer payChannel) {
        log.info("获取支付二维码");
        if (orderNo == null)
            throw new MeloonException("orderNo为空");
        if (payChannel == null)
            throw new MeloonException("payChannel为空");
        String qrCodeUrl = null;
        try {
            qrCodeUrl = salesOrderService.qrCodePay(orderNo, payChannel);
            log.info("获取支付二维码成功" + qrCodeUrl);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("qrCodeUrl", qrCodeUrl);
            return new MisRspModel(jsonObject);
        } catch (EntpayException e) {
            log.error("支付失败: {}", e.getMessage(), e);
            throw new MeloonException("支付失败: " + e.getMessage());
        }
    }

}
