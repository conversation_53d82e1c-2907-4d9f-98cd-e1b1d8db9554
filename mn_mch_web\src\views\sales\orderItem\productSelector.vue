<template>
  <div class="product-selector">
    <el-dialog
      title="选择商品"
      :visible.sync="visible"
      width="80%"
      :close-on-click-modal="false"
      :before-close="handleClose"
      :modal-append-to-body="true"
      :append-to-body="true"
      :z-index="3000"
      :destroy-on-close="true"
      top="5vh"
    >
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="queryParams" size="small">
          <el-form-item label="关键词">
            <el-input
              v-model="queryParams.keyword"
              placeholder="商品名称/编码/规格"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="分类">
            <el-select
              v-model="queryParams.categoryId"
              placeholder="商品分类"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="item in categoryOptions"
                :key="item.categoryId"
                :label="item.categoryName"
                :value="item.categoryId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 商品列表 -->
      <el-table
        ref="table"
        v-loading="loading"
        :data="productList"
        border
        size="small"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="商品编码" align="center" prop="productCode" min-width="120" />
        <el-table-column label="商品名称" align="center" prop="productName" min-width="150" />
        <el-table-column label="单位" align="center" prop="unit" width="80" />
        <el-table-column align="center" min-width="150">
          <template slot="header">
            <span>规格</span>
            <el-tooltip content="选择规格后将自动选中该商品" placement="top">
              <i class="el-icon-info" style="margin-left: 5px; color: #909399;"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.selectedSkuId"
              placeholder="选择规格"
              size="small"
              style="width: 100%"
              @focus="() => handleSkuFocus(scope.row)"
              @change="(val) => handleSkuSelect(scope.row, val)"
              :loading="scope.row.skuLoading"
            >
              <el-option
                v-for="sku in scope.row.skuList || []"
                :key="sku.skuId"
                :label="sku.skuCode + ' - ' + formatSpecData(sku.specData)"
                :value="sku.skuId"
              />
              <el-option v-if="scope.row.skuList.length === 0 && !scope.row.skuLoading" value="" disabled>
                <span>点击加载规格</span>
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="价格(元)" align="center" width="100">
          <template slot-scope="scope">
            {{ formatPrice(scope.row.selectedSku ? scope.row.selectedSku.price : 0) }}
          </template>
        </el-table-column>
        <el-table-column label="库存" align="center" width="80">
          <template slot-scope="scope">
            {{ scope.row.selectedSku ? scope.row.selectedSku.stock : 0 }}
          </template>
        </el-table-column>
        <el-table-column label="数量" align="center" width="120">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.quantity"
              :min="1"
              :max="9999"
              size="small"
              :disabled="!scope.row.selectedSkuId"
              controls-position="right"
              style="width: 100%"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="selectedProducts.length === 0">
          确认选择 ({{ selectedProducts?.length }})
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCategory } from "@/api/product/category";
import { listProductInfo } from "@/api/product/info";
import { listProductSku } from "@/api/product/sku";
import Pagination from "@/components/Pagination";
import { formatPrice, formatSpecData } from './utils';

export default {
  name: "ProductSelector",
  components: {
    Pagination
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      selectedProducts: [],
      // 总条数
      total: 0,
      // 商品列表
      productList: [],
      // 分类选项
      categoryOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        categoryId: undefined
      }
    };
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          console.log('商品选择器visible变为true', new Date().toISOString());
          // 延迟加载数据，确保对话框已经完全显示
          setTimeout(() => {
            this.getList();
            this.getCategoryOptions();
          }, 100);
        } else {
          console.log('商品选择器visible变为false', new Date().toISOString());
        }
      }
    }
  },
  methods: {
    formatSpecData,
    formatPrice,

    // 获取商品列表
    async getList() {
      this.loading = true;
      try {
        const response = await listProductInfo({
          ...this.queryParams,
          productName: this.queryParams.keyword,
          productCode: this.queryParams.keyword
        });

        this.productList = (response.rows || []).map(product => ({
          ...product,
          quantity: 1, // 默认数量
          skuList: [], // 初始化为空数组
          skuLoaded: false // 标记SKU是否已加载
        }));
        this.total = response.total;
      } catch (error) {
        console.error("获取商品列表失败", error);
        this.$message.error("获取商品列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 加载商品SKU列表
    async loadProductSkus(product) {
      if (product.skuLoaded) {
        return; // 如果已经加载过，直接返回
      }

      product.skuLoading = true;
      try {
        const response = await listProductSku({
          productId: product.productId
        });

        product.skuList = (response.rows || []).map(sku => ({
          ...sku,
          price: sku.price ? sku.price / 100 : 0
        }));

        product.skuLoaded = true;

        // 如果只有一个SKU，自动选择并选中商品
        if (product.skuList.length === 1) {
          product.selectedSkuId = product.skuList[0].skuId;
          product.selectedSku = product.skuList[0];

          // 自动选中该商品
          this.$nextTick(() => {
            if (this.$refs.table) {
              this.$refs.table.toggleRowSelection(product, true);
            }
          });
        }
      } catch (error) {
        console.error("获取SKU列表失败", error);
        this.$message.error("获取规格列表失败");
      } finally {
        product.skuLoading = false;
      }
    },

    // 处理规格选择框获得焦点
    async handleSkuFocus(product) {
      if (!product.skuLoaded && product.skuList.length === 0) {
        await this.loadProductSkus(product);
      }
    },

    // 获取商品分类选项
    async getCategoryOptions() {
      try {
        const response = await listCategory();
        // 确保返回的数据格式正确
        if (Array.isArray(response)) {
          this.categoryOptions = response;
        } else if (response && Array.isArray(response.data)) {
          this.categoryOptions = response.data;
        } else if (response && Array.isArray(response.rows)) {
          this.categoryOptions = response.rows;
        } else {
          this.categoryOptions = [];
          console.warn("获取商品分类返回格式异常:", response);
        }
      } catch (error) {
        console.error("获取商品分类失败", error);
        this.$message.error("获取商品分类失败");
      }
    },

    // 处理SKU选择
    handleSkuSelect(product, skuId) {
      product.selectedSkuId = skuId;
      product.selectedSku = product.skuList.find(sku => sku.skuId === skuId);

      // 当选择了规格时，自动选中该商品
      if (skuId && this.$refs.table) {
        // 查找当前商品在表格中的位置
        const productIndex = this.productList.findIndex(p => p.productId === product.productId);
        if (productIndex !== -1) {
          // 使用nextTick确保DOM已更新
          this.$nextTick(() => {
            // 自动选中该行
            this.$refs.table.toggleRowSelection(product, true);
          });
        }
      }
    },

    // 处理表格选择变更
    handleSelectionChange(selection) {
      this.selectedProducts = selection;
    },

    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 重置按钮操作
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        categoryId: undefined
      };
      this.handleQuery();
    },

    // 关闭对话框
    handleClose() {
      // 重置所有状态
      this.resetQuery();
      this.selectedProducts = [];
      this.productList = [];
      this.total = 0;

      // 延迟发送关闭事件，确保状态已完全重置
      setTimeout(() => {
        this.$emit('update:visible', false);
        console.log('商品选择器对话框已关闭', new Date().toISOString());
      }, 100);
    },

    // 确认选择
    handleConfirm() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning("请至少选择一个商品");
        return;
      }

      // 检查是否所有选中的商品都选择了SKU
      const invalidProducts = this.selectedProducts.filter(item => !item.selectedSkuId);
      if (invalidProducts.length > 0) {
        // 自动加载未加载SKU的商品
        const loadPromises = invalidProducts.map(async (product) => {
          if (!product.skuLoaded) {
            await this.loadProductSkus(product);
          }
        });

        // 等待所有SKU加载完成
        Promise.all(loadPromises).then(() => {
          this.$message.warning(`有${invalidProducts.length}个商品未选择规格，请选择规格后再确认`);
        });
        return;
      }

      // 转换为订单明细格式
      const orderItems = this.selectedProducts.map(product => {
        const sku = product.selectedSku;
        return {
          productId: product.productId,
          productName: product.productName,
          productCode: product.productCode,
          skuId: sku.skuId,
          skuCode: sku.skuCode,
          specData: sku.specData,
          unit: product.unit || '个',
          quantity: product.quantity || 1,
          price: sku.price || 0,
          amount: Math.round((sku.price || 0) * (product.quantity || 1) * 100),
          remark: '',
          skuOptions: product.skuList
        };
      });

      // 先发送确认事件
      this.$emit('confirm', orderItems);

      // 然后关闭对话框
      this.handleClose();
    }
  }
};
</script>

<style lang="scss" scoped>
.product-selector {
  .search-area {
    margin-bottom: 15px;
  }

  ::v-deep .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 90%;
    max-width: 90%;

    .el-dialog__body {
      flex: 1;
      overflow: auto;
      padding: 10px 20px;
    }

    .el-table {
      width: 100%;

      .el-input-number {
        width: 100%;
      }

      .el-input-number.is-controls-right .el-input__inner {
        padding-left: 5px;
        padding-right: 30px;
      }

      .el-select {
        width: 100%;
      }
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
    }
  }

  ::v-deep .el-dialog__wrapper {
    overflow: hidden;
    z-index: 3000 !important;
  }

  ::v-deep .v-modal {
    z-index: 2999 !important;
  }

  /* 确保对话框显示在最上层 */
  &.el-dialog-visible {
    position: relative;
    z-index: 3001 !important;
  }
}
</style>
