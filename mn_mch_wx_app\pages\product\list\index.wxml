<view class="product-list-container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-box">
      <view class="search-input-wrapper">
        <view class="css-icon icon-search"></view>
        <input class="search-input" placeholder="搜索产品" value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearch" />
        <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="clearSearch">
          <view class="css-icon icon-clear"></view>
        </view>
      </view>
      <view class="search-btn" bindtap="onSearch">搜索</view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 左侧分类列表 -->
    <view class="category-sidebar">
      <scroll-view class="category-scroll" scroll-y="true">
        <view class="category-item {{selectedCategoryId === '' ? 'active' : ''}}" bindtap="selectCategory" data-id="">
          <text class="category-text">推荐</text>
        </view>
        <view wx:for="{{categoryList}}" wx:key="categoryId" class="category-item {{selectedCategoryId === item.categoryId ? 'active' : ''}}" bindtap="selectCategory" data-id="{{item.categoryId}}">
          <text class="category-text">{{item.categoryName}}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 右侧产品列表 -->
    <view class="product-content">
      <!-- 分类标题 -->
      <view class="category-title">
        <text class="title-text">{{searchKeyword ? '搜索结果' : currentCategoryName}}</text>
        <text wx:if="{{searchKeyword}}" class="search-keyword">关键词: {{searchKeyword}}</text>
      </view>

      <!-- 产品列表 -->
      <scroll-view class="product-scroll" scroll-y="true" bindscrolltolower="loadMore">
        <view wx:if="{{productList.length === 0 && !loading}}" class="empty-state">
          <view class="empty-icon">
            <view class="css-icon icon-empty-product"></view>
          </view>
          <text class="empty-text">暂无产品</text>
          <text class="empty-desc">该分类下暂时没有产品</text>
        </view>

        <view wx:else class="product-list">
          <view wx:for="{{productList}}" wx:key="productId" class="product-item" bindtap="navigateToDetail" data-id="{{item.productId}}">
            <view class="product-image-wrapper">
              <image wx:if="{{item.mainImage}}" class="product-image" src="{{item.mainImage}}" mode="aspectFill"></image>
              <view wx:else class="product-image-placeholder">
                <view class="css-icon icon-product-placeholder"></view>
              </view>
            </view>
            <view class="product-info">
              <text class="product-name">{{item.productName}}</text>
              <view class="product-price-wrapper">
                <view class="price-container">
                  <text class="product-price">¥{{item.price}}</text>
                  <text class="product-unit">起</text>
                  <view wx:if="{{item.hasSpecialPrice}}" class="price-tag {{item.priceType === 'TIER_PRICE' ? 'tier' : item.priceType === 'LEVEL_PRICE' ? 'level' : 'base'}}">
                    {{item.priceType === 'TIER_PRICE' ? '阶梯价' : item.priceType === 'LEVEL_PRICE' ? '等级价' : '基础价'}}
                  </view>
                </view>
                <text wx:if="{{item.originalPrice !== item.price}}" class="original-price">原价¥{{item.originalPrice}}</text>
              </view>
              <view class="product-meta">
                <text class="product-stock">库存{{item.stock}}</text>
                <text class="product-sales">销量{{item.sales}}</text>
              </view>
            </view>
          </view>

          <!-- 加载更多 -->
          <view wx:if="{{hasMore}}" class="load-more" bindtap="loadMore">
            <text wx:if="{{!loadingMore}}">加载更多</text>
            <text wx:else>加载中...</text>
          </view>

          <view wx:if="{{!hasMore && productList.length > 0}}" class="no-more">
            <text>没有更多产品了</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>



  <t-message id="t-message" />
</view>
