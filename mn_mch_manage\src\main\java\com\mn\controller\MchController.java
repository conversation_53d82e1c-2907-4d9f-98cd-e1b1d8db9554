package com.mn.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mn.entity.NyDept;
import com.mn.entity.ProductCategory;
import com.mn.entity.ProductSku;
import com.mn.entity.SalesOrder;
import com.mn.entity.SalesOrderItem;
import com.mn.form.WxPayOrderForm;
import com.mn.form.WxProductInfoForm;
import com.mn.model.CreateOrderModel;
import com.mn.model.OrderModel;
import com.mn.model.PriceCalculateRequest;
import com.mn.model.PriceCalculateResponse;
import com.mn.model.TokenModel;
import com.mn.service.INyDeptService;
import com.mn.service.IProductCategoryService;
import com.mn.service.IProductInfoService;
import com.mn.service.IProductSkuService;
import com.mn.service.ISalesOrderItemService;
import com.mn.service.ISalesOrderService;
import com.mn.service.IPriceCalculateService;
import com.mn.service.WqfService;
import com.mn.util.MeloonException;
import com.mn.util.MeloonReqUtil;
import com.mn.util.MisRspModel;

import lombok.var;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/mch")
@Slf4j
public class MchController {

    @Resource
    IProductInfoService productInfoService;

    @Resource
    INyDeptService iNyDeptService;

    @Resource
    IProductCategoryService productCategoryService;

    @Resource
    IProductSkuService productSkuService;

    @Resource
    ISalesOrderService salesOrderService;

    @Resource
    ISalesOrderItemService salesOrderItemService;

    @Resource
    WqfService wqfService;

    @Resource
    IPriceCalculateService priceCalculateService;

    // 订单号生成器
    private static final AtomicLong orderCounter = new AtomicLong(1);

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        long counter = orderCounter.getAndIncrement();
        return "WX" + timestamp + String.format("%04d", counter % 10000);
    }

    /**
     * 创建订单明细项
     */
    private void createOrderItem(Long orderId, Long skuId, Integer quantity) {
        try {
            log.info("【创建订单明细】SKU ID: {}, 数量: {}", skuId, quantity);

            // 获取SKU信息
            ProductSku productSku = productSkuService.getById(skuId);
            if (productSku == null) {
                throw new MeloonException("SKU不存在: " + skuId);
            }

            // 使用价格计算服务计算价格
            PriceCalculateRequest priceRequest = new PriceCalculateRequest();
            priceRequest.setPartnerId(6L); // 默认合作伙伴ID，实际应该从订单中获取
            priceRequest.setProductId(productSku.getProductId());
            priceRequest.setSkuId(productSku.getSkuId());
            priceRequest.setQuantity(quantity);
            priceRequest.setOrderDate(java.time.LocalDate.now());

            PriceCalculateResponse priceResponse = priceCalculateService.calculatePrice(priceRequest);
            Long unitPrice = priceResponse.getFinalPrice();
            Long totalAmount = unitPrice * quantity;

            log.info("【创建订单明细】价格计算结果: 单价={}, 数量={}, 总金额={}, 价格类型={}",
                    unitPrice, quantity, totalAmount, priceResponse.getPriceType());

            // 创建订单明细
            SalesOrderItem orderItem = new SalesOrderItem();
            orderItem.setOrderId(orderId);
            orderItem.setProductId(productSku.getProductId());
            orderItem.setSkuId(productSku.getSkuId());
            orderItem.setQuantity(new java.math.BigDecimal(quantity));
            orderItem.setSkuCode(productSku.getSkuCode());
            orderItem.setSpecData(productSku.getSpecData());
            orderItem.setPrice(unitPrice);
            orderItem.setAmount(totalAmount);
            orderItem.setCreateTime(new Date());

            // 获取商品基本信息
            try {
                var productInfo = productInfoService.getById(productSku.getProductId());
                if (productInfo != null) {
                    orderItem.setProductName(productInfo.getProductName());
                    orderItem.setProductCode(productInfo.getProductCode());
                } else {
                    orderItem.setProductName("商品");
                }
            } catch (Exception e) {
                log.warn("【创建订单明细】获取商品基本信息失败", e);
                orderItem.setProductName("商品");
            }

            // 插入订单明细
            salesOrderItemService.insertSalesOrderItem(orderItem);
            log.info("【创建订单明细】插入成功: SKU={}, 金额={}", skuId, orderItem.getAmount());

        } catch (Exception e) {
            log.error("【创建订单明细】创建失败: SKU={}", skuId, e);
            throw new MeloonException("创建订单明细失败: " + e.getMessage());
        }
    }

    /**
     * 订单列表（新版本 - 使用sales_order表）
     */
    @PostMapping("/listOrder")
    public MisRspModel listOrder(HttpServletRequest request) {
        try {
            WxPayOrderForm orderForm = (WxPayOrderForm) MeloonReqUtil.getData(request, WxPayOrderForm.class);
            if (null == orderForm.getOrderPageNum())
                throw new MeloonException(-1, "orderPageNum为空!");
            if (null == orderForm.getOrderPageSize())
                throw new MeloonException(-1, "orderPageSize为空!");

            TokenModel tokenModel = MeloonReqUtil.getTokenModel(request);
            String openId = tokenModel.getOpenId();

            log.info("【订单列表】查询订单列表，openId: {}, 页码: {}, 页大小: {}",
                    openId, orderForm.getOrderPageNum(), orderForm.getOrderPageSize());

            // 获取商户信息
            NyDept dept = iNyDeptService.selectDeptByUid("f759d4e8d63b4e059e86f00dcd8f6738");
            if (dept == null) {
                throw new MeloonException("商户信息不存在");
            }

            // 查询销售订单列表
            List<SalesOrder> orderList = salesOrderService.selectOrdersByDeptId(
                    dept.getDeptId(),
                    orderForm.getOrderPageNum(),
                    orderForm.getOrderPageSize());

            // 转换为前端需要的格式
            List<JSONObject> resultList = new ArrayList<>();
            for (SalesOrder order : orderList) {
                JSONObject orderObj = new JSONObject();

                // 基本订单信息
                orderObj.put("orderId", order.getOrderId());
                orderObj.put("orderNo", order.getOrderNo());
                orderObj.put("outPaymentId", order.getOrderNo()); // 前端使用outPaymentId作为订单标识
                orderObj.put("orderDate", order.getOrderDate());
                orderObj.put("createTime", order.getOrderDate().getTime()); // 前端期望的时间戳格式
                orderObj.put("totalAmount", order.getTotalAmount());
                orderObj.put("actualAmount", order.getActualAmount());
                orderObj.put("amount", order.getActualAmount()); // 前端期望的金额字段
                orderObj.put("paymentAmount", order.getPaymentAmount());
                orderObj.put("paymentStatus", order.getPaymentStatus());
                orderObj.put("deliveryStatus", order.getDeliveryStatus());
                orderObj.put("orderStatus", order.getOrderStatus());
                orderObj.put("remark", order.getRemark());

                // 转换支付状态为前端可理解的状态
                String status;
                switch (order.getPaymentStatus()) {
                    case 0:
                        status = "PROCESSING"; // 前端期望的待付款状态
                        break;
                    case 1:
                        status = "SUCCEEDED"; // 前端期望的已付款状态
                        break;
                    case 2:
                        status = "PROCESSING"; // 付款中也显示为待付款
                        break;
                    default:
                        status = "CLOSED";
                }
                orderObj.put("status", status);

                // 获取订单明细并转换为前端期望的商品信息格式
                try {
                    List<SalesOrderItem> orderItems = salesOrderItemService.selectByOrderId(order.getOrderId());
                    List<JSONObject> goodsList = new ArrayList<>();

                    for (SalesOrderItem item : orderItems) {
                        JSONObject goodObj = new JSONObject();
                        goodObj.put("good_name", item.getProductName());
                        goodObj.put("good_amount", item.getPrice()); // 单价（分）
                        goodObj.put("good_number", item.getQuantity().intValue());
                        goodObj.put("good_img", ""); // 暂时为空，后续可以从商品表获取
                        goodsList.add(goodObj);
                    }

                    orderObj.put("goodsInfo", JSON.toJSONString(goodsList)); // 前端期望的商品信息JSON字符串
                    orderObj.put("goodsList", goodsList); // 同时提供解析后的商品列表

                } catch (Exception e) {
                    log.warn("【订单列表】获取订单明细失败，订单ID: {}", order.getOrderId(), e);
                    orderObj.put("goodsInfo", "[]");
                    orderObj.put("goodsList", new ArrayList<>());
                }

                resultList.add(orderObj);
            }

            log.info("【订单列表】查询完成，返回{}条订单", resultList.size());
            return new MisRspModel(resultList);

        } catch (Exception e) {
            log.error("【订单列表】查询订单列表失败", e);
            throw new MeloonException("查询订单列表失败: " + e.getMessage());
        }
    }

    // @GetMapping("/listProductInfo")
    // public MisRspModel listProductInfo(HttpServletRequest request) {
    // return new MisRspModel(productInfoService.wxSelectProductInfo(new
    // WxProductInfoForm()));
    // }

    // @GetMapping("/getProductInfo")
    // public MisRspModel getProductInfo(Long productId) {
    // return new MisRspModel(productInfoService.getById(productId));
    // }

    /**
     * 创建订单（新版本 - 使用sales_order表）
     */
    @PostMapping("/createOrder")
    public MisRspModel createOrder(HttpServletRequest request) {
        TokenModel tokenModel = MeloonReqUtil.getTokenModel(request);
        CreateOrderModel orderModel = (CreateOrderModel) MeloonReqUtil.getData(request, CreateOrderModel.class);
        orderModel.setOpenId(tokenModel.getOpenId());

        // 添加详细日志
        log.info("【创建订单】开始处理订单创建请求");
        log.info("【创建订单】订单数据: {}", JSON.toJSONString(orderModel));

        try {
            // 0. 验证必要参数和确定订单类型
            boolean isMultiSku = orderModel.getSkus() != null && !orderModel.getSkus().isEmpty();
            boolean isSingleSku = orderModel.getSkuId() != null || orderModel.getProductId() != null;

            if (!isMultiSku && !isSingleSku) {
                throw new MeloonException("请提供商品SKU信息");
            }

            log.info("【创建订单】订单类型: {}", isMultiSku ? "多SKU购物车订单" : "单SKU订单");

            // 计算订单总金额（分为单位）
            Long orderAmount = 0L;

            if (isMultiSku) {
                // 多SKU订单：使用前端传递的总金额
                if (orderModel.getTotalAmount() != null && !orderModel.getTotalAmount().trim().isEmpty()) {
                    try {
                        // 将元转换为分
                        java.math.BigDecimal amountInYuan = new java.math.BigDecimal(orderModel.getTotalAmount());
                        orderAmount = amountInYuan.multiply(new java.math.BigDecimal("100")).longValue();
                        log.info("【创建订单】多SKU订单，使用前端总金额: {}元 = {}分", orderModel.getTotalAmount(), orderAmount);
                    } catch (NumberFormatException e) {
                        throw new MeloonException("订单金额格式错误: " + orderModel.getTotalAmount());
                    }
                } else {
                    throw new MeloonException("多SKU订单必须提供总金额");
                }
            } else {
                // 单SKU订单：兼容旧版本逻辑
                ProductSku productSku = null;
                if (orderModel.getSkuId() != null) {
                    log.info("【创建订单】使用SKU ID获取商品信息: {}", orderModel.getSkuId());
                    productSku = productSkuService.getById(orderModel.getSkuId());
                } else if (orderModel.getProductId() != null) {
                    log.info("【创建订单】使用商品ID获取默认SKU信息: {}", orderModel.getProductId());
                    List<ProductSku> skuList = productSkuService.selectSkuByProductId(orderModel.getProductId());
                    if (!skuList.isEmpty()) {
                        productSku = skuList.get(0);
                        log.info("【创建订单】获取到默认SKU: {}", productSku.getSkuId());
                    }
                }

                if (productSku == null) {
                    throw new MeloonException("无法获取商品SKU信息");
                }

                // 设置默认数量
                if (orderModel.getQuantity() == null || orderModel.getQuantity() <= 0) {
                    orderModel.setQuantity(1L);
                    log.info("【创建订单】设置默认数量为1");
                }

                // 计算金额
                orderAmount = orderModel.getAmount();
                if (orderAmount == null || orderAmount <= 0) {
                    // 使用价格计算服务计算价格
                    PriceCalculateRequest priceRequest = new PriceCalculateRequest();
                    priceRequest.setPartnerId(6L); // 默认合作伙伴ID，实际应该从订单中获取
                    priceRequest.setProductId(productSku.getProductId());
                    priceRequest.setSkuId(productSku.getSkuId());
                    priceRequest.setQuantity(orderModel.getQuantity().intValue());
                    priceRequest.setOrderDate(java.time.LocalDate.now());

                    PriceCalculateResponse priceResponse = priceCalculateService.calculatePrice(priceRequest);
                    Long unitPrice = priceResponse.getFinalPrice();
                    orderAmount = unitPrice * orderModel.getQuantity();

                    log.info("【创建订单】计算单SKU订单金额: 单价={}, 数量={}, 总金额={}, 价格类型={}",
                            unitPrice, orderModel.getQuantity(), orderAmount, priceResponse.getPriceType());
                }
            }

            // 1. 获取商户信息
            NyDept dept = iNyDeptService.selectDeptByUid("f759d4e8d63b4e059e86f00dcd8f6738");
            if (dept == null) {
                throw new MeloonException("商户信息不存在");
            }
            log.info("【创建订单】获取商户信息成功: deptId={}, merchantName={}", dept.getDeptId(), dept.getMerchantName());

            // 2. 创建销售订单
            SalesOrder salesOrder = new SalesOrder();
            String orderNo = generateOrderNo();
            salesOrder.setOrderNo(orderNo); // 生成订单号
            salesOrder.setDeptId(dept.getDeptId());
            salesOrder.setOrderDate(new Date());
            salesOrder.setTotalAmount(orderAmount);
            salesOrder.setActualAmount(orderAmount);
            salesOrder.setPaymentStatus(0); // 未付款
            salesOrder.setDeliveryStatus(0); // 未发货
            salesOrder.setOrderStatus(0); // 未审核
            salesOrder.setPaymentAmount(0L); // 已付金额为0
            salesOrder.setCreateBy("wx_miniprogram");
            salesOrder.setPartnerId(6L);
            salesOrder.setCreateTime(new Date());
            salesOrder.setRemark("微信小程序下单");

            // 详细日志记录订单信息
            log.info("【创建订单】准备插入订单数据:");
            log.info("  - 订单号: {}", orderNo);
            log.info("  - 商户ID: {}", dept.getDeptId());
            log.info("  - 计算后订单金额: {}", orderAmount);
            log.info("  - 总金额: {}", salesOrder.getTotalAmount());
            log.info("  - 实际金额: {}", salesOrder.getActualAmount());
            log.info("【创建订单】完整订单对象: {}", JSON.toJSONString(salesOrder));

            // 3. 插入销售订单
            log.info("【创建订单】开始插入销售订单到数据库");
            int result = salesOrderService.insertSalesOrder(salesOrder);
            log.info("【创建订单】插入销售订单结果: result={}", result);

            if (result <= 0) {
                log.error("【创建订单】插入销售订单失败: result={}", result);
                throw new MeloonException("创建订单失败，插入数据库失败");
            }

            // 插入后获取生成的订单ID
            log.info("【创建订单】插入成功，订单ID: {}", salesOrder.getOrderId());

            // 验证插入的数据
            SalesOrder insertedOrder = salesOrderService.getByOrderNo(salesOrder.getOrderNo());
            if (insertedOrder != null) {
                log.info("【创建订单】验证插入数据成功:");
                log.info("  - 订单ID: {}", insertedOrder.getOrderId());
                log.info("  - 总金额: {}", insertedOrder.getTotalAmount());
                log.info("  - 实际金额: {}", insertedOrder.getActualAmount());
            } else {
                log.error("【创建订单】验证插入数据失败，无法查询到刚插入的订单");
            }

            // 4. 创建订单明细
            log.info("【创建订单】开始创建订单明细");

            if (isMultiSku) {
                // 多SKU订单：遍历SKU列表创建明细
                for (com.mn.model.OrderSkuItem skuItem : orderModel.getSkus()) {
                    createOrderItem(salesOrder.getOrderId(), skuItem.getSkuId(), skuItem.getQuantity());
                }
                log.info("【创建订单】多SKU订单明细创建完成，共{}个SKU", orderModel.getSkus().size());
            } else {
                // 单SKU订单：兼容旧版本
                Long skuId = orderModel.getSkuId();
                if (skuId == null && orderModel.getProductId() != null) {
                    // 如果只有productId，获取第一个SKU
                    List<ProductSku> skuList = productSkuService.selectSkuByProductId(orderModel.getProductId());
                    if (!skuList.isEmpty()) {
                        skuId = skuList.get(0).getSkuId();
                    }
                }

                if (skuId != null) {
                    createOrderItem(salesOrder.getOrderId(), skuId, orderModel.getQuantity().intValue());
                    log.info("【创建订单】单SKU订单明细创建完成");
                } else {
                    throw new MeloonException("无法确定SKU信息");
                }
            }

            // 5. 返回订单信息
            JSONObject result_obj = new JSONObject();
            result_obj.put("orderId", salesOrder.getOrderId());
            result_obj.put("orderNo", salesOrder.getOrderNo());
            result_obj.put("amount", salesOrder.getActualAmount());
            result_obj.put("status", "SUCCESS");

            return new MisRspModel(result_obj);

        } catch (Exception e) {
            log.error("【创建订单】创建订单异常", e);
            log.error("【创建订单】异常详情: 类型={}, 消息={}", e.getClass().getSimpleName(), e.getMessage());
            if (e.getCause() != null) {
                log.error("【创建订单】异常原因: {}", e.getCause().getMessage());
            }
            throw new MeloonException("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 订单详情（新版本 - 使用sales_order表）
     */
    @GetMapping("/getOrderDetail")
    public MisRspModel getOrderDetail(String orderId, String orderNo) {
        try {
            // 兼容前端传递的参数：orderId 或 orderNo
            String queryOrderNo = null;
            if (orderNo != null && !orderNo.trim().isEmpty()) {
                queryOrderNo = orderNo;
            } else if (orderId != null && !orderId.trim().isEmpty()) {
                queryOrderNo = orderId; // 前端传递的orderId实际上是orderNo
            }

            if (queryOrderNo == null || queryOrderNo.trim().isEmpty()) {
                throw new MeloonException("订单号不能为空");
            }

            log.info("【订单详情】查询订单详情，订单号: {}", queryOrderNo);

            // 1. 获取销售订单
            SalesOrder salesOrder = salesOrderService.getByOrderNo(queryOrderNo);
            if (salesOrder == null) {
                throw new MeloonException("订单不存在");
            }

            // 2. 获取订单明细
            List<SalesOrderItem> orderItems = salesOrderItemService.selectByOrderId(salesOrder.getOrderId());

            // 3. 构建返回数据
            JSONObject orderDetail = new JSONObject();

            // 订单基本信息（兼容前端期望的字段）
            orderDetail.put("orderId", salesOrder.getOrderId());
            orderDetail.put("orderNo", salesOrder.getOrderNo());
            orderDetail.put("outPaymentId", salesOrder.getOrderNo()); // 前端期望的订单标识
            orderDetail.put("orderDate", salesOrder.getOrderDate());
            orderDetail.put("createTime", salesOrder.getOrderDate().getTime()); // 前端期望的时间戳
            orderDetail.put("totalAmount", salesOrder.getTotalAmount());
            orderDetail.put("discountAmount", salesOrder.getDiscountAmount());
            orderDetail.put("actualAmount", salesOrder.getActualAmount());
            orderDetail.put("amount", salesOrder.getActualAmount()); // 前端期望的金额字段
            orderDetail.put("paymentAmount", salesOrder.getPaymentAmount());
            orderDetail.put("paymentStatus", salesOrder.getPaymentStatus());
            orderDetail.put("deliveryStatus", salesOrder.getDeliveryStatus());
            orderDetail.put("orderStatus", salesOrder.getOrderStatus());
            orderDetail.put("remark", salesOrder.getRemark());

            // 转换支付状态为前端期望的格式
            String status;
            switch (salesOrder.getPaymentStatus()) {
                case 0:
                    status = "PROCESSING"; // 前端期望的待付款状态
                    break;
                case 1:
                    status = "SUCCEEDED"; // 前端期望的已付款状态
                    break;
                case 2:
                    status = "PROCESSING"; // 付款中也显示为待付款
                    break;
                default:
                    status = "CLOSED";
            }
            orderDetail.put("status", status);

            // 订单明细信息（转换为前端期望的商品格式）
            List<JSONObject> itemList = new ArrayList<>();
            List<JSONObject> goodsList = new ArrayList<>();

            for (SalesOrderItem item : orderItems) {
                // 详细的明细信息
                JSONObject itemObj = new JSONObject();
                itemObj.put("itemId", item.getItemId());
                itemObj.put("productId", item.getProductId());
                itemObj.put("skuId", item.getSkuId());
                itemObj.put("productName", item.getProductName());
                itemObj.put("productCode", item.getProductCode());
                itemObj.put("skuCode", item.getSkuCode());
                itemObj.put("specData", item.getSpecData());
                itemObj.put("quantity", item.getQuantity());
                itemObj.put("price", item.getPrice());
                itemObj.put("amount", item.getAmount());
                itemList.add(itemObj);

                // 前端期望的商品格式
                JSONObject goodObj = new JSONObject();
                goodObj.put("good_name", item.getProductName());
                goodObj.put("good_amount", item.getPrice()); // 单价（分）
                goodObj.put("good_number", item.getQuantity().intValue());
                goodObj.put("good_img", ""); // 暂时为空
                goodsList.add(goodObj);
            }

            orderDetail.put("items", itemList); // 详细明细信息
            orderDetail.put("goodsList", goodsList); // 前端期望的商品列表
            orderDetail.put("goodsInfo", JSON.toJSONString(goodsList)); // 前端期望的商品信息JSON字符串

            // 计算商品总数量
            int goodsCount = goodsList.stream().mapToInt(good -> (Integer) good.get("good_number")).sum();
            orderDetail.put("goodsCount", goodsCount);

            log.info("【订单详情】查询完成，订单包含{}个商品", itemList.size());
            return new MisRspModel(orderDetail);

        } catch (Exception e) {
            log.error("【订单详情】查询订单详情失败", e);
            throw new MeloonException("查询订单详情失败: " + e.getMessage());
        }
    }

    /**
     * 首页统计数据
     */
    @GetMapping("/getDashboardStats")
    public MisRspModel getDashboardStats(HttpServletRequest request) {
        try {
            TokenModel tokenModel = MeloonReqUtil.getTokenModel(request);
            log.info("【首页统计】获取首页统计数据，openId: {}", tokenModel.getOpenId());

            // 获取商户信息
            NyDept dept = iNyDeptService.selectDeptByUid("f759d4e8d63b4e059e86f00dcd8f6738");
            if (dept == null) {
                throw new MeloonException("商户信息不存在");
            }

            // 获取统计数据
            JSONObject statsData = new JSONObject();

            // 1. 今日订单数量
            int todayOrders = salesOrderService.getTodayOrderCount(dept.getDeptId());
            statsData.put("todayOrders", todayOrders);

            // 2. 本月采购额（万元）
            double monthAmount = salesOrderService.getMonthPurchaseAmount(dept.getDeptId());
            statsData.put("monthAmount", String.format("%.1f", monthAmount / 10000.0)); // 转换为万元

            // 3. 本月订单数量
            int monthOrders = salesOrderService.getMonthOrderCount(dept.getDeptId());
            statsData.put("monthOrders", monthOrders);

            // 4. 待付款订单数量
            int unpaidOrders = salesOrderService.getUnpaidOrderCount(dept.getDeptId());
            statsData.put("unpaidOrders", unpaidOrders);

            log.info("【首页统计】统计数据获取成功 - 今日订单: {}, 本月采购额: {}万, 本月订单: {}, 待付款: {}",
                    todayOrders, String.format("%.1f", monthAmount / 10000.0), monthOrders, unpaidOrders);

            return new MisRspModel(statsData);

        } catch (Exception e) {
            log.error("【首页统计】获取首页统计数据失败", e);
            throw new MeloonException("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 支付订单（新版本 - 使用sales_order_pay表）
     */
    @GetMapping("/payOrder")
    public MisRspModel payOrder(String orderId, String orderNo) {
        try {
            // 兼容前端传递的参数：orderId 或 orderNo
            String queryOrderNo = null;
            if (orderNo != null && !orderNo.trim().isEmpty()) {
                queryOrderNo = orderNo;
            } else if (orderId != null && !orderId.trim().isEmpty()) {
                queryOrderNo = orderId; // 前端传递的orderId实际上是orderNo
            }

            if (queryOrderNo == null || queryOrderNo.trim().isEmpty()) {
                throw new MeloonException("订单号不能为空");
            }

            log.info("【支付订单】开始处理支付请求，订单号: {}", queryOrderNo);

            // 1. 获取销售订单
            SalesOrder salesOrder = salesOrderService.getByOrderNo(queryOrderNo);
            if (salesOrder == null) {
                throw new MeloonException("订单不存在");
            }

            // 2. 检查订单状态
            if (salesOrder.getPaymentStatus() == 1) {
                throw new MeloonException("订单已支付");
            }

            // 3. 微信小程序订单自动审核
            if (salesOrder.getOrderStatus() == 0) {
                log.info("【支付订单】微信小程序订单自动审核，订单号: {}", queryOrderNo);
                try {
                    salesOrderService.checkSalesOrder(salesOrder.getOrderId());
                    log.info("【支付订单】订单自动审核成功");
                } catch (Exception e) {
                    log.error("【支付订单】订单自动审核失败", e);
                    throw new MeloonException("订单审核失败: " + e.getMessage());
                }
            }

            log.info("【支付订单】订单状态检查通过，开始创建支付，订单金额: {}分", salesOrder.getActualAmount());

            // 4. 调用微企付支付（固定使用微企付渠道）
            String qrCodeUrl = salesOrderService.qrCodePay(queryOrderNo, 1); // 1表示微企付

            // 5. 返回支付信息（兼容前端期望的格式）
            JSONObject resultObj = new JSONObject();
            resultObj.put("qrCodeUrl", qrCodeUrl);
            resultObj.put("orderNo", salesOrder.getOrderNo());
            resultObj.put("outPaymentId", salesOrder.getOrderNo()); // 前端期望的订单标识
            resultObj.put("amount", salesOrder.getActualAmount());
            resultObj.put("status", "SUCCESS");

            log.info("【支付订单】支付创建成功，二维码链接: {}", qrCodeUrl);
            return new MisRspModel(resultObj);

        } catch (Exception e) {
            throw new MeloonException("支付失败: " + e.getMessage());
        }
    }

    /**
     * 查询订单状态（新版本 - 使用sales_order表）
     */
    @GetMapping("/queryOrder")
    public MisRspModel queryOrder(String orderId, String orderNo) {
        try {
            // 兼容前端传递的参数：orderId 或 orderNo
            String queryOrderNo = null;
            if (orderNo != null && !orderNo.trim().isEmpty()) {
                queryOrderNo = orderNo;
            } else if (orderId != null && !orderId.trim().isEmpty()) {
                queryOrderNo = orderId; // 前端传递的orderId实际上是orderNo
            }

            if (queryOrderNo == null || queryOrderNo.trim().isEmpty()) {
                throw new MeloonException("订单号不能为空");
            }

            log.info("【查询订单状态】查询订单状态，订单号: {}", queryOrderNo);

            // 1. 获取销售订单
            SalesOrder salesOrder = salesOrderService.getByOrderNo(queryOrderNo);
            if (salesOrder == null) {
                throw new MeloonException("订单不存在");
            }

            // 2. 返回订单状态（兼容前端期望的格式）
            JSONObject obj = new JSONObject();
            obj.put("orderNo", salesOrder.getOrderNo());
            obj.put("orderId", salesOrder.getOrderId());
            obj.put("outPaymentId", salesOrder.getOrderNo()); // 前端期望的订单标识
            obj.put("amount", salesOrder.getActualAmount());
            obj.put("paymentStatus", salesOrder.getPaymentStatus()); // 0-未付款，1-已付款，2-付款中
            obj.put("paymentAmount", salesOrder.getPaymentAmount());
            obj.put("orderStatus", salesOrder.getOrderStatus()); // 0-未审核，1-已审核
            obj.put("deliveryStatus", salesOrder.getDeliveryStatus()); // 0-未发货，1-已发货

            // 转换支付状态为前端可理解的状态
            String status;
            switch (salesOrder.getPaymentStatus()) {
                case 0:
                    status = "PROCESSING"; // 前端期望的待付款状态
                    break;
                case 1:
                    status = "SUCCEEDED"; // 前端期望的已付款状态
                    break;
                case 2:
                    status = "PROCESSING"; // 付款中也显示为待付款
                    break;
                default:
                    status = "CLOSED";
            }
            obj.put("status", status);

            log.info("【查询订单状态】查询完成，订单状态: {}, 支付状态: {}", status, salesOrder.getPaymentStatus());

            return new MisRspModel(obj);

        } catch (Exception e) {
            throw new MeloonException("查询订单失败: " + e.getMessage());
        }
    }

//
//    @PostMapping("/payByUser")
//    public MisRspModel payByUser(HttpServletRequest request) {
//        CreateOrderModel orderModel = (CreateOrderModel) MeloonReqUtil.getData(request, CreateOrderModel.class);
//        if (null == orderModel.getUid())
//            throw new MeloonException("uid为空!");
//        if (null == orderModel.getAmount())
//            throw new MeloonException("付款金额为空!");
//        TokenModel tokenModel = MeloonReqUtil.getTokenModel(request);
//        orderModel.setOpenId(tokenModel.getOpenId());
//        log.info("扫码支付:{}", JSON.toJSONString(orderModel));
//        return new MisRspModel(orderService.createQrCodeOrder(orderModel));
//    }

    // ==================== 产品目录相关接口 ====================

    /**
     * 获取产品分类列表
     * 
     * @return 产品分类列表
     */
    @GetMapping("/getProductCategories")
    public MisRspModel getProductCategories() {
        try {
            List<ProductCategory> categories = productCategoryService.list(null);
            return new MisRspModel(categories);
        } catch (Exception e) {
            log.error("获取产品分类列表失败", e);
            throw new MeloonException("获取产品分类列表失败");
        }
    }

    /**
     * 根据分类ID获取产品列表
     * 
     * @param categoryId 分类ID，为空时获取所有产品
     * @param pageNum    页码
     * @param pageSize   每页大小
     * @return 产品列表
     */
    @GetMapping("/getProductsByCategory")
    public MisRspModel getProductsByCategory(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            WxProductInfoForm form = new WxProductInfoForm();
            form.setCategoryId(categoryId);
            form.setKeyword(keyword);
            // 不设置分页参数，让SQL查询返回所有结果，然后手动分页
            return new MisRspModel(productInfoService.wxSelectProductInfo(form));
        } catch (Exception e) {
            log.error("获取产品列表失败", e);
            throw new MeloonException("获取产品列表失败");
        }
    }

    /**
     * 获取产品详情
     * 
     * @param productId 产品ID
     * @return 产品详情
     */
    @GetMapping("/getProductDetail")
    public MisRspModel getProductDetail(@RequestParam Long productId) {
        try {
            if (productId == null) {
                throw new MeloonException("产品ID不能为空");
            }

            // 获取产品基本信息
            Object productInfo = productInfoService.getById(productId);
            if (productInfo == null) {
                throw new MeloonException("产品不存在");
            }

            // 获取产品规格信息
            List<ProductSku> skuList = productSkuService.selectSkuByProductId(productId);

            JSONObject result = new JSONObject();
            result.put("productInfo", productInfo);
            result.put("skuList", skuList);

            return new MisRspModel(result);
        } catch (Exception e) {
            log.error("获取产品详情失败", e);
            throw new MeloonException("获取产品详情失败");
        }
    }

    /**
     * 搜索产品
     * 
     * @param keyword    搜索关键词
     * @param categoryId 分类ID
     * @param pageNum    页码
     * @param pageSize   每页大小
     * @return 搜索结果
     */
    @GetMapping("/searchProducts")
    public MisRspModel searchProducts(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            WxProductInfoForm form = new WxProductInfoForm();
            form.setKeyword(keyword);
            form.setCategoryId(categoryId);
            // 先设置pageSize，再设置pageNum，确保offset计算正确
            form.setPageSize(pageSize);
            form.setPageNum(pageNum);
            return new MisRspModel(productInfoService.wxSelectProductInfo(form));
        } catch (Exception e) {
            log.error("搜索产品失败", e);
            throw new MeloonException("搜索产品失败");
        }
    }

}
