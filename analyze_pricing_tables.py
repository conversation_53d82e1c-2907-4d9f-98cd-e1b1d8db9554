#!/usr/bin/env python3
"""
分析商品定价相关表结构和关系
"""

import mysql.connector
from mysql.connector import Error
import json

def get_db_connection():
    """获取数据库连接"""
    config = {
        "host": "localhost",
        "port": 13306,
        "user": "meloon",
        "password": "meloon2019",
        "database": "mn_pay",
        "charset": "utf8mb4",
        "autocommit": True,
        "ssl_disabled": True,
        "use_unicode": True,
        "connect_timeout": 10
    }
    return mysql.connector.connect(**config)

def describe_table(cursor, table_name):
    """获取表结构"""
    cursor.execute(f"DESCRIBE {table_name}")
    columns = cursor.fetchall()
    
    print(f"\n=== {table_name} 表结构 ===")
    print("字段名 | 类型 | 是否为空 | 键 | 默认值 | 额外")
    print("-" * 80)
    for col in columns:
        print(f"{col[0]} | {col[1]} | {col[2]} | {col[3]} | {col[4]} | {col[5]}")
    
    return columns

def get_sample_data(cursor, table_name, limit=5):
    """获取表的示例数据"""
    cursor.execute(f"SELECT * FROM {table_name} LIMIT {limit}")
    rows = cursor.fetchall()
    
    # 获取列名
    cursor.execute(f"DESCRIBE {table_name}")
    columns = [col[0] for col in cursor.fetchall()]
    
    print(f"\n=== {table_name} 示例数据 (前{limit}条) ===")
    if rows:
        # 打印列名
        print(" | ".join(columns))
        print("-" * (len(" | ".join(columns))))
        
        # 打印数据
        for row in rows:
            print(" | ".join([str(val) if val is not None else "NULL" for val in row]))
    else:
        print("表中暂无数据")
    
    return rows, columns

def analyze_relationships():
    """分析表之间的关系"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 要分析的表
        tables = [
            'product_info',
            'product_sku', 
            'customer_level_price',
            'quantity_tier_price'
        ]
        
        print("商品定价系统表结构分析")
        print("=" * 50)
        
        # 分析每个表的结构和示例数据
        for table in tables:
            try:
                describe_table(cursor, table)
                get_sample_data(cursor, table)
                print("\n" + "=" * 50)
            except Error as e:
                print(f"分析表 {table} 时出错: {e}")
                continue
        
        # 分析关系
        print("\n=== 表关系分析 ===")
        
        # 检查product_info和product_sku的关系
        cursor.execute("""
            SELECT p.product_id, p.product_name, COUNT(s.sku_id) as sku_count
            FROM product_info p 
            LEFT JOIN product_sku s ON p.product_id = s.product_id 
            GROUP BY p.product_id, p.product_name
            LIMIT 5
        """)
        product_sku_relation = cursor.fetchall()
        
        print("\nproduct_info 与 product_sku 关系:")
        print("商品ID | 商品名称 | SKU数量")
        print("-" * 40)
        for row in product_sku_relation:
            print(f"{row[0]} | {row[1]} | {row[2]}")
        
        # 检查customer_level_price表的数据分布
        cursor.execute("""
            SELECT sku_id, level_id, COUNT(*) as price_count
            FROM customer_level_price
            GROUP BY sku_id, level_id
            LIMIT 10
        """)
        level_price_relation = cursor.fetchall()

        print("\ncustomer_level_price 数据分布:")
        print("SKU_ID | 客户等级ID | 价格记录数")
        print("-" * 40)
        for row in level_price_relation:
            print(f"{row[0]} | {row[1]} | {row[2]}")

        # 检查quantity_tier_price表的数据分布
        cursor.execute("""
            SELECT sku_id, level_id, min_quantity, COUNT(*) as tier_count
            FROM quantity_tier_price
            GROUP BY sku_id, level_id, min_quantity
            LIMIT 10
        """)
        tier_price_relation = cursor.fetchall()

        print("\nquantity_tier_price 数据分布:")
        print("SKU_ID | 客户等级ID | 最小数量 | 阶梯记录数")
        print("-" * 50)
        for row in tier_price_relation:
            print(f"{row[0]} | {row[1]} | {row[2]} | {row[3]}")
        
        cursor.close()
        conn.close()
        
    except Error as e:
        print(f"数据库连接失败: {e}")

if __name__ == "__main__":
    analyze_relationships()
