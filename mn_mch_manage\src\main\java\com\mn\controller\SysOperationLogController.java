package com.mn.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mn.entity.SysOperationLog;
import com.mn.enums.BusinessType;
import com.mn.form.SysOperationLogForm;
import com.mn.model.TableDataInfo;
import com.mn.security.annotation.Log;
import com.mn.service.ISysOperationLogService;
import com.mn.util.AjaxResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 操作日志记录
 *
 * <AUTHOR>
 * @since 2025-01-28
 */
@RestController
@RequestMapping("/system/operlog")
@Slf4j
public class SysOperationLogController extends BaseController {

    @Resource
    private ISysOperationLogService operationLogService;

    /**
     * 获取操作日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysOperationLogForm form) {
        startPage();
        List<SysOperationLog> list = operationLogService.selectOperationLogList(form);
        return getDataTable(list);
    }

    /**
     * 根据操作日志编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:log:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable Long logId) {
        return AjaxResult.success(operationLogService.getById(logId));
    }

    /**
     * 删除操作日志
     */
    @PreAuthorize("@ss.hasPermi('system:log:remove')")
    @Log(title = "操作日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds) {
        boolean result = operationLogService.deleteOperationLogByIds(logIds);
        return result ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
    }

    /**
     * 清空操作日志
     */
    @PreAuthorize("@ss.hasPermi('system:log:remove')")
    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public AjaxResult clean() {
        boolean result = operationLogService.cleanOperationLog();
        return result ? AjaxResult.success("清空成功") : AjaxResult.error("清空失败");
    }

    /**
     * 导出操作日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:log:export')")
    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysOperationLogForm form) {
        List<SysOperationLog> list = operationLogService.selectOperationLogList(form);
        // 这里可以添加导出逻辑，暂时返回成功
        log.info("导出操作日志，共{}条记录", list.size());
    }

    /**
     * 根据用户ID查询操作日志
     */
    @PreAuthorize("@ss.hasPermi('system:log:query')")
    @GetMapping("/user/{userId}")
    public AjaxResult getLogsByUserId(@PathVariable Integer userId) {
        List<SysOperationLog> list = operationLogService.selectOperationLogByUserId(userId);
        return AjaxResult.success(list);
    }

    /**
     * 根据集团ID查询操作日志
     */
    @PreAuthorize("@ss.hasPermi('system:log:query')")
    @GetMapping("/dept/{deptId}")
    public AjaxResult getLogsByDeptId(@PathVariable Integer deptId) {
        List<SysOperationLog> list = operationLogService.selectOperationLogByDeptId(deptId);
        return AjaxResult.success(list);
    }
}
