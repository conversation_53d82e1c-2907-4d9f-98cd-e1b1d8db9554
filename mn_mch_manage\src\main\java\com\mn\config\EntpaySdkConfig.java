package com.mn.config;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import com.tenpay.business.entpay.mse.sdk.config.EntpayConfig;
import com.tenpay.business.entpay.mse.sdk.enums.EnvironmentEnum;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class EntpaySdkConfig implements InitializingBean {

    @Autowired
    EntpayProperties properties;

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("begin init entpay config...");
        EntpayConfig.setNormalMode(properties.getPlatformId(),
                properties.getPlatformPrivateKey(),
                properties.getPlatformPrivateCertSerialNo(),
                properties.getTbepSerialNo(),
                properties.getTbepPublicKey());
        // 设置联调环境地址
        EntpayConfig.setEnv(EnvironmentEnum.SANDBOX);
        // 三个默认都是10秒
        // 从连接管理器请求连接时使用的超时（以毫秒为单位）
        EntpayConfig.setConnectionRequestTimeout(3000);
        // 确定连接建立之前的超时时间（以毫秒为单位）
        EntpayConfig.setConnectTimeout(5000);
        // 定义套接字超时（SO_TIMEOUT），以毫秒为单位，这是等待数据的超时。
        EntpayConfig.setSocketTimeout(15000);
        log.info("init entpay config done");

    }
}
