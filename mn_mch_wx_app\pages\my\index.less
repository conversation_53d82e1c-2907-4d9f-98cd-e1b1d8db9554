@import '/variable.less';

.my-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 顶部用户信息区域 */
.user-header-section {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 40rpx 30rpx 30rpx;
  color: #fff;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  overflow: hidden;
}

.user-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.8;
}

.edit-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 编辑图标 */
.icon-edit::before {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #fff;
  border-radius: 4rpx;
  top: 12rpx;
  left: 12rpx;
}

.icon-edit::after {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 3rpx;
  background-color: #fff;
  transform: rotate(45deg);
  top: 18rpx;
  right: 8rpx;
}

/* 功能菜单区域 */
.menu-section {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.06);
}

.menu-list {
  padding: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.menu-icon .css-icon {
  width: 36rpx;
  height: 36rpx;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.menu-arrow {
  width: 24rpx;
  height: 24rpx;
}

/* 箭头图标 */
.icon-arrow-right::before {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border-top: 2rpx solid #ccc;
  border-right: 2rpx solid #ccc;
  transform: rotate(45deg);
  top: 6rpx;
  left: 6rpx;
}

/* CSS图标基础样式 */
.css-icon {
  width: 48rpx;
  height: 48rpx;
  position: relative;
  display: inline-block;
}

/* 复用首页的图标样式 */
.icon-product::before {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #fff;
  border-radius: 6rpx;
  top: 6rpx;
  left: 6rpx;
}

.icon-product::after {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: #fff;
  border-radius: 3rpx;
  top: 12rpx;
  left: 12rpx;
}

.icon-home::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 16rpx solid currentColor;
  top: 8rpx;
  left: 12rpx;
}

.icon-home::after {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 12rpx;
  background-color: currentColor;
  bottom: 8rpx;
  left: 15rpx;
}

.icon-user::before {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border: 2rpx solid currentColor;
  border-radius: 50%;
  top: 8rpx;
  left: 18rpx;
}

.icon-user::after {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 10rpx;
  border: 2rpx solid currentColor;
  border-top: none;
  border-radius: 0 0 9rpx 9rpx;
  bottom: 8rpx;
  left: 15rpx;
}

/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
  z-index: 1000;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1890ff;
}

.tab-css-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}

.tab-text {
  font-size: 24rpx;
  line-height: 1;
}

/* 菜单图标样式 */
.icon-order::before {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 30rpx;
  border: 3rpx solid #fff;
  border-radius: 3rpx;
  top: 3rpx;
  left: 6rpx;
}

.icon-order::after {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 2rpx;
  background-color: #fff;
  top: 12rpx;
  left: 12rpx;
  box-shadow: 0 6rpx 0 #fff, 0 12rpx 0 #fff;
}

.icon-location::before {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 24rpx;
  border: 3rpx solid #fff;
  border-radius: 9rpx 9rpx 9rpx 0;
  transform: rotate(-45deg);
  top: 6rpx;
  left: 12rpx;
}

.icon-location::after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background-color: #fff;
  border-radius: 50%;
  top: 12rpx;
  left: 16rpx;
}

.icon-finance::before {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #fff;
  border-radius: 50%;
  top: 6rpx;
  left: 6rpx;
}

.icon-finance::after {
  content: '¥';
  position: absolute;
  color: #fff;
  font-size: 16rpx;
  font-weight: bold;
  top: 12rpx;
  left: 14rpx;
}

.icon-service::before {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 18rpx;
  border: 3rpx solid #fff;
  border-radius: 50%;
  top: 9rpx;
  left: 9rpx;
}

.icon-service::after {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 6rpx;
  border: 2rpx solid #fff;
  border-top: none;
  border-radius: 0 0 12rpx 12rpx;
  bottom: 6rpx;
  left: 12rpx;
}

.icon-setting::before {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 18rpx;
  border: 3rpx solid #fff;
  border-radius: 50%;
  top: 9rpx;
  left: 9rpx;
}

.icon-setting::after {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #fff;
  border-radius: 3rpx;
  top: 6rpx;
  left: 6rpx;
  transform: rotate(45deg);
}
