package com.mn.controller;

import com.mn.entity.SalesOrderItem;
import com.mn.form.SalesOrderItemForm;
import com.mn.model.TableDataInfo;
import com.mn.service.ISalesOrderItemService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 销售单明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@RestController
@RequestMapping("/sales-order-item")
public class SalesOrderItemController extends BaseController {

    @Resource
    private ISalesOrderItemService salesOrderItemService;

    /**
     * 获取销售单明细列表
     */
    @PreAuthorize("@ss.hasPermi('sales:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(SalesOrderItemForm form) {
        startPage();
        form.setDeptId(getDeptId());
        List<SalesOrderItem> list = salesOrderItemService.selectSalesOrderItemList(form);
        return getDataTable(list);
    }

    /**
     * 根据ID获取销售单明细信息
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:query')")
    @GetMapping(value = "/{itemId}")
    public AjaxResult getInfo(@PathVariable Long itemId) {
        return success(salesOrderItemService.getById(itemId));
    }

    /**
     * 新增销售单明细
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody SalesOrderItem salesOrderItem) {
        salesOrderItem.setCreateTime(DateUtils.getNowDate());
        return toAjax(salesOrderItemService.insertSalesOrderItem(salesOrderItem));
    }

    /**
     * 批量新增销售单明细
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:add')")
    @PostMapping("/batchAdd")
    public AjaxResult batchAdd(@Validated @RequestBody List<SalesOrderItem> salesOrderItems) {
        Date now = DateUtils.getNowDate();
        for (SalesOrderItem item : salesOrderItems) {
            item.setCreateTime(now);
        }
        return toAjax(salesOrderItemService.batchInsertSalesOrderItem(salesOrderItems));
    }

    /**
     * 修改销售单明细
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:edit')")
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody SalesOrderItem salesOrderItem) {
        salesOrderItem.setUpdateTime(DateUtils.getNowDate());
        return toAjax(salesOrderItemService.updateSalesOrderItem(salesOrderItem));
    }

    /**
     * 批量修改销售单明细
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:edit')")
    @PostMapping("/batchEdit")
    public AjaxResult batchEdit(@Validated @RequestBody List<SalesOrderItem> salesOrderItems) {
        Date now = DateUtils.getNowDate();
        for (SalesOrderItem item : salesOrderItems) {
            item.setUpdateTime(now);
        }
        return toAjax(salesOrderItemService.batchUpdateSalesOrderItem(salesOrderItems));
    }

    /**
     * 删除销售单明细
     */
    //@PreAuthorize("@ss.hasPermi('sales:order:remove')")
    @PostMapping("/del")
    public AjaxResult remove(Long itemId) {
        return toAjax(salesOrderItemService.deleteByItemId(itemId));
    }
}
