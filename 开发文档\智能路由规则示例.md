# 智能路由规则配置示例

## 规则类型说明

### 1. FAULT_SWITCH (故障切换)
当检测到渠道故障或被风控时，自动切换到备用渠道。

### 2. REGION_LIMIT (区域限制)
根据用户所在地区选择不同的收款渠道。

### 3. QUOTA_LIMIT (额度限制)
根据渠道剩余额度和免费额度选择最优渠道。

### 4. SCENE_LIMIT (场景限制)
根据支付场景强制或优先选择特定渠道。

## 规则配置示例

### 1. 故障切换规则

```json
{
  "rule_name": "微信支付故障切换",
  "rule_type": "FAULT_SWITCH",
  "priority": 1,
  "conditions_json": {
    "channel_codes": ["WECHAT_PAY"],
    "risk_types": ["FAULT", "FREQUENCY"],
    "risk_levels": ["MEDIUM", "HIGH"]
  },
  "actions_json": {
    "switch_to": ["WEIQIFU", "ALIPAY"],
    "switch_order": "priority",
    "notify_admin": true,
    "auto_recover": true,
    "recover_check_interval": 300
  }
}
```

### 2. 区域限制规则

```json
{
  "rule_name": "华南地区优先微企付",
  "rule_type": "REGION_LIMIT",
  "priority": 2,
  "conditions_json": {
    "regions": {
      "provinces": ["广东", "广西", "海南", "福建"],
      "cities": ["深圳", "广州", "东莞", "佛山"]
    },
    "scene_codes": ["WECHAT_SCAN", "PC_WEB"]
  },
  "actions_json": {
    "preferred_channels": [
      {"channel_code": "WEIQIFU", "weight": 80},
      {"channel_code": "WECHAT_PAY", "weight": 20}
    ],
    "fallback_channels": ["ALIPAY", "UNIONPAY"]
  }
}
```

### 3. 额度限制规则

```json
{
  "rule_name": "免费额度优先使用",
  "rule_type": "QUOTA_LIMIT",
  "priority": 3,
  "conditions_json": {
    "quota_types": ["DAILY", "MONTHLY"],
    "free_quota_threshold": 0.1,
    "amount_range": {
      "min": 100,
      "max": 50000
    }
  },
  "actions_json": {
    "selection_strategy": "max_free_quota_first",
    "quota_check_order": ["DAILY", "MONTHLY", "YEARLY"],
    "switch_when_quota_exhausted": true,
    "alternative_channels": ["WEIQIFU", "ALIPAY", "UNIONPAY"]
  }
}
```

### 4. 场景限制规则

```json
{
  "rule_name": "微信扫码强制路由",
  "rule_type": "SCENE_LIMIT",
  "priority": 0,
  "conditions_json": {
    "scene_codes": ["WECHAT_SCAN"],
    "mandatory": true
  },
  "actions_json": {
    "allowed_channels": ["WECHAT_PAY", "WEIQIFU"],
    "default_channel": "WECHAT_PAY",
    "fallback_channel": "WEIQIFU",
    "check_channel_status": true
  }
}
```

### 5. 手续费优化规则

```json
{
  "rule_name": "手续费最优选择",
  "rule_type": "QUOTA_LIMIT",
  "priority": 4,
  "conditions_json": {
    "amount_range": {
      "min": 1000,
      "max": 100000
    },
    "time_range": {
      "start_hour": 9,
      "end_hour": 18
    }
  },
  "actions_json": {
    "selection_strategy": "lowest_fee_rate",
    "consider_free_quota": true,
    "fee_calculation": {
      "include_quota_savings": true,
      "projection_period": "monthly"
    },
    "channel_preferences": [
      {"channel_code": "WEIQIFU", "fee_rate": 0.0038},
      {"channel_code": "UNIONPAY", "fee_rate": 0.0050},
      {"channel_code": "WECHAT_PAY", "fee_rate": 0.0060},
      {"channel_code": "ALIPAY", "fee_rate": 0.0060}
    ]
  }
}
```

### 6. 交易频率控制规则

```json
{
  "rule_name": "高频交易分流",
  "rule_type": "QUOTA_LIMIT",
  "priority": 5,
  "conditions_json": {
    "frequency_limits": {
      "per_minute": 10,
      "per_hour": 100,
      "per_day": 1000
    },
    "merchant_types": ["B", "O"]
  },
  "actions_json": {
    "load_balancing": true,
    "distribution_strategy": "round_robin",
    "channel_weights": {
      "WECHAT_PAY": 30,
      "ALIPAY": 30,
      "WEIQIFU": 25,
      "UNIONPAY": 15
    },
    "frequency_check_interval": 60
  }
}
```

## 规则执行逻辑

### 1. 规则优先级
- 优先级数字越小，优先级越高
- 系统按优先级顺序执行规则
- 高优先级规则可以覆盖低优先级规则的结果

### 2. 规则匹配流程
```
1. 获取支付请求信息 (金额、场景、地区等)
2. 按优先级排序获取商户的智能路由规则
3. 逐个匹配规则条件
4. 执行匹配规则的动作
5. 合并多个规则的结果
6. 选择最终的支付渠道
```

### 3. 条件匹配逻辑
- **AND逻辑**: 同一规则内的多个条件需要同时满足
- **OR逻辑**: 数组内的多个值满足其一即可
- **范围匹配**: 支持数值范围、时间范围等
- **模糊匹配**: 支持地区、关键词等模糊匹配

### 4. 动作执行策略
- **立即执行**: 故障切换等紧急动作
- **权重选择**: 根据权重随机或轮询选择
- **优先级选择**: 按优先级顺序选择可用渠道
- **负载均衡**: 在多个渠道间分配流量

## 规则管理建议

### 1. 规则设计原则
- **简单明确**: 每个规则职责单一，条件清晰
- **优先级合理**: 重要规则优先级高，避免冲突
- **可测试**: 规则条件可验证，动作可追踪
- **可维护**: 规则配置易于理解和修改

### 2. 常用规则模板
- **基础故障切换**: 所有商户必备
- **场景强制路由**: 根据业务需求配置
- **成本优化**: 根据手续费和额度优化
- **风险控制**: 根据交易风险分散渠道

### 3. 规则监控指标
- **规则命中率**: 各规则的触发频率
- **渠道分布**: 各渠道的流量分配
- **成本节约**: 通过智能路由节约的手续费
- **故障恢复**: 故障切换的响应时间

### 4. 规则优化建议
- **定期评估**: 根据业务数据调整规则
- **A/B测试**: 对比不同规则配置的效果
- **实时调整**: 根据渠道状态动态调整
- **历史分析**: 基于历史数据优化规则参数
