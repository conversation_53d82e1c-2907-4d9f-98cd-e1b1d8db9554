package com.mn.controller;

import com.google.code.kaptcha.Producer;
import com.mn.constants.RedisConstants;
import com.mn.service.RedisService;
import com.mn.util.AjaxResult;
import com.mn.util.Base64;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.UUID;


@RestController
@Slf4j
public class CaptchaController
{
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Resource
    private RedisService redisService;

    private static final String captchaType = "math";

    /**
     * 生成验证码
     */
    @GetMapping("/captchaImage")
    public AjaxResult getCode(HttpServletResponse response) throws IOException
    {

        AjaxResult ajax = AjaxResult.success();
        ajax.put("captchaEnabled", true);
        // 保存验证码信息
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        String verifyKey = RedisConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;
        // 生成验证码
        String capText = captchaProducerMath.createText();
        //log.info("获取验证码"+capText);

        capStr = capText.substring(0, capText.lastIndexOf("@"));
        code = capText.substring(capText.lastIndexOf("@") + 1);

        image = captchaProducerMath.createImage(capStr);

        redisService.setString(verifyKey, code, 2L);
        log.info("设置verifyKey:"+verifyKey+"生成"+code);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try
        {
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e)
        {
            return AjaxResult.error(e.getMessage());
        }

        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }
}
