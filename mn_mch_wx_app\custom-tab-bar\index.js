const app = getApp();

Component({
  data: {
    value: '', // 初始值设置为空，避免第一次加载时闪烁
    //unreadNum: 0, // 未读消息数量
    list: [
      {
        icon: 'home',
        value: 'home',
        label: '首页',
      },
      {
        icon: 'view-list',
        value: 'product/list',
        label: '分类',
      },
      {
        icon: 'shop',
        value: 'order',
        label: '订单',
      },
      {
        icon: 'user',
        value: 'my',
        label: '我的',
      },
    ],
  },
  lifetimes: {
    ready() {
      const pages = getCurrentPages();
      const curPage = pages[pages.length - 1];
      if (curPage) {
        // 处理首页
        if (curPage.route === 'pages/home/<USER>') {
          this.setData({ value: 'home' });
        }
        // 处理产品分类页
        else if (curPage.route === 'pages/product/list/index') {
          this.setData({ value: 'product/list' });
        }
        // 处理订单页
        else if (curPage.route === 'pages/order/index') {
          this.setData({ value: 'order' });
        }
        // 处理我的页面
        else if (curPage.route === 'pages/my/index') {
          this.setData({ value: 'my' });
        }
      }
    },
  },
  methods: {
    handleChange(e) {
      const { value } = e.detail;
      console.log('Tab bar clicked, value:', value);

      // 路径映射
      let targetPath;
      switch (value) {
        case 'home':
          targetPath = '/pages/home/<USER>';
          break;
        case 'product/list':
          targetPath = '/pages/product/list/index';
          break;
        case 'order':
          targetPath = '/pages/order/index';
          break;
        case 'my':
          targetPath = '/pages/my/index';
          break;
        default:
          targetPath = `/pages/${value}/index`;
      }

      console.log('Navigating to:', targetPath);
      wx.switchTab({
        url: targetPath,
        success: () => {
          console.log('Navigation success');
        },
        fail: (err) => {
          console.error('Navigation failed:', err);
        }
      });
    },
  },
});
