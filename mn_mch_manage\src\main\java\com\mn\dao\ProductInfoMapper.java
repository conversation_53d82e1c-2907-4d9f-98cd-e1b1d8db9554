package com.mn.dao;

import com.mn.entity.ProductInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.form.ProductInfoForm;
import com.mn.form.WxProductInfoForm;

import java.util.List;

/**
 * <p>
 * 商品信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-08
 */
public interface ProductInfoMapper extends BaseMapper<ProductInfo> {

    List<ProductInfo> wxSelectProductInfo(WxProductInfoForm form);
    
    List<ProductInfo> selectProductInfoList(ProductInfoForm form);
    
    List<ProductInfo> selectProductInfoByName(String productName);

}
