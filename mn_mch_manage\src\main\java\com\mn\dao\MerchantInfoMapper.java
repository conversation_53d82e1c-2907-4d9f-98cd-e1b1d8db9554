package com.mn.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.entity.MerchantInfo;
import com.mn.form.MerchantInfoForm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商户信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-28
 */
public interface MerchantInfoMapper extends BaseMapper<MerchantInfo> {

    /**
     * 查询商户信息列表
     *
     * @param form 查询条件
     * @return 商户信息列表
     */
    List<MerchantInfo> selectMerchantInfoList(MerchantInfoForm form);

    /**
     * 根据集团ID查询商户列表
     *
     * @param deptId 集团ID
     * @return 商户列表
     */
    List<MerchantInfo> selectMerchantInfoByDeptId(@Param("deptId") Integer deptId);

    /**
     * 根据用户ID查询有权限的商户列表
     *
     * @param userId 用户ID
     * @return 商户列表
     */
    List<MerchantInfo> selectMerchantInfoByUserId(@Param("userId") Integer userId);

    /**
     * 检查统一社会信用代码是否存在
     *
     * @param unifiedSocialCreditCode 统一社会信用代码
     * @param merchantId 商户ID（排除自己）
     * @return 数量
     */
    int checkUnifiedSocialCreditCodeUnique(@Param("unifiedSocialCreditCode") String unifiedSocialCreditCode, 
                                          @Param("merchantId") Long merchantId);

    /**
     * 根据商户ID查询详细信息（包含关联信息）
     *
     * @param merchantId 商户ID
     * @return 商户详细信息
     */
    MerchantInfo selectMerchantInfoDetail(@Param("merchantId") Long merchantId);
}
