@import '/variable.less';

.product-list-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 搜索栏 */
.search-section {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-box {
  display: flex;
  align-items: center;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 24rpx;
  padding: 0 20rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0 16rpx;
}

.search-btn {
  margin-left: 20rpx;
  padding: 0 24rpx;
  height: 72rpx;
  line-height: 72rpx;
  background-color: #1890ff;
  color: #fff;
  font-size: 28rpx;
  border-radius: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.search-btn:active {
  background-color: #0c7cd5;
  transform: scale(0.95);
}

.clear-btn {
  padding: 8rpx;
  margin-left: 8rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.clear-btn:active {
  background-color: rgba(0, 0, 0, 0.1);
}

/* 主要内容区域 */
.main-content {
  display: flex;
  height: calc(100vh - 112rpx); /* 减去搜索栏的高度 */
}

/* 左侧分类栏 */
.category-sidebar {
  width: 200rpx;
  background-color: #f8f9fa;
  border-right: 1rpx solid #e8e8e8;
}

.category-scroll {
  height: 100%;
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #e8e8e8;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  position: relative;
}

.category-item.active {
  background-color: #fff;
  color: #1890ff;
}

.category-item.active::after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 40rpx;
  background-color: #1890ff;
  border-radius: 0 3rpx 3rpx 0;
}

.category-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

.category-item.active .category-text {
  color: #1890ff;
  font-weight: 500;
}

/* 右侧产品内容区域 */
.product-content {
  flex: 1;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

/* 分类标题 */
.category-title {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.search-keyword {
  display: block;
  font-size: 24rpx;
  color: #1890ff;
  margin-top: 8rpx;
  font-weight: normal;
}

/* 产品滚动区域 */
.product-scroll {
  flex: 1;
  height: 100%;
}

/* 产品列表 */
.product-list {
  padding: 20rpx;
}

.product-item {
  display: flex;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.product-item:active {
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.06);
}

/* 产品图片 */
.product-image-wrapper {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f8ff;
}

/* 产品信息 */
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-price-wrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: 12rpx;
}

.price-container {
  display: flex;
  align-items: center;
  position: relative;
}

.product-price {
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: 600;
  margin-right: 8rpx;
}

.product-unit {
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}

.price-tag {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  color: white;

  &.tier {
    background-color: #faad14;
  }

  &.level {
    background-color: #52c41a;
  }

  &.base {
    background-color: #8c8c8c;
  }
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-top: 4rpx;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-stock,
.product-sales {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f8ff;
  border-radius: 50%;
}

.empty-text {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-desc {
  color: #999;
  font-size: 26rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx;
  color: #1890ff;
  font-size: 28rpx;
}

.no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}

/* CSS图标样式 */
.css-icon {
  width: 48rpx;
  height: 48rpx;
  position: relative;
  display: inline-block;
}

/* 搜索图标 */
.icon-search {
  width: 32rpx;
  height: 32rpx;
}

.icon-search::before {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border: 3rpx solid #999;
  border-radius: 50%;
  top: 3rpx;
  left: 3rpx;
}

.icon-search::after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 3rpx;
  background-color: #999;
  transform: rotate(45deg);
  bottom: 3rpx;
  right: 3rpx;
}

/* 清除图标 */
.icon-clear {
  width: 32rpx;
  height: 32rpx;
}

.icon-clear::before {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 3rpx;
  background-color: #999;
  transform: rotate(45deg);
  top: 14rpx;
  left: 6rpx;
}

.icon-clear::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 3rpx;
  background-color: #999;
  transform: rotate(-45deg);
  top: 14rpx;
  left: 6rpx;
}

/* 空状态图标 */
.icon-empty-product {
  width: 80rpx;
  height: 80rpx;
}

.icon-empty-product::before {
  content: '';
  position: absolute;
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #1890ff;
  border-radius: 8rpx;
  top: 16rpx;
  left: 16rpx;
}

.icon-empty-product::after {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
  top: 28rpx;
  left: 28rpx;
}

/* 商品占位图标 */
.icon-product-placeholder {
  width: 80rpx;
  height: 80rpx;
}

.icon-product-placeholder::before {
  content: '';
  position: absolute;
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #1890ff;
  border-radius: 8rpx;
  top: 16rpx;
  left: 16rpx;
}

.icon-product-placeholder::after {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
  top: 28rpx;
  left: 28rpx;
}

/* 底部导航图标 */
.icon-home::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 16rpx solid currentColor;
  top: 8rpx;
  left: 12rpx;
}

.icon-home::after {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 12rpx;
  background-color: currentColor;
  bottom: 8rpx;
  left: 15rpx;
}

.icon-product::before {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid currentColor;
  border-radius: 6rpx;
  top: 6rpx;
  left: 6rpx;
}

.icon-product::after {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: currentColor;
  border-radius: 3rpx;
  top: 12rpx;
  left: 12rpx;
}

.icon-user::before {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border: 2rpx solid currentColor;
  border-radius: 50%;
  top: 8rpx;
  left: 18rpx;
}

.icon-user::after {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 10rpx;
  border: 2rpx solid currentColor;
  border-top: none;
  border-radius: 0 0 9rpx 9rpx;
  bottom: 8rpx;
  left: 15rpx;
}

/* 购物车图标 */
.icon-cart::before {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 18rpx;
  border: 2rpx solid currentColor;
  border-radius: 4rpx 4rpx 0 0;
  top: 12rpx;
  left: 12rpx;
}

.icon-cart::after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  border: 2rpx solid currentColor;
  border-radius: 50%;
  bottom: 6rpx;
  left: 20rpx;
}


