<view class="product-detail-container">
  <!-- 产品图片轮播 -->
  <view class="product-images-section">
    <swiper class="product-swiper" indicator-dots="{{imageList.length > 1}}" autoplay="{{false}}" circular="{{true}}" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#fff">
      <swiper-item wx:for="{{imageList}}" wx:key="index">
        <image class="product-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-url="{{item}}"></image>
      </swiper-item>
    </swiper>
    <view wx:if="{{imageList.length === 0}}" class="no-image">
      <view class="css-icon icon-product-placeholder"></view>
      <text>暂无图片</text>
    </view>
  </view>

  <!-- 产品基本信息 -->
  <view class="product-info-section">
    <view class="product-price-box">
      <view class="current-price">
        <text class="price-symbol">¥</text>
        <text class="price-value">{{minPrice}}</text>
        <text class="price-range" wx:if="{{maxPrice > minPrice}}"> ~ ¥{{maxPrice}}</text>
      </view>
      <view class="price-label">
        <text class="stock-info">已售{{productInfo.sales || 0}}+件</text>
      </view>
    </view>

    <view class="product-title">{{productInfo.productName}}</view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-action-bar">
    <view class="action-left">
      <view class="action-item" bindtap="goHome">
        <view class="action-icon home-icon"></view>
        <text class="action-text">首页</text>
      </view>
      <view class="action-item" bindtap="contactService">
        <view class="action-icon service-icon"></view>
        <text class="action-text">客服</text>
      </view>
    </view>
    <view class="action-right">
      <button class="action-btn cart-btn" bindtap="showSkuPopup" data-type="cart">
        <view class="cart-icon"></view>
        <text>购物车</text>
      </button>
      <button class="action-btn order-btn" bindtap="showSkuPopup" data-type="order">下单</button>
    </view>
  </view>

  <!-- 规格选择弹窗 -->
  <view class="sku-popup {{showSkuPopup ? 'show' : ''}}" bindtap="hideSkuPopup">
    <view class="popup-mask"></view>
    <view class="popup-content" catchtap="stopPropagation">
      <!-- 弹窗头部 -->
      <view class="popup-header">
        <view class="product-brief">
          <image class="brief-image" src="{{productInfo.mainImage}}" mode="aspectFill"></image>
          <view class="brief-info">
            <view class="brief-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{selectedSku ? selectedSku.price : minPrice}}</text>
              <text class="price-range" wx:if="{{!selectedSku && maxPrice > minPrice}}"> ~ ¥{{maxPrice}}</text>
            </view>
            <view class="brief-stock">库存{{selectedSku ? selectedSku.stock : totalStock}}件</view>
            <view class="brief-selected" wx:if="{{selectedSku}}">已选择：{{selectedSku.skuName}}</view>
          </view>
        </view>
        <view class="popup-close" bindtap="hideSkuPopup">×</view>
      </view>

      <!-- 规格选择 -->
      <view class="popup-body">
        <view wx:if="{{skuList.length > 0}}" class="sku-selection">
          <view class="selection-title">选择规格</view>
          <view class="sku-options">
            <view wx:for="{{skuList}}" wx:key="skuId"
                  class="sku-option {{selectedSkuId === item.skuId ? 'selected' : ''}}"
                  bindtap="selectSku"
                  data-id="{{item.skuId}}">
              <view class="sku-option-content">
                <view class="sku-name">{{item.skuName}}</view>
                <view class="sku-price-stock">
                  <text class="sku-price">¥{{item.price}}</text>
                  <text class="sku-stock">库存{{item.stock}}</text>
                </view>
              </view>
              <view class="sku-quantity">
                <view class="quantity-controls">
                  <view class="quantity-btn {{item.quantity <= 0 ? 'disabled' : ''}}"
                        bindtap="decreaseSkuQuantity"
                        data-id="{{item.skuId}}">-</view>
                  <input class="quantity-input"
                         type="number"
                         value="{{item.quantity || 0}}"
                         bindinput="onSkuQuantityInput"
                         data-id="{{item.skuId}}" />
                  <view class="quantity-btn {{item.quantity >= item.stock ? 'disabled' : ''}}"
                        bindtap="increaseSkuQuantity"
                        data-id="{{item.skuId}}">+</view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 总计信息 -->
        <view class="order-summary">
          <view class="summary-item">
            <text class="summary-label">已选择</text>
            <text class="summary-value">{{totalQuantity}}条</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">商品金额</text>
            <text class="summary-value total-amount">¥{{totalAmount}}</text>
          </view>
        </view>
      </view>

      <!-- 弹窗底部 -->
      <view class="popup-footer">
        <button class="confirm-btn" bindtap="confirmSelection">
          {{popupType === 'cart' ? '加入购物车' : '立即下单'}}
        </button>
      </view>
    </view>
  </view>

  <t-message id="t-message" />
</view>