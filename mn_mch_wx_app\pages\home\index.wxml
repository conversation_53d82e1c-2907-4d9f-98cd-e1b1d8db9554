<scroll-view scroll-y="true" class="home-container">
  <!-- 顶部企业信息区域 -->
  <view class="header-section">
    <view class="company-info">
      <view class="company-logo">
        <text class="logo-text">明</text>
      </view>
      <view class="company-details">
        <text class="company-name">明意湖集团</text>
        <text class="company-subtitle">工厂直供采购平台</text>
      </view>
    </view>
    <view class="header-stats">
      <view class="stat-item">
        <text class="stat-number">{{todayOrders || 0}}</text>
        <text class="stat-label">今日订单</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-number">{{monthAmount || '0.0'}}</text>
        <text class="stat-label">本月采购额(万)</text>
      </view>
    </view>

    <!-- 扩展统计信息 -->
    <view class="extended-stats">
      <view class="extended-stat-item">
        <text class="extended-stat-number">{{monthOrders || 0}}</text>
        <text class="extended-stat-label">本月订单</text>
      </view>
      <view class="extended-stat-item">
        <text class="extended-stat-number">{{unpaidOrders || 0}}</text>
        <text class="extended-stat-label">待付款</text>
      </view>
    </view>
  </view>

  <!-- 快捷功能区域 -->
  <view class="function-section">
    <view class="section-title">
      <text>快捷功能</text>
    </view>
    <view class="function-grid">
      <view class="function-item" bindtap="navigateToProductList">
        <view class="function-icon-wrapper">
          <view class="css-icon icon-product"></view>
        </view>
        <text class="function-text">产品目录</text>
      </view>
      <view class="function-item" bindtap="navigateToOrderList">
        <view class="function-icon-wrapper">
          <view class="css-icon icon-order"></view>
        </view>
        <text class="function-text">我的订单</text>
      </view>
      <view class="function-item" bindtap="navigateToInventory">
        <view class="function-icon-wrapper">
          <view class="css-icon icon-inventory"></view>
        </view>
        <text class="function-text">库存查询</text>
      </view>
      <view class="function-item" bindtap="navigateToFinance">
        <view class="function-icon-wrapper">
          <view class="css-icon icon-finance"></view>
        </view>
        <text class="function-text">账单明细</text>
      </view>
      <view class="function-item" bindtap="navigateToPrice">
        <view class="function-icon-wrapper">
          <view class="css-icon icon-price"></view>
        </view>
        <text class="function-text">价格政策</text>
      </view>
      <view class="function-item" bindtap="navigateToReport">
        <view class="function-icon-wrapper">
          <view class="css-icon icon-report"></view>
        </view>
        <text class="function-text">采购统计</text>
      </view>
      <view class="function-item" bindtap="navigateToService">
        <view class="function-icon-wrapper">
          <view class="css-icon icon-service"></view>
        </view>
        <text class="function-text">客户服务</text>
      </view>
      <view class="function-item" bindtap="navigateToNotice">
        <view class="function-icon-wrapper">
          <view class="css-icon icon-notice"></view>
        </view>
        <text class="function-text">通知公告</text>
      </view>
    </view>
  </view>



  <!-- 最新动态 -->
  <view class="news-section">
    <view class="section-title">
      <text>最新动态</text>
    </view>
    <view class="news-list">
      <view class="news-item" wx:for="{{newsList}}" wx:key="id">
        <view class="news-dot"></view>
        <view class="news-content">
          <text class="news-title">{{item.title}}</text>
          <text class="news-time">{{item.time}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部空白区域 -->
  <view class="bottom-space"></view>
</scroll-view>



<t-message id="t-message" />