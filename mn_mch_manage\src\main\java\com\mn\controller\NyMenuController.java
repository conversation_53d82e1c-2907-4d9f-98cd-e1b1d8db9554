package com.mn.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mn.entity.NyMenu;
import com.mn.form.MenuForm;
import com.mn.service.INyMenuService;
import com.mn.service.INyRoleService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@RestController
@RequestMapping("/menu")
@Slf4j
public class NyMenuController extends BaseController {

    @Resource
    INyMenuService menuService;

    @Resource
    INyRoleService roleService;

    /**
     * 获取菜单列表
     */
    @PreAuthorize("@ss.hasPermi('system:menu:list')")
    @GetMapping("/list")
    public AjaxResult list(MenuForm form) {
        List<NyMenu> menus = menuService.selectMenuList(form);

        return success(menus);
    }

    /**
     * 根据菜单编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:menu:query')")
    @GetMapping(value = "/{menuId}")
    public AjaxResult getInfo(@PathVariable Integer menuId) {
        return success(menuService.getById(menuId));
    }

    /**
     * 新增菜单
     */
    @PreAuthorize("@ss.hasPermi('system:menu:add')")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody NyMenu menu) {
        menu.setCreateBy(getUsername());
        menu.setCreateTime(DateUtils.getNowDate());
        return toAjax(menuService.save(menu));
    }

    /**
     * 修改菜单
     */
    @PreAuthorize("@ss.hasPermi('system:menu:edit')")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody NyMenu menu) {
        menu.setUpdateBy(getUsername());
        menu.setUpdateTime(DateUtils.getNowDate());
        return toAjax(menuService.updateById(menu));
    }

    /**
     * 删除菜单
     */
    @PreAuthorize("@ss.hasPermi('system:menu:remove')")
    @PostMapping("/del")
    public AjaxResult remove(Integer menuId) {
        if (menuService.countChildByMenuId(menuId) > 0) {
            return warn("存在子菜单,不允许删除");
        }
        if (menuService.countRoleByMenuId(menuId) > 0) {
            return warn("菜单已分配,不允许删除");
        }
        return toAjax(menuService.removeById(menuId));
    }

    /**
     * 获取菜单下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(MenuForm form) {
        List<NyMenu> menus = menuService.selectMenuList(form);
        return success(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
    public AjaxResult roleMenuTreeselect(@PathVariable Integer roleId) {
        MenuForm form = new MenuForm();
        List<NyMenu> menus = menuService.selectMenuList(form);

        AjaxResult ajax = AjaxResult.success();
        ajax.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
        ajax.put("menus", menuService.buildMenuTreeSelect(menus));
        return ajax;
    }

}
