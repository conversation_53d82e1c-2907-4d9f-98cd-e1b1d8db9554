import { listPartner } from "@/api/company/partner";
import { addSalesOrder, cancelCheck, checkSalesOrder, delSalesOrder, listSalesOrder, updateSalesOrder } from "@/api/sales/order";
import { batchAdd, batchEdit, delSalesOrderItem, listSalesOrderItem } from "@/api/sales/orderItem";
import { calculatePrice, getPrice } from "@/api/pricing/price";
import { parseTime } from "@/utils/ruoyi";

export default {
  /** 查询销售订单列表 */
  getList() {
    this.loading = true;
    const params = {
      ...this.queryParams
    };
    // 处理日期范围
    if (this.dateRange && this.dateRange.length === 2) {
      params.beginTime = this.dateRange[0];
      params.endTime = this.dateRange[1];
    }

    listSalesOrder(params).then(response => {
      this.orderList = response.rows;
      this.total = response.total;
      this.loading = false;
    });
  },
  /** 获取客户选项 */
  getPartnerOptions() {
    listPartner({ status: 1 }).then(response => {
      this.partnerOptions = response.rows || [];
    });
  },
  /** 搜索按钮操作 */
  handleQuery() {
    this.queryParams.pageNum = 1;
    this.getList();
  },
  /** 重置按钮操作 */
  resetQuery() {
    this.dateRange = [];
    this.resetForm("queryForm");
    this.handleQuery();
  },
  /** 新增按钮操作 */
  handleAdd() {
    this.reset();
    this.open = true;
    this.title = "新增销售订单";
    this.form.operationType = 'add';
    // 设置默认订单日期为今天
    this.form.orderDate = new Date().toISOString().split('T')[0];
  },

  /** 删除按钮操作 */
  handleDelete(row) {
    const orderId = row.orderId;
    this.$modal.confirm('是否确认删除订单编号为"' + row.orderNo + '"的数据项？').then(function () {
      return delSalesOrder(orderId);
    }).then(() => {
      this.getList();
      this.$modal.msgSuccess("删除成功");
    }).catch(() => { });
  },
  /** 取消按钮 */
  cancel() {
    this.open = false;
    this.reset();
  },
  // 计算实际金额
  calculateActualAmount() {
    // 将金额转换为分
    const totalAmount = this.form.totalAmount * 100;
    const discountAmount = this.form.discountAmount * 100;
    let actualAmount = totalAmount - discountAmount;
    if (actualAmount < 0) actualAmount = 0;
    // 转回元并保留两位小数
    this.form.actualAmount = actualAmount / 100;
  },
  // 表单重置
  reset() {
    this.form = {
      orderId: undefined,
      orderNo: undefined,
      deptId: undefined,
      partnerId: undefined,
      orderDate: undefined,
      totalAmount: 0,
      discountAmount: 0,
      actualAmount: 0,
      paymentStatus: 0,
      paymentAmount: 0,
      orderStatus: 0,
      remark: undefined,
      createTime: undefined,
      updateTime: undefined,
      createBy: undefined,
      updateBy: undefined,
      operationType: undefined
    };
    // 重置客户选择显示
    this.selectedPartnerName = '';
    // 重置订单明细
    this.orderItemList = [];
    this.resetForm("form");
  },
  /** 修改按钮操作 */
  handleUpdate(row) {
    const orderId = row.orderId;
    this.$router.push({ name: 'SalesOrderEdit', params: { orderId: orderId } });
  },
  /** 提交按钮 */
  submitForm() {
    this.$refs["form"].validate(valid => {
      if (valid) {
        const submitData = { ...this.form };

        // 将金额转换为分
        submitData.totalAmount = Math.round(submitData.totalAmount * 100);
        submitData.discountAmount = Math.round(submitData.discountAmount * 100);
        submitData.actualAmount = Math.round(submitData.actualAmount * 100);
        submitData.paymentAmount = Math.round(submitData.paymentAmount * 100);

        // 保存订单明细
        const saveOrderItems = async () => {
          try {
            await this.saveOrderItemData();
            this.$modal.msgSuccess(submitData.operationType === 'update' ? "修改成功" : "新增成功");
            this.open = false;
            this.getList();
          } catch (error) {
            this.$modal.msgError("保存订单明细失败：" + (error.message || "未知错误"));
          }
        };

        if (submitData.operationType === 'update') {
          updateSalesOrder(submitData).then(response => {
            saveOrderItems();
          }).catch(error => {
            this.$modal.msgError("保存订单失败：" + (error.message || "未知错误"));
          });
        } else {
          addSalesOrder(submitData).then(response => {
            // 设置订单ID，用于保存订单明细
            submitData.orderId = response.data;
            this.form.orderId = response.data;
            saveOrderItems();
          }).catch(error => {
            this.$modal.msgError("保存订单失败：" + (error.message || "未知错误"));
          });
        }
      }
    });
  },
  /** 处理订单明细组件更新总金额的事件 */
  handleUpdateTotalAmount(amount) {
    // 更新订单总金额
    this.form.totalAmount = amount;
    // 重新计算实际金额
    this.calculateActualAmount();
  },

  // 格式化日期显示
  formatDate(date) {
    return parseTime(date, '{y}-{m}-{d}');
  },

  // 格式化日期时间显示
  formatDateTime(dateTime) {
    return parseTime(dateTime);
  },

  // 表格行样式
  tableRowClassName({row}) {
    if (row.orderStatus === 1) {
      return 'success-row';
    } else if (row.paymentStatus === 0) {
      return 'warning-row';
    }
    return '';
  },

  // 计算明细总数量
  calculateTotalQuantity(items) {
    return items.reduce((sum, item) => {
      return sum + (parseFloat(item.quantity) || 0);
    }, 0).toFixed(2);
  },

  // 计算明细总金额
  calculateTotalAmount(items) {
    return items.reduce((sum, item) => {
      return sum + (item.amount || 0);
    }, 0);
  },

  // 查看详情时加载订单明细
  async loadDetailItems(orderId) {
    if (!orderId) return;

    this.detailItemsLoading = true;
    try {
      const response = await listSalesOrderItem({ orderId });
      if (response.rows && response.rows.length > 0) {
        this.detailItems = response.rows;
      } else {
        this.detailItems = [];
      }
    } catch (error) {
      console.error("获取订单明细失败", error);
      this.$message.error("获取订单明细失败");
    } finally {
      this.detailItemsLoading = false;
    }
  },

  // 查看详情方法
  handleDetail(row) {
    this.detailForm = { ...row };
    this.detailOpen = true;
    this.loadDetailItems(row.orderId);
  },

  // 生成订单编号
  generateOrderNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

    this.form.orderNo = `SO${year}${month}${day}${hours}${minutes}${seconds}${random}`;
  },

  // 打开商品选择器
  openProductSelector() {
    // 先关闭对话框
    this.productSelectorVisible = false;

    // 使用setTimeout确保在DOM完全更新后再打开对话框
    // 增加延迟时间，确保DOM有足够时间完成更新
    setTimeout(() => {
      this.productSelectorVisible = true;

      // 打印日志，帮助调试
      console.log('商品选择器对话框已打开', new Date().toISOString());
    }, 300);
  },

  // 处理商品选择确认
  handleProductsSelected(products) {
    if (!products || products.length === 0) return;

    // 将选中的商品添加到订单明细列表
    this.orderItemList = [...this.orderItemList, ...products];

    // 确保对话框关闭
    this.productSelectorVisible = false;

    // 更新订单总金额
    this.updateTotalAmount();

    // 显示成功消息
    this.$message.success(`成功添加 ${products.length} 个商品到订单明细`);
  },

  // 更新订单总金额
  updateTotalAmount() {
    // 计算订单总金额（元）
    this.form.totalAmount = this.totalAmount / 100;
    // 重新计算实际金额
    this.calculateActualAmount();
  },

  // 处理SKU选择变更
  async handleSkuChange(skuId, orderItem) {
    if (!skuId || !orderItem.skuOptions) return;

    const selectedSku = orderItem.skuOptions.find(sku => sku.skuId === skuId);
    if (selectedSku) {
      orderItem.skuCode = selectedSku.skuCode;
      orderItem.specData = selectedSku.specData;

      // 使用价格计算服务获取价格
      await this.calculateItemPrice(orderItem);

      // 更新订单总金额
      this.updateTotalAmount();
    }
  },

  // 处理数量变更
  async handleQuantityChange(orderItem, value) {
    orderItem.quantity = value;

    // 使用价格计算服务重新计算价格
    await this.calculateItemPrice(orderItem);

    // 更新订单总金额
    this.updateTotalAmount();
  },

  // 处理价格变更
  handlePriceChange(orderItem, value) {
    orderItem.price = value;
    // 更新金额
    orderItem.amount = Math.round(orderItem.price * orderItem.quantity * 100);
    // 更新订单总金额
    this.updateTotalAmount();
  },

  // 计算订单明细价格
  async calculateItemPrice(orderItem) {
    if (!orderItem.productId || !orderItem.skuId || !orderItem.quantity) {
      return;
    }

    try {
      // 显示价格计算中的状态
      orderItem.priceCalculating = true;

      const priceRequest = {
        partnerId: this.form.partnerId || 6, // 默认合作伙伴ID
        productId: orderItem.productId,
        skuId: orderItem.skuId,
        quantity: parseInt(orderItem.quantity),
        orderDate: this.form.orderDate || new Date().toISOString().split('T')[0]
      };

      const response = await calculatePrice(priceRequest);

      if (response.code === 200 && response.data) {
        const priceData = response.data;

        // 更新价格信息
        orderItem.price = priceData.finalPriceYuan || (priceData.finalPrice / 100);
        orderItem.amount = priceData.finalPrice * orderItem.quantity;
        orderItem.priceType = priceData.priceType;
        orderItem.priceDescription = priceData.priceDescription;

        // 显示价格计算结果
        if (priceData.priceType !== 'BASE_PRICE') {
          this.$message.success(`${orderItem.productName}: ${priceData.priceDescription}`);
        }
      } else {
        // 价格计算失败，使用默认价格
        this.$message.warning(`商品 ${orderItem.productName} 价格计算失败，使用默认价格`);
        orderItem.price = orderItem.defaultPrice || 0;
        orderItem.amount = Math.round(orderItem.price * orderItem.quantity * 100);
      }
    } catch (error) {
      console.error('价格计算失败:', error);
      this.$message.error(`商品 ${orderItem.productName} 价格计算失败: ${error.message}`);
      // 使用默认价格
      orderItem.price = orderItem.defaultPrice || 0;
      orderItem.amount = Math.round(orderItem.price * orderItem.quantity * 100);
    } finally {
      orderItem.priceCalculating = false;
    }
  },

  // 批量重新计算价格
  async recalculateAllPrices() {
    if (!this.orderItemList || this.orderItemList.length === 0) {
      this.$message.warning('没有订单明细需要计算价格');
      return;
    }

    if (!this.form.partnerId) {
      this.$message.warning('请先选择客户');
      return;
    }

    this.$message.info('正在重新计算所有商品价格...');

    for (const item of this.orderItemList) {
      await this.calculateItemPrice(item);
    }

    // 更新订单总金额
    this.updateTotalAmount();

    this.$message.success('价格重新计算完成');
  },

  // 移除订单明细
  async removeOrderItem(index) {
    const orderItem = this.orderItemList[index];
    if (orderItem.itemId) {
      try {
        await this.$modal.confirm('确定要删除该订单明细吗？');
        await delSalesOrderItem(orderItem.itemId);
        this.orderItemList.splice(index, 1);
        this.$message.success("删除成功");
        // 更新订单总金额
        this.updateTotalAmount();
      } catch (error) {
        // 用户取消或删除失败
        if (error) {
          console.error("删除订单明细失败", error);
          this.$message.error("删除订单明细失败");
        }
      }
    } else {
      this.orderItemList.splice(index, 1);
      // 更新订单总金额
      this.updateTotalAmount();
    }
  },

  // 获取订单明细列表
  async getOrderItemList() {
    if (!this.form.orderId) {
      // 如果没有orderId，清空明细列表
      this.orderItemList = [];
      this.itemsLoading = false;
      return;
    }

    this.itemsLoading = true;
    try {
      const response = await listSalesOrderItem({ orderId: this.form.orderId });
      if (response.rows && response.rows.length > 0) {
        // 处理价格显示（后端存储单位为分，前端显示为元）
        this.orderItemList = response.rows.map(item => {
          return {
            ...item,
            price: item.price ? item.price / 100 : 0,
            // 保留amount为分单位，用于计算
            skuOptions: [] // 初始化为空数组，需要时再加载
          };
        });

        // 为每个明细项加载SKU选项
        for (const item of this.orderItemList) {
          if (item.productId) {
            try {
              const skuResponse = await this.$api.product.listProductSku({ productId: item.productId });
              item.skuOptions = (skuResponse.rows || []).map(sku => ({
                ...sku,
                price: sku.price ? sku.price / 100 : 0
              }));
            } catch (error) {
              console.error("获取SKU列表失败", error);
            }
          }
        }

        // 更新订单总金额
        this.updateTotalAmount();
      } else {
        this.orderItemList = [];
      }
    } catch (error) {
      console.error("获取订单明细列表失败", error);
      this.$message.error("获取订单明细列表失败");
    } finally {
      this.itemsLoading = false;
    }
  },

  // 保存订单明细数据
  async saveOrderItemData() {
    // 如果没有orderId，不进行保存操作
    if (!this.form.orderId) {
      console.warn("无法保存订单明细数据：缺少orderId");
      return [];
    }

    if (this.orderItemList.length === 0) {
      return [];
    }

    // 数据验证
    for (let i = 0; i < this.orderItemList.length; i++) {
      const item = this.orderItemList[i];
      if (!item.productId) {
        this.$message.warning(`第${i + 1}行商品不能为空`);
        return Promise.reject(new Error("商品不能为空"));
      }
      if (!item.skuId) {
        this.$message.warning(`第${i + 1}行SKU不能为空`);
        return Promise.reject(new Error("SKU不能为空"));
      }
      if (!item.quantity || item.quantity <= 0) {
        this.$message.warning(`第${i + 1}行数量必须大于0`);
        return Promise.reject(new Error("数量必须大于0"));
      }
    }

    // 处理数据，转换价格单位（元转分）
    const itemDataList = this.orderItemList.map(item => {
      const itemData = { ...item };
      // 确保每个明细都有orderId
      itemData.orderId = this.form.orderId;
      itemData.price = Math.round(item.price * 100);
      // amount已经是分单位，不需要转换
      // 移除不需要提交的字段
      delete itemData.skuOptions;
      return itemData;
    });

    // 区分新增和更新
    const newItems = itemDataList.filter(item => !item.itemId);
    const updateItems = itemDataList.filter(item => item.itemId);

    try {
      // 批量新增
      if (newItems.length > 0) {
        await batchAdd(newItems);
      }

      // 批量更新
      if (updateItems.length > 0) {
        await batchEdit(updateItems);
      }

      this.$message.success("保存成功");
      await this.getOrderItemList(); // 刷新列表
      return this.orderItemList;
    } catch (error) {
      this.$message.error("保存失败：" + (error.message || "未知错误"));
      return Promise.reject(error);
    }
  },

  /** 审核订单 */
  handleApproveOrder(row) {
    const orderId = row.orderId;
    this.$modal.confirm('是否确认审核订单编号为"' + row.orderNo + '"的订单？').then(function() {
      return checkSalesOrder(orderId);
    }).then(() => {
      this.getList();
      this.$modal.msgSuccess("审核成功");
      // 如果当前正在查看详情，更新详情数据
      if (this.detailOpen && this.detailForm.orderId === orderId) {
        this.detailForm.orderStatus = 1;
      }
    }).catch(() => {});
  },

  /** 取消审核 */
  handleCancelApproval(row) {
    const orderId = row.orderId;
    this.$modal.confirm('是否确认取消审核订单编号为"' + row.orderNo + '"的订单？').then(function() {
      return cancelCheck(orderId);
    }).then(() => {
      this.getList();
      this.$modal.msgSuccess("取消审核成功");
      // 如果当前正在查看详情，更新详情数据
      if (this.detailOpen && this.detailForm.orderId === orderId) {
        this.detailForm.orderStatus = 0;
      }
    }).catch(() => {});
  },

  /** 处理下拉菜单命令 */
  handleCommand(command, row) {
    switch (command) {
      case 'delete':
        this.handleDelete(row);
        break;
      default:
        break;
    }
  },

  /** 处理表格选择变更 */
  handleSelectionChange(selection) {
    this.multipleSelection = selection;
  },

  /** 生成收款单 */
  handleGeneratePayment() {
    // 检查是否有选中的行
    if (this.multipleSelection && this.multipleSelection.length > 0) {
      const selectedRows = this.multipleSelection;
      // 检查选中的订单是否符合条件（已审核且未付款）
      const validOrders = selectedRows.filter(row => row.orderStatus === 1 && row.paymentStatus === 0);

      if (validOrders.length === 0) {
        this.$message.warning('请选择已审核且未付款的订单');
        return;
      }

      // 如果只有一个有效订单，直接跳转到收款单页面
      if (validOrders.length === 1) {
        this.handleGeneratePaymentForOrder(validOrders[0]);
        return;
      }

      // 如果有多个有效订单，提示用户只能选择一个
      this.$message.warning('一次只能为一个订单生成收款单，请只选择一个订单');
    } else {
      this.$message.warning('请先选择一个已审核且未付款的订单');
    }
  },

  /** 为指定订单生成收款单 */
  handleGeneratePaymentForOrder(row) {
    // 检查订单状态
    if (row.orderStatus !== 1) {
      this.$message.warning('只能为已审核的订单生成收款单');
      return;
    }

    if (row.paymentStatus !== 0) {
      this.$message.warning('该订单已付款，无需生成收款单');
      return;
    }

    // 跳转到收款单页面
    this.$router.push({
      name: 'PaymentSlip',
      params: {
        orderId: row.orderId,
        orderNo: row.orderNo
      }
    });
  }
};