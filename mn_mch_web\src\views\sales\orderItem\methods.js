import { listSalesOrderItem, getSalesOrderItem, addSalesOrderItem, batchAdd, batchEdit, updateSalesOrderItem, delSalesOrderItem } from "@/api/sales/orderItem";

// 查询商品列表
export async function searchProducts(query) {
  if (query === '') {
    this.productOptions = [];
    return;
  }

  this.productLoading = true;
  try {
    // 这里需要替换为实际的商品查询API
    const response = await this.$api.product.listProduct({
      productName: query,
      productCode: query,
      pageNum: 1,
      pageSize: 20
    });

    this.productOptions = response.rows || [];
  } catch (error) {
    console.error("查询商品失败", error);
    this.$message.error("查询商品失败");
  } finally {
    this.productLoading = false;
  }
}

// 处理商品选择变更
export async function handleProductChange(productId) {
  if (!productId) return;

  try {
    // 获取商品详情
    const response = await this.$api.product.getProduct(productId);
    const product = response.data;

    if (!product) {
      this.$message.warning("未找到商品信息");
      return;
    }

    // 获取商品的SKU列表
    const skuResponse = await this.$api.product.listProductSku({ productId });
    const skuList = skuResponse.rows || [];

    // 创建新的订单明细项
    const newOrderItem = {
      productId: product.productId,
      productName: product.productName,
      productCode: product.productCode,
      skuId: null,
      skuCode: '',
      specData: '',
      unit: product.unit || '个',
      quantity: 1,
      price: 0,
      amount: 0,
      remark: '',
      skuOptions: skuList.map(sku => ({
        ...sku,
        price: sku.price ? sku.price / 100 : 0
      }))
    };

    // 如果只有一个SKU，自动选择
    if (skuList.length === 1) {
      const sku = skuList[0];
      newOrderItem.skuId = sku.skuId;
      newOrderItem.skuCode = sku.skuCode;
      newOrderItem.specData = sku.specData;
      newOrderItem.price = sku.price ? sku.price / 100 : 0;
      newOrderItem.amount = Math.round(newOrderItem.price * newOrderItem.quantity * 100);
    }

    // 添加到明细列表
    this.orderItemList.push(newOrderItem);

    // 清空选择，方便下次选择
    this.selectedProduct = null;
  } catch (error) {
    console.error("获取商品信息失败", error);
    this.$message.error("获取商品信息失败");
  }
}

// 处理SKU选择变更
export function handleSkuChange(skuId, orderItem) {
  if (!skuId || !orderItem.skuOptions) return;

  const selectedSku = orderItem.skuOptions.find(sku => sku.skuId === skuId);
  if (selectedSku) {
    orderItem.skuCode = selectedSku.skuCode;
    orderItem.specData = selectedSku.specData;
    orderItem.price = selectedSku.price || 0;
    // 更新金额
    orderItem.amount = Math.round(orderItem.price * orderItem.quantity * 100);
  }
}

// 处理数量变更
export function handleQuantityChange(orderItem, value) {
  orderItem.quantity = value;
  // 更新金额
  orderItem.amount = Math.round(orderItem.price * orderItem.quantity * 100);
}

// 处理价格变更
export function handlePriceChange(orderItem, value) {
  orderItem.price = value;
  // 更新金额
  orderItem.amount = Math.round(orderItem.price * orderItem.quantity * 100);
}


// 移除订单明细
export async function removeOrderItem(index) {
  const orderItem = this.orderItemList[index];
  if (orderItem.itemId) {
    try {
      await this.$modal.confirm('确定要删除该订单明细吗？');
      await delSalesOrderItem(orderItem.itemId);
      this.orderItemList.splice(index, 1);
      this.$message.success("删除成功");
    } catch (error) {
      // 用户取消或删除失败
      if (error) {
        console.error("删除订单明细失败", error);
        this.$message.error("删除订单明细失败");
      }
    }
  } else {
    this.orderItemList.splice(index, 1);
  }
}

// 获取订单明细列表
export async function getOrderItemList() {
  if (!this.orderId) {
    // 如果没有orderId，清空明细列表
    this.orderItemList = [];
    this.loading = false;
    return;
  }

  this.loading = true;
  try {
    const response = await listSalesOrderItem({ orderId: this.orderId });
    if (response.rows && response.rows.length > 0) {
      // 处理价格显示（后端存储单位为分，前端显示为元）
      this.orderItemList = response.rows.map(item => {
        return {
          ...item,
          price: item.price ? item.price / 100 : 0,
          // 保留amount为分单位，用于计算
          skuOptions: [] // 初始化为空数组，需要时再加载
        };
      });

      // 为每个明细项加载SKU选项
      for (const item of this.orderItemList) {
        if (item.productId) {
          try {
            const skuResponse = await this.$api.product.listProductSku({ productId: item.productId });
            item.skuOptions = (skuResponse.rows || []).map(sku => ({
              ...sku,
              price: sku.price ? sku.price / 100 : 0
            }));
          } catch (error) {
            console.error("获取SKU列表失败", error);
          }
        }
      }
    } else {
      this.orderItemList = [];
    }
  } catch (error) {
    console.error("获取订单明细列表失败", error);
    this.$message.error("获取订单明细列表失败");
  } finally {
    this.loading = false;
  }
}

// 保存订单明细数据
export async function saveOrderItemData() {
  // 如果没有orderId，不进行保存操作
  if (!this.orderId) {
    console.warn("无法保存订单明细数据：缺少orderId");
    return [];
  }

  if (this.orderItemList.length === 0) {
    return [];
  }

  // 数据验证
  for (let i = 0; i < this.orderItemList.length; i++) {
    const item = this.orderItemList[i];
    if (!item.productId) {
      this.$message.warning(`第${i + 1}行商品不能为空`);
      return Promise.reject();
    }
    if (!item.skuId) {
      this.$message.warning(`第${i + 1}行SKU不能为空`);
      return Promise.reject();
    }
    if (!item.quantity || item.quantity <= 0) {
      this.$message.warning(`第${i + 1}行数量必须大于0`);
      return Promise.reject();
    }
  }

  // 处理数据，转换价格单位（元转分）
  const itemDataList = this.orderItemList.map(item => {
    const itemData = { ...item };
    // 确保每个明细都有orderId
    itemData.orderId = this.orderId;
    itemData.price = Math.round(item.price * 100);
    // amount已经是分单位，不需要转换
    // 移除不需要提交的字段
    delete itemData.skuOptions;
    return itemData;
  });

  // 区分新增和更新
  const newItems = itemDataList.filter(item => !item.itemId);
  const updateItems = itemDataList.filter(item => item.itemId);

  try {
    // 批量新增
    if (newItems.length > 0) {
      await batchAdd(newItems);
    }

    // 批量更新
    if (updateItems.length > 0) {
      await batchEdit(updateItems);
    }

    this.$message.success("保存成功");
    await this.getOrderItemList(); // 刷新列表
    return this.orderItemList;
  } catch (error) {
    this.$message.error("保存失败：" + (error.message || "未知错误"));
    return Promise.reject(error);
  }
}

// 获取订单明细数据
export function getOrderItemData() {
  return this.orderItemList;
}

// 重置订单明细
export function resetOrderItems() {
  this.orderItemList = [];
  this.selectedProduct = null;
  this.productOptions = [];
  this.batchSettings = {
    price: 0,
    quantity: 0
  };
}

// 加载订单明细
export function loadOrderItems(orderId) {
  if (orderId) {
    this.orderId = orderId;
    this.getOrderItemList();
  } else {
    this.resetOrderItems();
  }
}

// 保存订单明细
export async function saveOrderItems(orderId) {
  if (orderId) {
    this.orderId = orderId;
  }

  // 计算总金额并通知父组件
  const totalAmount = this.orderItemList.reduce((sum, item) => {
    return sum + (item.amount || 0);
  }, 0) / 100; // 转换为元

  this.$emit('update-total-amount', totalAmount);

  return this.saveOrderItemData();
}