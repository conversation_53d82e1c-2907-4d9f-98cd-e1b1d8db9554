package com.mn.controller;

import com.mn.model.PriceCalculateRequest;
import com.mn.model.PriceCalculateResponse;
import com.mn.service.IPriceCalculateService;
import com.mn.util.MisRspModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 价格计算控制器
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@RestController
@RequestMapping("/price")
@Slf4j
public class PriceController {

    @Autowired
    private IPriceCalculateService priceCalculateService;

    /**
     * 计算单个商品价格
     */
    @PostMapping("/calculate")
    public MisRspModel calculatePrice(@RequestBody PriceCalculateRequest request) {
        try {
            log.info("【价格计算】接收到价格计算请求: {}", request);
            
            PriceCalculateResponse response = priceCalculateService.calculatePrice(request);
            
            log.info("【价格计算】价格计算完成: 最终价格={}分, 价格类型={}", 
                    response.getFinalPrice(), response.getPriceType());
            
            return new MisRspModel(response);
        } catch (Exception e) {
            log.error("【价格计算】价格计算失败", e);
            return new MisRspModel(-1, "价格计算失败: " + e.getMessage());
        }
    }

    /**
     * 批量计算商品价格
     */
    @PostMapping("/batchCalculate")
    public MisRspModel batchCalculatePrice(@RequestBody List<PriceCalculateRequest> requests) {
        try {
            log.info("【批量价格计算】接收到批量价格计算请求，数量: {}", requests.size());
            
            List<PriceCalculateResponse> responses = priceCalculateService.batchCalculatePrice(requests);
            
            log.info("【批量价格计算】批量价格计算完成，返回{}条结果", responses.size());
            
            return new MisRspModel(responses);
        } catch (Exception e) {
            log.error("【批量价格计算】批量价格计算失败", e);
            return new MisRspModel(-1, "批量价格计算失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品价格信息（用于前端展示）
     */
    @GetMapping("/getPrice")
    public MisRspModel getPrice(@RequestParam Long productId, 
                               @RequestParam Long skuId,
                               @RequestParam(required = false) Long partnerId,
                               @RequestParam(required = false) Integer quantity) {
        try {
            PriceCalculateRequest request = new PriceCalculateRequest();
            request.setProductId(productId);
            request.setSkuId(skuId);
            request.setPartnerId(partnerId != null ? partnerId : 6L); // 默认合作伙伴
            request.setQuantity(quantity != null ? quantity : 1);
            request.setOrderDate(java.time.LocalDate.now());
            
            PriceCalculateResponse response = priceCalculateService.calculatePrice(request);
            
            return new MisRspModel(response);
        } catch (Exception e) {
            log.error("【获取价格】获取商品价格失败", e);
            return new MisRspModel(-1, "获取商品价格失败: " + e.getMessage());
        }
    }
}
