package com.mn.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.mn.entity.NyRole;
import com.mn.entity.NyUser;
import com.mn.form.DeptForm;
import com.mn.form.UserForm;
import com.mn.model.TableDataInfo;
import com.mn.service.INyDeptService;
import com.mn.service.INyRoleService;
import com.mn.service.INyUserService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;
import com.mn.util.MeloonException;
import com.mn.util.SecurityUtils;
import com.mn.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@RestController
@RequestMapping("/user")
@Slf4j
public class NyUserController extends BaseController {

    @Resource
    INyUserService userService;

    @Resource
    INyRoleService roleService;

    @Resource
    INyDeptService deptService;

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('myShop:mchUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserForm form) {
        startPage();
        List<NyUser> list = userService.selectUserList(form);
        return getDataTable(list);
    }

    /**
     * 获取公司用户列表
     */
    @GetMapping("/companyUserList")
    public TableDataInfo companyUserList(UserForm form) {
        form.setDeptId(getDeptId());
        startPage();
        List<NyUser> list = userService.selectUserList(form);
        return getDataTable(list);
    }

    @GetMapping("/companyGetInfo")
    public AjaxResult companyGetInfo(Integer userId) {
        AjaxResult ajax = AjaxResult.success();
        List<NyRole> roles = roleService.companySelectRoleAll();
        ajax.put("roles", roles);
        if (StringUtils.isNotNull(userId)) {
            NyUser sysUser = userService.getById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("roleIds", roleService.selectRoleListByUserId(userId));
        }
        return ajax;
    }

    @PostMapping("/companyAdd")
    public AjaxResult companyAdd(@RequestBody NyUser user) {
        if (!userService.checkUserNameUnique(user.getUserName())) {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        Integer[] roleIds = user.getRoleIds();
        if (StringUtils.isNotEmpty(roleIds)) {
            for (Integer roleId : roleIds) {
                NyRole role = roleService.getById(roleId);
                if (null == role)
                    throw new MeloonException("角色不存在");
                if ("0".equals(role.getIsCompany().toString()))
                    throw new MeloonException("无法分配的角色");
            }
        }
        user.setCreateBy(getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setCreateTime(DateUtils.getNowDate());
        user.setDeptId(getDeptId());
        return toAjax(userService.insertUser(user));
    }

    @PostMapping("/companyEdit")
    public AjaxResult companyEdit(@RequestBody NyUser user) {
        NyUser dbuser = userService.getById(user.getUserId());
        if (null == dbuser)
            throw new MeloonException("用户不存在");
        if (getUserId().equals(user.getUserId()))
            throw new MeloonException("无法修改自己的信息");
        if (!"1".equals(dbuser.getEnableFlag().toString()))
            throw new MeloonException("用户已被停用");
        if (!dbuser.getDeptId().equals(getDeptId()))
            return error("无操作权限");
        Integer[] roleIds = user.getRoleIds();
        if (StringUtils.isNotEmpty(roleIds)) {
            for (Integer roleId : roleIds) {
                NyRole role = roleService.getById(roleId);
                if (null == role)
                    throw new MeloonException("角色不存在");
                if ("0".equals(role.getIsCompany().toString()))
                    throw new MeloonException("无法分配的角色");
            }
        }
        dbuser.setNickName(user.getNickName());
        dbuser.setRoleIds(roleIds);
        dbuser.setUpdateBy(getUsername());
        dbuser.setUpdateTime(DateUtils.getNowDate());
        return toAjax(userService.updateUser(dbuser));
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = { "/", "/{userId}" })
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Integer userId) {
        AjaxResult ajax = AjaxResult.success();
        List<NyRole> roles = roleService.selectRoleAll();
        ajax.put("roles", roles);
        if (StringUtils.isNotNull(userId)) {
            NyUser sysUser = userService.getById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("roleIds", roleService.selectRoleListByUserId(userId));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('myShop:mchUser:list')")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody NyUser user) {
        if (!userService.checkUserNameUnique(user.getUserName())) {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        user.setCreateBy(getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setCreateTime(DateUtils.getNowDate());
        user.setDeptId(getDeptId());
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('myShop:mchUser:list')")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody NyUser user) {
        NyUser dbuser = userService.getById(user.getUserId());
        if (null == dbuser)
            throw new MeloonException("用户不存在");
        if (getUserId().equals(user.getUserId()))
            throw new MeloonException("无法修改自己的信息");
        dbuser.setUpdateBy(getUsername());
        dbuser.setUpdateTime(DateUtils.getNowDate());
        return toAjax(userService.updateUser(dbuser));
    }

    /**
     * 删除用户
     */
    // @PreAuthorize("@ss.hasPermi('myShop:mchUser:list')")
    @PostMapping("/del")
    public AjaxResult remove(Integer userId) {
        return toAjax(userService.deleteUserById(userId));
    }

    /**
     * 重置密码
     */
    // @PreAuthorize("@ss.hasPermi('myShop:mchUser:list')")
    @PostMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody NyUser user) {
        NyUser dbUser = userService.getById(user.getUserId());
        if (null == dbUser)
            throw new MeloonException("用户不存在");
        log.info("重置密码:" + JSON.toJSONString(user));
        dbUser.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        dbUser.setUpdateBy(getUsername());
        dbUser.setUpdateTime(DateUtils.getNowDate());
        return toAjax(userService.updateById(dbUser));
    }

    @PostMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody NyUser user) {
        NyUser dbUser = userService.getById(user.getUserId());
        if (null == dbUser)
            throw new MeloonException("用户不存在");
        dbUser.setEnableFlag(user.getEnableFlag());
        dbUser.setUpdateBy(getUsername());
        dbUser.setUpdateTime(DateUtils.getNowDate());
        return toAjax(userService.updateById(dbUser));
    }

    /**
     * 获取部门树列表
     */
    @PreAuthorize("@ss.hasPermi('myShop:mchUser:list')")
    @GetMapping("/deptTree")
    public AjaxResult deptTree(DeptForm form) {
        return success(deptService.selectDeptTreeList(form));
    }

    /**
     * 设置集团管理员
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @PostMapping("/setDeptAdmin")
    public AjaxResult setDeptAdmin(@RequestBody NyUser user) {
        if (user.getUserId() == null || user.getIsDeptAdmin() == null) {
            return error("参数不能为空");
        }

        NyUser dbUser = userService.getById(user.getUserId());
        if (dbUser == null) {
            return error("用户不存在");
        }

        dbUser.setIsDeptAdmin(user.getIsDeptAdmin());
        dbUser.setUpdateBy(getUsername());
        dbUser.setUpdateTime(DateUtils.getNowDate());

        return toAjax(userService.updateById(dbUser));
    }

    /**
     * 获取集团下的用户列表（集团管理员使用）
     */
    @GetMapping("/deptUserList")
    public TableDataInfo deptUserList(UserForm form) {
        // 集团管理员只能查看自己集团下的用户
        form.setDeptId(getDeptId());
        startPage();
        List<NyUser> list = userService.selectUserList(form);
        return getDataTable(list);
    }

    /**
     * 获取集团角色列表（集团管理员使用）
     */
    @GetMapping("/deptRoles")
    public AjaxResult deptRoles() {
        List<NyRole> roles = roleService.companySelectRoleAll();
        return success(roles);
    }

}
