package com.mn.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(value = "mqtt.enabled", havingValue = "true")
@Slf4j
public class MqttMsgSubscribe implements MessageHandler {

    @Override
    public void handleMessage(Message<?> message) throws MessagingException {
        String id = String.valueOf(message.getHeaders().get(MqttHeaders.ID));
        String topic = String.valueOf(message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC));
        String payload = String.valueOf(message.getPayload());
        log.info("Mqtt服务器ID-->{},订阅主题-->{},收到消息-->{}", id, topic, payload);
    }

}
