package com.mn.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 支付配置
 * 
 * <AUTHOR>
 * @since 2025-01-21
 */
@Component
@ConfigurationProperties(prefix = "payment")
public class PaymentConfig {
    
    /**
     * 支付过期时间（分钟）
     */
    private Integer expireMinutes = 5;
    
    /**
     * 支付类型 - 二维码支付
     */
    private Integer qrCodePayType = 3;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;
    
    /**
     * 锁超时时间（秒）
     */
    private Integer lockTimeoutSeconds = 30;
    
    public Integer getExpireMinutes() {
        return expireMinutes;
    }
    
    public void setExpireMinutes(Integer expireMinutes) {
        this.expireMinutes = expireMinutes;
    }
    
    public Integer getQrCodePayType() {
        return qrCodePayType;
    }
    
    public void setQrCodePayType(Integer qrCodePayType) {
        this.qrCodePayType = qrCodePayType;
    }
    
    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }
    
    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }
    
    public Integer getLockTimeoutSeconds() {
        return lockTimeoutSeconds;
    }
    
    public void setLockTimeoutSeconds(Integer lockTimeoutSeconds) {
        this.lockTimeoutSeconds = lockTimeoutSeconds;
    }
}
