package com.mn.controller;

import com.mn.dto.ProductV2DTO;
import com.mn.form.ProductV2Form;
import com.mn.form.ProductV2QueryForm;
import com.mn.model.TableDataInfo;
import com.mn.service.IProductV2Service;
import com.mn.util.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
// import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

// import javax.validation.Valid;
import java.util.List;

/**
 * 商品V2管理控制器
 * 提供新版本的商品管理功能，支持复杂的价格体系
 * 
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@RestController
@RequestMapping("/product-v2")
public class ProductV2Controller extends BaseController {

    @Autowired
    private IProductV2Service productV2Service;

    /**
     * 查询商品列表
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductV2QueryForm queryForm) {
        startPage();
        List<ProductV2DTO> list = productV2Service.selectProductList(queryForm);
        return getDataTable(list);
    }

    /**
     * 获取商品详细信息
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @GetMapping("/{productId}")
    public AjaxResult getInfo(@PathVariable("productId") Long productId,
                              @RequestParam(value = "includePrice", defaultValue = "true") boolean includePrice,
                              @RequestParam(value = "includeSku", defaultValue = "true") boolean includeSku,
                              @RequestParam(value = "includeLevelPrice", defaultValue = "false") boolean includeLevelPrice,
                              @RequestParam(value = "includeTierPrice", defaultValue = "false") boolean includeTierPrice) {
        ProductV2DTO product = productV2Service.selectProductById(productId, includePrice, includeSku, includeLevelPrice, includeTierPrice);
        return AjaxResult.success(product);
    }

    /**
     * 新增商品
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @PostMapping
    public AjaxResult add(@RequestBody ProductV2Form form) {
        try {
            Long productId = productV2Service.insertProduct(form);
            return AjaxResult.success("新增成功", productId);
        } catch (Exception e) {
            log.error("新增商品失败", e);
            return AjaxResult.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改商品
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @PutMapping
    public AjaxResult edit(@RequestBody ProductV2Form form) {
        try {
            boolean success = productV2Service.updateProduct(form);
            return success ? AjaxResult.success("修改成功") : AjaxResult.error("修改失败");
        } catch (Exception e) {
            log.error("修改商品失败", e);
            return AjaxResult.error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 删除商品
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @DeleteMapping("/{productIds}")
    public AjaxResult remove(@PathVariable Long[] productIds) {
        try {
            boolean success = productV2Service.deleteProducts(productIds);
            return success ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
        } catch (Exception e) {
            log.error("删除商品失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量上架/下架商品
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody ProductStatusForm form) {
        try {
            boolean success = productV2Service.updateProductStatus(form.getProductIds(), form.getStatus());
            String action = form.getStatus() ? "上架" : "下架";
            return success ? AjaxResult.success(action + "成功") : AjaxResult.error(action + "失败");
        } catch (Exception e) {
            log.error("更新商品状态失败", e);
            return AjaxResult.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 复制商品
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @PostMapping("/copy")
    public AjaxResult copyProduct(@RequestBody ProductCopyForm form) {
        try {
            Long newProductId = productV2Service.copyProduct(form.getProductId(), form.getNewProductName(), form.getNewProductCode());
            return AjaxResult.success("复制成功", newProductId);
        } catch (Exception e) {
            log.error("复制商品失败", e);
            return AjaxResult.error("复制失败：" + e.getMessage());
        }
    }

    /**
     * 获取商品SKU列表
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @GetMapping("/{productId}/skus")
    public AjaxResult getSkus(@PathVariable("productId") Long productId) {
        List<ProductV2DTO.ProductSkuV2DTO> skuList = productV2Service.selectSkusByProductId(productId);
        return AjaxResult.success(skuList);
    }

    /**
     * 获取商品基础价格
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @GetMapping("/{productId}/base-prices")
    public AjaxResult getBasePrices(@PathVariable("productId") Long productId,
                                    @RequestParam(value = "skuId", required = false) Long skuId) {
        List<ProductV2DTO.ProductBasePriceDTO> priceList = productV2Service.selectBasePricesByProductId(productId, skuId);
        return AjaxResult.success(priceList);
    }

    /**
     * 获取商品等级价格
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @GetMapping("/{productId}/level-prices")
    public AjaxResult getLevelPrices(@PathVariable("productId") Long productId,
                                     @RequestParam(value = "skuId", required = false) Long skuId,
                                     @RequestParam(value = "levelId", required = false) Integer levelId) {
        List<ProductV2DTO.CustomerLevelPriceDTO> priceList = productV2Service.selectLevelPricesByProductId(productId, skuId, levelId);
        return AjaxResult.success(priceList);
    }

    /**
     * 获取商品阶梯价格
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @GetMapping("/{productId}/tier-prices")
    public AjaxResult getTierPrices(@PathVariable("productId") Long productId,
                                    @RequestParam(value = "skuId", required = false) Long skuId,
                                    @RequestParam(value = "levelId", required = false) Integer levelId) {
        List<ProductV2DTO.QuantityTierPriceDTO> priceList = productV2Service.selectTierPricesByProductId(productId, skuId, levelId);
        return AjaxResult.success(priceList);
    }

    /**
     * 更新商品基础价格
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @PutMapping("/{productId}/base-prices")
    public AjaxResult updateBasePrices(@PathVariable("productId") Long productId,
                                       @RequestBody List<ProductV2Form.ProductBasePriceForm> basePriceList) {
        try {
            boolean success = productV2Service.updateProductBasePrice(productId, basePriceList);
            return success ? AjaxResult.success("更新基础价格成功") : AjaxResult.error("更新基础价格失败");
        } catch (Exception e) {
            log.error("更新基础价格失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 更新商品等级价格
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @PutMapping("/{productId}/level-prices")
    public AjaxResult updateLevelPrices(@PathVariable("productId") Long productId,
                                        @RequestBody List<ProductV2Form.CustomerLevelPriceForm> levelPriceList) {
        try {
            boolean success = productV2Service.updateProductLevelPrice(productId, levelPriceList);
            return success ? AjaxResult.success("更新等级价格成功") : AjaxResult.error("更新等级价格失败");
        } catch (Exception e) {
            log.error("更新等级价格失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 更新商品阶梯价格
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @PutMapping("/{productId}/tier-prices")
    public AjaxResult updateTierPrices(@PathVariable("productId") Long productId,
                                       @RequestBody List<ProductV2Form.QuantityTierPriceForm> tierPriceList) {
        try {
            boolean success = productV2Service.updateProductTierPrice(productId, tierPriceList);
            return success ? AjaxResult.success("更新阶梯价格成功") : AjaxResult.error("更新阶梯价格失败");
        } catch (Exception e) {
            log.error("更新阶梯价格失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 计算商品价格
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @GetMapping("/{productId}/calculate-price")
    public AjaxResult calculatePrice(@PathVariable("productId") Long productId,
                                     @RequestParam("skuId") Long skuId,
                                     @RequestParam(value = "levelId", required = false) Integer levelId,
                                     @RequestParam(value = "quantity", defaultValue = "1") Integer quantity) {
        try {
            Long price = productV2Service.calculateProductPrice(productId, skuId, levelId, quantity);
            return AjaxResult.success("计算成功", price);
        } catch (Exception e) {
            log.error("计算价格失败", e);
            return AjaxResult.error("计算失败：" + e.getMessage());
        }
    }

    /**
     * 验证商品编码唯一性
     */
    @GetMapping("/check-product-code")
    public AjaxResult checkProductCode(@RequestParam("productCode") String productCode,
                                       @RequestParam(value = "productId", required = false) Long productId) {
        boolean unique = productV2Service.checkProductCodeUnique(productCode, productId);
        return AjaxResult.success(unique);
    }

    /**
     * 验证SKU编码唯一性
     */
    @GetMapping("/check-sku-code")
    public AjaxResult checkSkuCode(@RequestParam("skuCode") String skuCode,
                                   @RequestParam(value = "skuId", required = false) Long skuId) {
        boolean unique = productV2Service.checkSkuCodeUnique(skuCode, skuId);
        return AjaxResult.success(unique);
    }

    /**
     * 获取商品统计信息
     */
    @PreAuthorize("@ss.hasPermi('product:v2:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(@RequestParam(value = "deptId", required = false) Integer deptId) {
        IProductV2Service.ProductV2StatisticsDTO statistics = productV2Service.getProductStatistics(deptId);
        return AjaxResult.success(statistics);
    }

    /**
     * 商品状态更新表单
     */
    public static class ProductStatusForm {
        private Long[] productIds;
        private Boolean status;

        public Long[] getProductIds() { return productIds; }
        public void setProductIds(Long[] productIds) { this.productIds = productIds; }
        public Boolean getStatus() { return status; }
        public void setStatus(Boolean status) { this.status = status; }
    }

    /**
     * 商品复制表单
     */
    public static class ProductCopyForm {
        private Long productId;
        private String newProductName;
        private String newProductCode;

        public Long getProductId() { return productId; }
        public void setProductId(Long productId) { this.productId = productId; }
        public String getNewProductName() { return newProductName; }
        public void setNewProductName(String newProductName) { this.newProductName = newProductName; }
        public String getNewProductCode() { return newProductCode; }
        public void setNewProductCode(String newProductCode) { this.newProductCode = newProductCode; }
    }
}
