#!/usr/bin/env python3
"""
MySQL MCP服务器 - 连接真实数据库版本
基于您的Navicat连接配置
"""

import asyncio
import json
import sys
import logging
import os
from typing import Dict, Any, List, Optional
import mysql.connector
from mysql.connector import Error

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealMySQLMCPServer:
    """连接真实MySQL数据库的MCP服务器"""
    
    def __init__(self):
        self.db_config = self._get_db_config()
        
    def _get_db_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        config = {
            "host": os.getenv("MYSQL_HOST", "localhost"),
            "port": int(os.getenv("MYSQL_PORT", "13306")),  # 使用您的端口
            "user": os.getenv("MYSQL_USER", "meloon"),
            "password": os.getenv("MYSQL_PASSWORD", "meloon2019"),
            "database": os.getenv("MYSQL_DATABASE", "mn_pay"),
            "charset": "utf8mb4",
            "autocommit": True,
            "ssl_disabled": True,
            "use_unicode": True,
            "connect_timeout": 10
        }
        
        logger.info(f"数据库配置: {config['host']}:{config['port']}/{config['database']}")
        return config
    
    def _get_connection(self):
        """获取数据库连接"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            return conn
        except Error as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
    
    async def handle_initialize(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理初始化请求"""
        # 测试数据库连接
        try:
            conn = self._get_connection()
            if conn.is_connected():
                logger.info(f"成功连接到MySQL服务器版本: {conn.server_info}")
                conn.close()
        except Exception as e:
            logger.error(f"初始化时数据库连接失败: {str(e)}")
        
        return {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "resources": {},
                "tools": {}
            },
            "serverInfo": {
                "name": "mysql-real-server",
                "version": "1.0.0"
            }
        }
    
    async def handle_list_resources(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """列出数据库表作为资源"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            cursor.close()
            conn.close()
            
            resources = []
            for table in tables:
                table_name = table[0]
                resources.append({
                    "uri": f"mysql://table/{table_name}",
                    "name": f"表: {table_name}",
                    "description": f"MySQL表 {table_name} 的数据",
                    "mimeType": "application/json"
                })
            
            return {"resources": resources}
        except Exception as e:
            logger.error(f"列出资源时出错: {str(e)}")
            return {"resources": []}
    
    async def handle_read_resource(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """读取表数据"""
        uri = params.get("uri", "")
        
        if uri.startswith("mysql://table/"):
            table_name = uri.replace("mysql://table/", "")
            try:
                conn = self._get_connection()
                cursor = conn.cursor()
                
                # 获取表结构
                cursor.execute(f"DESCRIBE {table_name}")
                columns_info = cursor.fetchall()
                columns = [col[0] for col in columns_info]
                
                # 获取数据（限制100行）
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 100")
                rows = cursor.fetchall()
                
                cursor.close()
                conn.close()
                
                content = {
                    "table": table_name,
                    "columns": columns,
                    "data": [list(row) for row in rows],
                    "row_count": len(rows)
                }
                
                return {
                    "contents": [{
                        "uri": uri,
                        "mimeType": "application/json",
                        "text": json.dumps(content, ensure_ascii=False, indent=2, default=str)
                    }]
                }
            except Exception as e:
                logger.error(f"读取资源时出错: {str(e)}")
                raise ValueError(f"读取表 {table_name} 失败: {str(e)}")
        
        raise ValueError(f"未知资源: {uri}")
    
    async def handle_list_tools(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """列出可用工具"""
        return {
            "tools": [
                {
                    "name": "execute_query",
                    "description": "执行SQL查询",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "要执行的SQL查询"
                            }
                        },
                        "required": ["query"]
                    }
                },
                {
                    "name": "list_tables",
                    "description": "列出数据库中的所有表",
                    "inputSchema": {
                        "type": "object",
                        "properties": {}
                    }
                },
                {
                    "name": "describe_table",
                    "description": "描述表结构",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "table_name": {
                                "type": "string",
                                "description": "表名"
                            }
                        },
                        "required": ["table_name"]
                    }
                },
                {
                    "name": "analyze_database",
                    "description": "分析数据库结构",
                    "inputSchema": {
                        "type": "object",
                        "properties": {}
                    }
                }
            ]
        }
    
    async def handle_call_tool(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """调用工具"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        try:
            if tool_name == "list_tables":
                return await self._list_tables()
            elif tool_name == "describe_table":
                return await self._describe_table(arguments.get("table_name"))
            elif tool_name == "execute_query":
                return await self._execute_query(arguments.get("query"))
            elif tool_name == "analyze_database":
                return await self._analyze_database()
            else:
                raise ValueError(f"未知工具: {tool_name}")
        except Exception as e:
            logger.error(f"调用工具 {tool_name} 时出错: {str(e)}")
            return {
                "content": [{
                    "type": "text",
                    "text": f"错误: {str(e)}"
                }]
            }
    
    async def _list_tables(self) -> Dict[str, Any]:
        """列出所有表"""
        conn = self._get_connection()
        cursor = conn.cursor()
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        cursor.close()
        conn.close()
        
        table_list = [table[0] for table in tables]
        result = f"数据库 {self.db_config['database']} 中的表:\n"
        for i, table in enumerate(table_list, 1):
            result += f"{i}. {table}\n"
        
        return {
            "content": [{
                "type": "text",
                "text": result
            }]
        }
    
    async def _describe_table(self, table_name: str) -> Dict[str, Any]:
        """描述表结构"""
        if not table_name:
            raise ValueError("表名不能为空")
        
        conn = self._get_connection()
        cursor = conn.cursor()
        cursor.execute(f"DESCRIBE {table_name}")
        columns = cursor.fetchall()
        cursor.close()
        conn.close()
        
        result = f"表 {table_name} 的结构:\n\n"
        result += "字段名 | 类型 | 是否为空 | 键 | 默认值 | 额外\n"
        result += "-" * 60 + "\n"
        for col in columns:
            result += f"{col[0]} | {col[1]} | {col[2]} | {col[3]} | {col[4]} | {col[5]}\n"
        
        return {
            "content": [{
                "type": "text",
                "text": result
            }]
        }
    
    async def _execute_query(self, query: str) -> Dict[str, Any]:
        """执行SQL查询"""
        if not query:
            raise ValueError("查询语句不能为空")
        
        # 安全检查：只允许SELECT查询
        query_upper = query.strip().upper()
        if not query_upper.startswith('SELECT') and not query_upper.startswith('SHOW') and not query_upper.startswith('DESCRIBE'):
            raise ValueError("出于安全考虑，只允许执行SELECT、SHOW和DESCRIBE查询")
        
        conn = self._get_connection()
        cursor = conn.cursor()
        
        # 添加LIMIT以防止返回过多数据
        if query_upper.startswith('SELECT') and 'LIMIT' not in query_upper:
            query += " LIMIT 100"
        
        cursor.execute(query)
        
        if cursor.description:
            columns = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            
            result = f"查询结果 (共 {len(rows)} 行):\n\n"
            result += " | ".join(columns) + "\n"
            result += "-" * (len(" | ".join(columns))) + "\n"
            
            for row in rows:
                result += " | ".join(str(cell) for cell in row) + "\n"
        else:
            result = "查询执行完成，无返回结果"
        
        cursor.close()
        conn.close()
        
        return {
            "content": [{
                "type": "text",
                "text": result
            }]
        }
    
    async def _analyze_database(self) -> Dict[str, Any]:
        """分析数据库结构"""
        conn = self._get_connection()
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        result = f"# 数据库 {self.db_config['database']} 结构分析\n\n"
        result += f"## 基本信息\n"
        result += f"- 主机: {self.db_config['host']}:{self.db_config['port']}\n"
        result += f"- 数据库: {self.db_config['database']}\n"
        result += f"- 表数量: {len(tables)}\n\n"
        
        result += "## 表列表\n"
        for i, table in enumerate(tables, 1):
            table_name = table[0]
            
            # 获取表的行数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            
            # 获取表结构
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            
            result += f"{i}. **{table_name}** ({row_count} 行, {len(columns)} 字段)\n"
            for col in columns[:5]:  # 只显示前5个字段
                result += f"   - {col[0]} ({col[1]})\n"
            if len(columns) > 5:
                result += f"   - ... 还有 {len(columns) - 5} 个字段\n"
            result += "\n"
        
        cursor.close()
        conn.close()
        
        return {
            "content": [{
                "type": "text",
                "text": result
            }]
        }
    
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理MCP请求"""
        method = request.get("method")
        params = request.get("params", {})
        
        if method == "initialize":
            return await self.handle_initialize(params)
        elif method == "resources/list":
            return await self.handle_list_resources(params)
        elif method == "resources/read":
            return await self.handle_read_resource(params)
        elif method == "tools/list":
            return await self.handle_list_tools(params)
        elif method == "tools/call":
            return await self.handle_call_tool(params)
        else:
            raise ValueError(f"未知方法: {method}")
    
    async def run(self):
        """运行MCP服务器"""
        logger.info("MySQL真实数据库MCP服务器启动")
        
        while True:
            try:
                line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
                if not line:
                    break
                
                request = json.loads(line.strip())
                logger.info(f"收到请求: {request.get('method')}")
                
                try:
                    result = await self.handle_request(request)
                    response = {
                        "jsonrpc": "2.0",
                        "id": request.get("id"),
                        "result": result
                    }
                except Exception as e:
                    logger.error(f"处理请求时出错: {str(e)}")
                    response = {
                        "jsonrpc": "2.0",
                        "id": request.get("id"),
                        "error": {
                            "code": -1,
                            "message": str(e)
                        }
                    }
                
                print(json.dumps(response, ensure_ascii=False, default=str))
                sys.stdout.flush()
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {str(e)}")
            except Exception as e:
                logger.error(f"处理请求时出错: {str(e)}")

async def main():
    """主函数"""
    server = RealMySQLMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
