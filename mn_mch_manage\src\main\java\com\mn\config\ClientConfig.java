package com.mn.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetAddress;

@Component
public class ClientConfig {

    @Value("${spring.application.name}")
    public String application_name;

    @Value("${appid}")
    public String appId;

    @Value("${appsecret}")
    public String appSecret;

    @Value("${baseUrl}")
    public String baseUrl;

    @Value("${imgPath}")
    public String imgPath;

    @Value("${wqfNotifyUrl}")
    public String wqfNotifyUrl;

    @Value("${wqfRefundNotifyUrl}")
    public String wqfRefundNotifyUrl;

    @Value("${certPath}")
    public String certPath;

    public String redis_lock_value = application_name + InetAddress.getLocalHost().getHostAddress();

    public ClientConfig() throws IOException {

    }

}
