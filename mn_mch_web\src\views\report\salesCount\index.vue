<template>
  <div class="finance-report-container">
    <!-- 查询条件区域 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
        <el-form-item label="报表类型" prop="reportType">
          <el-select v-model="queryParams.reportType" placeholder="选择报表类型" @change="handleReportTypeChange">
            <el-option label="收款明细报表" value="payment-details"></el-option>
            <el-option label="应收账款报表" value="receivables"></el-option>
            <el-option label="月度财务汇总" value="monthly-summary"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="日期范围" prop="dateRange" v-if="queryParams.reportType !== 'monthly-summary'">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleDateChange">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="年份" prop="year" v-if="queryParams.reportType === 'monthly-summary'">
          <el-date-picker
            v-model="queryParams.year"
            type="year"
            placeholder="选择年份"
            value-format="yyyy"
            @change="handleQuery">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="客户" prop="partnerId" v-if="queryParams.reportType !== 'monthly-summary'">
          <el-select v-model="queryParams.partnerId" placeholder="选择客户" clearable filterable>
            <el-option
              v-for="partner in partnerList"
              :key="partner.partnerId"
              :label="partner.partnerName"
              :value="partner.partnerId">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="订单号" prop="orderNo" v-if="queryParams.reportType === 'payment-details'">
          <el-input v-model="queryParams.orderNo" placeholder="输入订单号" clearable></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button type="success" icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading">导出Excel</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 财务概览卡片 -->
    <el-row :gutter="20" class="overview-row" v-if="financeOverview">
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon" style="background-color: #409EFF;">
              <i class="el-icon-document"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">总订单数</div>
              <div class="overview-value">{{ financeOverview.totalOrders || 0 }}</div>
              <div class="overview-desc">已付款: {{ financeOverview.paidOrders || 0 }}笔</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon" style="background-color: #67C23A;">
              <i class="el-icon-money"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">总金额</div>
              <div class="overview-value">¥{{ formatAmountFromCents(financeOverview.totalAmount) }}</div>
              <div class="overview-desc">已收: ¥{{ formatAmountFromCents(financeOverview.paidAmount) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon" style="background-color: #E6A23C;">
              <i class="el-icon-warning"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">未收金额</div>
              <div class="overview-value">¥{{ formatAmountFromCents(financeOverview.unpaidAmount) }}</div>
              <div class="overview-desc">未付款: {{ financeOverview.unpaidOrders || 0 }}笔</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon" style="background-color: #F56C6C;">
              <i class="el-icon-pie-chart"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">收款率</div>
              <div class="overview-value">{{ financeOverview.collectionRate || 0 }}%</div>
              <div class="overview-desc">财务指标</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 报表数据表格 -->
    <el-card class="table-card" shadow="never">
      <div slot="header" class="table-header">
        <span>{{ getReportTitle() }}</span>
        <div>
          <el-button type="text" size="mini" @click="handleQuery" :loading="loading">
            <i class="el-icon-refresh"></i> 刷新数据
          </el-button>
        </div>
      </div>

      <!-- 收款明细报表 -->
      <el-table
        v-if="queryParams.reportType === 'payment-details'"
        :data="tableData"
        v-loading="loading"
        size="mini"
        stripe
        border
        height="500">
        <el-table-column prop="orderNo" label="订单号" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="partnerName" label="客户名称" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderDate" label="订单日期" width="100" align="center"></el-table-column>
        <el-table-column prop="orderAmount" label="订单金额" width="100" align="right">
          <template slot-scope="scope">
            ¥{{ formatAmountFromCents(scope.row.orderAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentMethod" label="支付方式" width="100" align="center"></el-table-column>
        <el-table-column prop="paymentStatus" label="支付状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)" size="mini">
              {{ scope.row.paymentStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="payTime" label="支付时间" width="150" align="center"></el-table-column>
        <el-table-column prop="paidAmount" label="实收金额" width="100" align="right">
          <template slot-scope="scope">
            <span v-if="scope.row.paidAmount">¥{{ formatAmountFromCents(scope.row.paidAmount) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="memo" label="备注" show-overflow-tooltip></el-table-column>
      </el-table>

      <!-- 应收账款报表 -->
      <el-table
        v-if="queryParams.reportType === 'receivables'"
        :data="tableData"
        v-loading="loading"
        size="mini"
        stripe
        border
        height="500">
        <el-table-column prop="partnerName" label="客户名称" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderNo" label="订单号" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderDate" label="订单日期" width="100" align="center"></el-table-column>
        <el-table-column prop="orderAmount" label="订单金额" width="100" align="right">
          <template slot-scope="scope">
            ¥{{ formatAmountFromCents(scope.row.orderAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="paidAmount" label="已收金额" width="100" align="right">
          <template slot-scope="scope">
            ¥{{ formatAmountFromCents(scope.row.paidAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="unpaidAmount" label="未收金额" width="100" align="right">
          <template slot-scope="scope">
            <span style="color: #F56C6C; font-weight: bold;">
              ¥{{ formatAmountFromCents(scope.row.unpaidAmount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" label="支付状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)" size="mini">
              {{ scope.row.paymentStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="overdueDays" label="逾期天数" width="80" align="center">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.overdueDays > 30 ? '#F56C6C' : '#909399' }">
              {{ scope.row.overdueDays }}天
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 月度财务汇总 -->
      <el-table
        v-if="queryParams.reportType === 'monthly-summary'"
        :data="tableData"
        v-loading="loading"
        size="mini"
        stripe
        border
        height="500">
        <el-table-column prop="monthLabel" label="月份" width="100" align="center"></el-table-column>
        <el-table-column prop="orderCount" label="订单数量" width="100" align="center"></el-table-column>
        <el-table-column prop="totalAmount" label="订单总额" width="120" align="right">
          <template slot-scope="scope">
            ¥{{ formatAmountFromCents(scope.row.totalAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="paidAmount" label="已收金额" width="120" align="right">
          <template slot-scope="scope">
            ¥{{ formatAmountFromCents(scope.row.paidAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="unpaidAmount" label="未收金额" width="120" align="right">
          <template slot-scope="scope">
            <span style="color: #F56C6C;">
              ¥{{ formatAmountFromCents(scope.row.unpaidAmount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="paidCount" label="已付款笔数" width="100" align="center"></el-table-column>
        <el-table-column prop="collectionRate" label="收款率" width="80" align="center">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.collectionRate >= 80 ? '#67C23A' : '#F56C6C' }">
              {{ scope.row.collectionRate }}%
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import { getFinanceReport, getPaymentDetails, getReceivables, getMonthlySummary, exportFinanceReport } from "@/api/sales/order";
import { listPartner } from "@/api/company/partner";

export default {
  name: "FinanceReport",
  data() {
    return {
      // 查询参数
      queryParams: {
        reportType: 'payment-details',
        startDate: null,
        endDate: null,
        year: null,
        partnerId: null,
        orderNo: null,
        pageNum: 1,
        pageSize: 10
      },
      // 日期范围
      dateRange: [],
      // 客户列表
      partnerList: [],
      // 财务概览数据
      financeOverview: null,
      // 表格数据
      tableData: [],
      // 总记录数
      total: 0,
      // 加载状态
      loading: false,
      // 导出加载状态
      exportLoading: false
    };
  },
  created() {
    this.initDefaultDate();
    this.loadPartnerList();
    this.getList();
  },
  methods: {
    /** 初始化默认日期 */
    initDefaultDate() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30); // 默认30天
      this.dateRange = [this.formatDate(start), this.formatDate(end)];
      this.queryParams.startDate = this.formatDate(start);
      this.queryParams.endDate = this.formatDate(end);
      this.queryParams.year = String(new Date().getFullYear());
    },

    /** 格式化日期 */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    /** 加载客户列表 */
    async loadPartnerList() {
      try {
        const response = await listPartner({
          pageNum: 1,
          pageSize: 1000 // 获取所有客户
        });
        if (response.code === 200) {
          this.partnerList = response.rows || [];
        }
      } catch (error) {
        console.error('加载客户列表失败:', error);
        this.partnerList = [];
      }
    },

    /** 报表类型变化 */
    handleReportTypeChange() {
      console.log('报表类型切换到:', this.queryParams.reportType);
      // 清空之前的数据
      this.tableData = [];
      this.total = 0;
      this.financeOverview = null;

      // 重新加载数据
      this.handleQuery();
    },

    /** 日期范围变化 */
    handleDateChange(val) {
      if (val) {
        this.queryParams.startDate = val[0];
        this.queryParams.endDate = val[1];
      } else {
        this.queryParams.startDate = null;
        this.queryParams.endDate = null;
      }
    },

    /** 查询 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置 */
    resetQuery() {
      this.resetForm("queryForm");
      this.initDefaultDate();
      this.queryParams.partnerId = null;
      this.queryParams.orderNo = null;
      this.handleQuery();
    },

    /** 获取数据 */
    async getList() {
      this.loading = true;
      try {
        await Promise.all([
          this.loadFinanceOverview(),
          this.loadTableData()
        ]);
      } catch (error) {
        console.error('获取数据失败:', error);
        this.$modal.msgError('获取数据失败');
      } finally {
        this.loading = false;
      }
    },

    /** 加载财务概览 */
    async loadFinanceOverview() {
      try {
        const response = await getFinanceReport({
          startDate: this.queryParams.startDate,
          endDate: this.queryParams.endDate,
          reportType: this.queryParams.reportType,
          partnerId: this.queryParams.partnerId
        });
        if (response.code === 200) {
          this.financeOverview = response.data;
        } else {
          console.warn('财务概览数据返回异常:', response);
          this.financeOverview = {
            totalOrders: 0,
            totalAmount: 0,
            paidAmount: 0,
            unpaidAmount: 0,
            paidOrders: 0,
            unpaidOrders: 0,
            collectionRate: 0
          };
        }
      } catch (error) {
        console.error('加载财务概览失败:', error);
        this.financeOverview = {
          totalOrders: 0,
          totalAmount: 0,
          paidAmount: 0,
          unpaidAmount: 0,
          paidOrders: 0,
          unpaidOrders: 0,
          collectionRate: 0
        };
      }
    },

    /** 加载表格数据 */
    async loadTableData() {
      try {
        let response;
        const params = {
          startDate: this.queryParams.startDate,
          endDate: this.queryParams.endDate,
          partnerId: this.queryParams.partnerId,
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize
        };

        switch (this.queryParams.reportType) {
          case 'receivables':
            response = await getReceivables(params);
            if (response.code === 200) {
              this.tableData = response.rows || [];
              this.total = response.total || 0;
            }
            break;
          case 'monthly-summary':
            response = await getMonthlySummary({ year: this.queryParams.year });
            if (response.code === 200) {
              // 月度汇总返回的是data数组，不是rows
              this.tableData = response.data || [];
              this.total = this.tableData.length;
            }
            break;
          default: // payment-details
            response = await getPaymentDetails({
              ...params,
              orderNo: this.queryParams.orderNo
            });
            if (response.code === 200) {
              this.tableData = response.rows || [];
              this.total = response.total || 0;
            }
            break;
        }

        if (response.code !== 200) {
          console.warn('表格数据返回异常:', response);
          this.tableData = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('加载表格数据失败:', error);
        this.$modal.msgError('加载数据失败: ' + (error.message || '网络错误'));
        this.tableData = [];
        this.total = 0;
      }
    },

    /** 导出Excel */
    async handleExport() {
      this.exportLoading = true;
      try {
        const params = {
          startDate: this.queryParams.startDate,
          endDate: this.queryParams.endDate,
          reportType: this.queryParams.reportType,
          partnerId: this.queryParams.partnerId
        };

        await exportFinanceReport(params);
        this.$modal.msgSuccess('导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        this.$modal.msgError('导出失败');
      } finally {
        this.exportLoading = false;
      }
    },

    /** 获取报表标题 */
    getReportTitle() {
      const titles = {
        'payment-details': '收款明细报表',
        'receivables': '应收账款报表',
        'monthly-summary': '月度财务汇总'
      };
      return titles[this.queryParams.reportType] || '财务报表';
    },

    /** 获取支付状态类型 */
    getPaymentStatusType(status) {
      const types = {
        '已付款': 'success',
        '未付款': 'danger',
        '付款中': 'warning'
      };
      return types[status] || 'info';
    },

    /** 格式化金额（从分转换为元） */
    formatAmountFromCents(amountInCents) {
      if (!amountInCents) return '0.00';
      const amountInYuan = parseFloat(amountInCents) / 100;
      return amountInYuan.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>