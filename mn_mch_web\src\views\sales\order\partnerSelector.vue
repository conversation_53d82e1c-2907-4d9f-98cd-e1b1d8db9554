<template>
  <div class="partner-selector">
    <el-dialog
      title="选择客户"
      :visible.sync="visible"
      width="70%"
      :close-on-click-modal="false"
      :before-close="handleClose"
      :modal-append-to-body="true"
      :append-to-body="true"
      :z-index="3200"
      :destroy-on-close="true"
      top="5vh"
    >
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="queryParams" size="small">
          <el-form-item label="客户名称">
            <el-input
              v-model="queryParams.partnerName"
              placeholder="客户名称"
              clearable
              style="width: 200px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="联系人">
            <el-input
              v-model="queryParams.contactPerson"
              placeholder="联系人"
              clearable
              style="width: 200px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            <el-button type="success" icon="el-icon-plus" @click="handleAddPartner">新增客户</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 客户列表 -->
      <el-table
        ref="table"
        v-loading="loading"
        :data="partnerList"
        border
        size="small"
        stripe
        highlight-current-row
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="客户名称" align="center" prop="partnerName" min-width="150" />
        <el-table-column label="客户编码" align="center" prop="partnerCode" min-width="120" />
        <el-table-column label="联系人" align="center" prop="contactPerson" min-width="100" />
        <el-table-column label="联系电话" align="center" prop="contactPhone" min-width="120" />
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="selectedPartner === null">
          确认选择
        </el-button>
      </div>
    </el-dialog>

    <!-- 新增客户对话框 -->
    <el-dialog
      title="新增客户"
      :visible.sync="addPartnerVisible"
      width="700px"
      append-to-body
      :close-on-click-modal="false"
      :z-index="3300"
    >
      <el-form ref="partnerForm" :model="partnerForm" :rules="partnerRules" label-width="100px">
        <!-- 基本信息卡片 -->
        <el-card class="box-card mb-20">
          <div slot="header" class="card-header">
            <span><i class="el-icon-document"></i> 基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户名称" prop="partnerName">
                <el-input v-model="partnerForm.partnerName" placeholder="请输入客户名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户编码" prop="partnerCode">
                <el-input v-model="partnerForm.partnerCode" placeholder="请输入客户编码" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系人" prop="contactPerson">
                <el-input v-model="partnerForm.contactPerson" placeholder="请输入联系人姓名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="partnerForm.contactPhone" placeholder="请输入联系电话" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系邮箱" prop="contactEmail">
                <el-input v-model="partnerForm.contactEmail" placeholder="请输入联系邮箱" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="partnerForm.status">
                  <el-radio :label="1">启用</el-radio>
                  <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 财务信息卡片 -->
        <el-card class="box-card mb-20">
          <div slot="header" class="card-header">
            <span><i class="el-icon-money"></i> 财务信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="税号" prop="taxNumber">
                <el-input v-model="partnerForm.taxNumber" placeholder="请输入税号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户银行" prop="bankName">
                <el-input v-model="partnerForm.bankName" placeholder="请输入开户银行" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="银行账号" prop="bankAccount">
                <el-input v-model="partnerForm.bankAccount" placeholder="请输入银行账号" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 地址信息卡片 -->
        <el-card class="box-card mb-20">
          <div slot="header" class="card-header">
            <span><i class="el-icon-location"></i> 地址信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="详细地址" prop="address">
                <el-input v-model="partnerForm.address" type="textarea" :rows="3" placeholder="请输入详细地址" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 备注信息卡片 -->
        <el-card class="box-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-notebook-2"></i> 备注信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="partnerForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddPartner">取 消</el-button>
        <el-button type="primary" @click="submitPartnerForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import { listPartner, addPartner } from "@/api/company/partner";

export default {
  name: "PartnerSelector",
  components: {
    Pagination
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中的客户
      selectedPartner: null,
      // 总条数
      total: 0,
      // 客户列表
      partnerList: [],
      // 新增客户对话框是否可见
      addPartnerVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        partnerName: undefined,
        contactPerson: undefined,
        status: 1 // 只显示启用的客户
      },
      // 新增客户表单
      partnerForm: {
        partnerId: undefined,
        partnerName: undefined,
        partnerCode: undefined,
        contactPerson: undefined,
        contactPhone: undefined,
        contactEmail: undefined,
        address: undefined,
        taxNumber: undefined,
        bankName: undefined,
        bankAccount: undefined,
        status: 1,
        remark: undefined
      },
      // 客户表单校验规则
      partnerRules: {
        partnerName: [
          { required: true, message: "请输入客户名称", trigger: "blur" },
          { min: 2, max: 100, message: "客户名称长度必须在2到100个字符之间", trigger: "blur" }
        ],
        status: [
          { required: true, message: "请选择状态", trigger: "change" }
        ]
      }
    };
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          console.log('客户选择器visible变为true', new Date().toISOString());
          setTimeout(() => {
            this.getList();
          }, 100);
        } else {
          console.log('客户选择器visible变为false', new Date().toISOString());
        }
      }
    }
  },
  methods: {
    // 获取客户列表
    async getList() {
      this.loading = true;
      try {
        const response = await listPartner(this.queryParams);
        this.partnerList = response.rows || [];
        this.total = response.total;
      } catch (error) {
        console.error("获取客户列表失败", error);
        this.$message.error("获取客户列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 处理表格选择变更
    handleSelectionChange(selection) {
      this.selectedPartner = selection.length > 0 ? selection[0] : null;
    },

    // 处理行点击
    handleRowClick(row) {
      this.$refs.table.toggleRowSelection(row);
    },

    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 重置按钮操作
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        partnerName: undefined,
        contactPerson: undefined,
        status: 1
      };
      this.handleQuery();
    },

    // 新增客户按钮操作
    handleAddPartner() {
      this.resetPartnerForm();
      this.addPartnerVisible = true;
    },

    // 重置客户表单
    resetPartnerForm() {
      this.partnerForm = {
        partnerId: undefined,
        partnerName: undefined,
        partnerCode: undefined,
        contactPerson: undefined,
        contactPhone: undefined,
        contactEmail: undefined,
        address: undefined,
        taxNumber: undefined,
        bankName: undefined,
        bankAccount: undefined,
        status: 1,
        remark: undefined
      };
      if (this.$refs.partnerForm) {
        this.$refs.partnerForm.resetFields();
      }
    },

    // 取消新增客户
    cancelAddPartner() {
      this.addPartnerVisible = false;
      this.resetPartnerForm();
    },

    // 提交客户表单
    submitPartnerForm() {
      this.$refs.partnerForm.validate(async (valid) => {
        if (valid) {
          try {
            const response = await addPartner(this.partnerForm);
            this.$message.success("新增客户成功");
            this.addPartnerVisible = false;
            
            // 刷新客户列表
            await this.getList();
            
            // 自动选择新增的客户
            const newPartner = this.partnerList.find(p => p.partnerName === this.partnerForm.partnerName);
            if (newPartner) {
              this.$refs.table.toggleRowSelection(newPartner, true);
              this.selectedPartner = newPartner;
            }
            
            this.resetPartnerForm();
          } catch (error) {
            console.error("新增客户失败", error);
            this.$message.error("新增客户失败");
          }
        }
      });
    },

    // 关闭对话框
    handleClose() {
      // 重置所有状态
      this.resetQuery();
      this.selectedPartner = null;
      this.partnerList = [];
      this.total = 0;

      // 延迟发送关闭事件，确保状态已完全重置
      setTimeout(() => {
        this.$emit('update:visible', false);
        console.log('客户选择器对话框已关闭', new Date().toISOString());
      }, 100);
    },

    // 确认选择
    handleConfirm() {
      if (!this.selectedPartner) {
        this.$message.warning("请选择一个客户");
        return;
      }

      // 发送确认事件
      this.$emit('confirm', this.selectedPartner);

      // 然后关闭对话框
      this.handleClose();
    }
  }
};
</script>

<style lang="scss" scoped>
.partner-selector {
  .search-area {
    margin-bottom: 15px;
  }

  ::v-deep .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 90%;
    max-width: 90%;

    .el-dialog__body {
      flex: 1;
      overflow: auto;
      padding: 10px 20px;
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
    }
  }

  ::v-deep .el-dialog__wrapper {
    overflow: hidden;
    z-index: 3200 !important;
  }

  ::v-deep .v-modal {
    z-index: 3199 !important;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    span {
      font-weight: 600;
      color: #303133;
    }
    
    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .box-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}
</style>
