package com.mn.controller;

import com.mn.entity.ProductInfo;
import com.mn.form.ProductInfoForm;
import com.mn.model.TableDataInfo;
import com.mn.service.IProductInfoService;
import com.mn.util.AjaxResult;
import com.mn.util.DateUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 商品信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-08
 */
@RestController
@RequestMapping("/product-info")
public class ProductInfoController extends BaseController {

    @Resource
    private IProductInfoService productInfoService;

    /**
     * 获取商品信息列表
     */
    @PreAuthorize("@ss.hasPermi('product:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductInfoForm form) {
        startPage();
        form.setDeptId(getDeptId());
        List<ProductInfo> list = productInfoService.selectProductInfoList(form);
        return getDataTable(list);
    }

    /**
     * 根据商品ID获取详细信息
     */
    //@PreAuthorize("@ss.hasPermi('product:info:query')")
    @GetMapping(value = "/{productId}")
    public AjaxResult getInfo(@PathVariable Long productId) {
        return success(productInfoService.getById(productId));
    }

    /**
     * 新增商品信息
     */
    //@PreAuthorize("@ss.hasPermi('product:info:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody ProductInfo productInfo) {
        productInfo.setCreateBy(getUsername());
        productInfo.setCreateTime(DateUtils.getNowDate());
        productInfo.setDeptId(getDeptId());
        return toAjax(productInfoService.insertProductInfo(productInfo));
    }

    /**
     * 修改商品信息
     */
    //@PreAuthorize("@ss.hasPermi('product:info:edit')")
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody ProductInfo productInfo) {
        productInfo.setUpdateBy(getUsername());
        productInfo.setUpdateTime(DateUtils.getNowDate());
        return toAjax(productInfoService.updateProductInfo(productInfo));
    }

    /**
     * 删除商品信息
     */
    //@PreAuthorize("@ss.hasPermi('product:info:remove')")
    @PostMapping("/del")
    public AjaxResult remove(Long productId) {
        return toAjax(productInfoService.deleteByProductId(productId));
    }
}
