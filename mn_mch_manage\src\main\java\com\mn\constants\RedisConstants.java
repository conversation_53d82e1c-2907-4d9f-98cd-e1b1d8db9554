package com.mn.constants;

public class RedisConstants {

    public static final String primary_key = "exam_primary_key";

    public static final String long_id = "long_id";

    public static final String short_id = "short_id";

    public static final Long token_time = 60L;

    public static final Long list_time = 30L;

    public static final String order_list = "order_list";

    public static final String redis_lock = "exam_redis_Lock:%s";

    public static final String order_lock = "order_Lock:%s";

    public static final Long redis_lock_time_out = 10L;//秒

    public static final Long LOCK_TRY_INTERVAL = 200L;//毫秒

    public static final String redis_lock_value = "exam";

    public static final Long client_time = 60L;

    public static final String mis_order_list = "exam_order_list";

    public static final String LOGIN_USER_KEY = "login_user_key";

    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    public static final String TOKEN = "token";

    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    public static final String course_access_token = "exam_access_token";

    public static final String course_access_ticket = "exam_access_ticket";

    public static final String app_tokenformat = "exam_TOKEN:%s";
}
