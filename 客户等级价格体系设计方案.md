# 客户等级价格体系设计方案

## 📋 需求分析

### 业务需求
1. **客户分等级**：不同等级的客户享受不同的价格优惠
2. **商品阶梯定价**：同一商品根据采购数量有不同价格
3. **灵活定价**：支持针对特定客户、特定商品的个性化定价

### 设计目标
- 支持多层级客户等级管理
- 支持商品数量阶梯定价
- 支持客户等级与数量阶梯的组合定价
- 便于维护和扩展
- 高性能的价格计算

## 🏗️ 数据库设计方案

### 1. 客户等级管理

#### 1.1 客户等级表 (customer_level)
```sql
CREATE TABLE customer_level (
    level_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '等级ID',
    level_code VARCHAR(32) NOT NULL UNIQUE COMMENT '等级编码',
    level_name VARCHAR(64) NOT NULL COMMENT '等级名称',
    level_order INT NOT NULL COMMENT '等级排序(数字越小等级越高)',
    discount_rate DECIMAL(5,4) DEFAULT 1.0000 COMMENT '默认折扣率(1.0000=无折扣)',
    min_order_amount BIGINT DEFAULT 0 COMMENT '最低订单金额(分)',
    description VARCHAR(255) COMMENT '等级描述',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by VARCHAR(64),
    update_by VARCHAR(64)
) COMMENT='客户等级表';
```

#### 1.2 修改 partner_info 表
```sql
-- 添加客户等级字段
ALTER TABLE partner_info
ADD COLUMN customer_level_id INT COMMENT '客户等级ID',
ADD INDEX idx_customer_level (customer_level_id);

-- 添加外键约束
ALTER TABLE partner_info
ADD CONSTRAINT fk_partner_customer_level
FOREIGN KEY (customer_level_id) REFERENCES customer_level(level_id);
```

### 2. 商品价格管理

#### 2.1 商品基础价格表 (product_base_price)
```sql
CREATE TABLE product_base_price (
    price_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '价格ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    sku_id BIGINT NOT NULL COMMENT 'SKU ID',
    base_price BIGINT NOT NULL COMMENT '基础价格(分)',
    cost_price BIGINT DEFAULT 0 COMMENT '成本价格(分)',
    market_price BIGINT DEFAULT 0 COMMENT '市场价格(分)',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expire_date DATE COMMENT '失效日期',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by VARCHAR(64),
    update_by VARCHAR(64),
    INDEX idx_product_sku (product_id, sku_id),
    INDEX idx_effective_date (effective_date),
    FOREIGN KEY (product_id) REFERENCES product_info(product_id),
    FOREIGN KEY (sku_id) REFERENCES product_sku(sku_id)
) COMMENT='商品基础价格表';
```

#### 2.2 客户等级价格表 (customer_level_price)
```sql
CREATE TABLE customer_level_price (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    level_id INT NOT NULL COMMENT '客户等级ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    sku_id BIGINT NOT NULL COMMENT 'SKU ID',
    price BIGINT NOT NULL COMMENT '等级价格(分)',
    discount_rate DECIMAL(5,4) COMMENT '折扣率',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expire_date DATE COMMENT '失效日期',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by VARCHAR(64),
    update_by VARCHAR(64),
    UNIQUE KEY uk_level_product_sku_date (level_id, product_id, sku_id, effective_date),
    INDEX idx_level_product (level_id, product_id),
    FOREIGN KEY (level_id) REFERENCES customer_level(level_id),
    FOREIGN KEY (product_id) REFERENCES product_info(product_id),
    FOREIGN KEY (sku_id) REFERENCES product_sku(sku_id)
) COMMENT='客户等级价格表';
```

#### 2.3 数量阶梯价格表 (quantity_tier_price)
```sql
CREATE TABLE quantity_tier_price (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    sku_id BIGINT NOT NULL COMMENT 'SKU ID',
    level_id INT COMMENT '客户等级ID(NULL表示适用所有等级)',
    min_quantity INT NOT NULL COMMENT '最小数量',
    max_quantity INT COMMENT '最大数量(NULL表示无上限)',
    tier_price BIGINT NOT NULL COMMENT '阶梯价格(分)',
    discount_rate DECIMAL(5,4) COMMENT '折扣率',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expire_date DATE COMMENT '失效日期',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by VARCHAR(64),
    update_by VARCHAR(64),
    INDEX idx_product_sku_level (product_id, sku_id, level_id),
    INDEX idx_quantity_range (min_quantity, max_quantity),
    FOREIGN KEY (product_id) REFERENCES product_info(product_id),
    FOREIGN KEY (sku_id) REFERENCES product_sku(sku_id),
    FOREIGN KEY (level_id) REFERENCES customer_level(level_id)
) COMMENT='数量阶梯价格表';
```

### 2.4 清理原有价格字段

由于新增了 `product_base_price` 表统一管理价格，需要清理原有表中的价格字段：

#### 清理 product_info 表的价格字段
```sql
-- 备份现有价格数据到新表后，删除原有价格字段
ALTER TABLE product_info
DROP COLUMN price,
DROP COLUMN market_price,
DROP COLUMN cost_price;
```

#### 清理 product_sku 表的价格字段
```sql
-- 备份现有价格数据到新表后，删除原有价格字段
ALTER TABLE product_sku
DROP COLUMN price,
DROP COLUMN market_price,
DROP COLUMN cost_price;
```

## 💡 价格计算逻辑

### 价格优先级（从高到低）
1. **数量阶梯价格** - 基于客户等级和采购数量的阶梯定价
2. **客户等级价格** - 基于客户等级的统一定价
3. **商品基础价格** - 商品的标准价格

### 价格计算算法
```
function calculatePrice(partnerId, productId, skuId, quantity, orderDate) {
    // 1. 获取客户等级
    customerLevel = getCustomerLevel(partnerId);

    // 2. 检查数量阶梯价格
    tierPrice = getQuantityTierPrice(productId, skuId, customerLevel.levelId, quantity, orderDate);
    if (tierPrice != null) {
        return tierPrice.price;
    }

    // 3. 检查客户等级价格
    levelPrice = getCustomerLevelPrice(customerLevel.levelId, productId, skuId, orderDate);
    if (levelPrice != null) {
        return levelPrice.price;
    }

    // 4. 使用基础价格 + 等级折扣
    basePrice = getBasePrice(productId, skuId, orderDate);
    return basePrice * customerLevel.discountRate;
}
```

## 📊 示例数据

### 客户等级示例
```sql
INSERT INTO customer_level (level_code, level_name, level_order, discount_rate, min_order_amount, description) VALUES
('VIP', 'VIP客户', 1, 0.8500, 100000, '年采购额100万以上的核心客户'),
('GOLD', '金牌客户', 2, 0.9000, 50000, '年采购额50万以上的重要客户'),
('SILVER', '银牌客户', 3, 0.9500, 20000, '年采购额20万以上的优质客户'),
('BRONZE', '铜牌客户', 4, 0.9800, 5000, '年采购额5万以上的普通客户'),
('NORMAL', '普通客户', 5, 1.0000, 0, '新客户或小额采购客户');
```

### 数量阶梯价格示例
```sql
-- 某商品的数量阶梯定价
INSERT INTO quantity_tier_price (product_id, sku_id, level_id, min_quantity, max_quantity, tier_price) VALUES
-- VIP客户阶梯价格
(1, 1, 1, 1, 99, 85000),    -- 1-99个：850元
(1, 1, 1, 100, 499, 80000), -- 100-499个：800元
(1, 1, 1, 500, NULL, 75000), -- 500个以上：750元

-- 金牌客户阶梯价格
(1, 1, 2, 1, 99, 90000),    -- 1-99个：900元
(1, 1, 2, 100, 499, 85000), -- 100-499个：850元
(1, 1, 2, 500, NULL, 80000); -- 500个以上：800元
```

## 🔧 实施建议

### 第一阶段：基础框架
1. 创建客户等级表和基础价格表
2. 修改partner_info表添加等级字段
3. 实现基础的等级价格计算

### 第二阶段：阶梯定价
1. 创建数量阶梯价格表
2. 实现数量阶梯价格计算逻辑
3. 完善价格计算API

### 第三阶段：高级功能
1. 创建客户专享价格表
2. 实现完整的价格优先级逻辑
3. 添加价格历史记录和审计功能

### 第四阶段：优化和扩展
1. 价格缓存机制
2. 批量价格计算优化
3. 价格变更通知机制

## 📁 相关文件

1. **数据库变更脚本.sql** - 完整的数据库结构变更脚本
2. **示例数据脚本.sql** - 演示数据和使用示例
3. **Java代码示例** - 应用层价格计算实现（即将创建）

## 🎯 实施步骤

### 立即执行
1. 执行 `数据库变更脚本.sql` 创建新的表结构
2. 执行 `示例数据脚本.sql` 导入示例数据
3. 测试价格计算功能

### 后续开发
1. 在Java应用中实现价格计算服务
2. 更新前端页面支持等级价格显示
3. 完善价格管理后台功能

## 💡 核心优势

- **灵活性**：支持多种定价策略组合
- **扩展性**：易于添加新的定价规则
- **性能**：通过索引和缓存优化查询性能
- **维护性**：清晰的表结构和业务逻辑分离

这个设计方案既满足了您的业务需求，又保持了良好的扩展性和维护性。您觉得这个方案如何？需要我详细说明某个部分吗？
