<template>
  <div class="app-container payment-slip-container">
    <el-card class="box-card">
      <div slot="header" class="card-header">
        <span><i class="el-icon-money header-icon"></i>销售订单收款单</span>
        <div class="header-buttons">
          <el-button icon="el-icon-back" size="small" @click="goBack">返回列表</el-button>
          <el-button type="primary" icon="el-icon-printer" size="small" @click="printPaymentSlip">打印收款单</el-button>
        </div>
      </div>

      <div class="payment-slip" ref="paymentSlip">
        <!-- 收款单标题和时间 -->
        <div class="payment-slip-header">
          <div class="header-left">
            {{ formatDateTime(new Date()) }}
          </div>
          <!-- 不添加header-right元素，避免显示重复标题 -->
        </div>

        <!-- 标题 -->
        <div class="main-title">
          <h2>销售订单收款单</h2>
        </div>

        <!-- 订单编号 -->
        <div class="order-no-section">
          <div class="order-no">订单编号：{{ orderInfo.orderNo }}</div>
        </div>

        <!-- 订单基本信息 -->
        <div class="info-section">
          <div class="section-title">订单基本信息</div>
          <div class="info-content">
            <div class="info-row">
              <div class="info-item">
                <span class="label">客户名称：</span>
                <span class="value">{{ orderInfo.partnerName || '' }}</span>
              </div>
              <div class="info-item">
                <span class="label">订单日期：</span>
                <span class="value">{{ formatDate(orderInfo.orderDate) }}</span>
              </div>
              <div class="info-item">
                <span class="label">订单状态：</span>
                <span class="value status-tag" :class="orderInfo.orderStatus === 1 ? 'approved' : 'pending'">
                  {{ orderInfo.orderStatus === 1 ? '已审核' : '未审核' }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 订单明细 -->
        <div class="info-section">
          <div class="section-title">订单明细</div>
          <table class="order-items-table">
            <thead>
              <tr>
                <th>商品名称</th>
                <th>商品编码</th>
                <th>规格</th>
                <th>数量</th>
                <th>总金额</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in orderItems" :key="index">
                <td>{{ item.productName }}</td>
                <td>{{ item.productCode }}</td>
                <td>{{ formatSpecData(item.specData) }}</td>
                <td>{{ item.quantity }}</td>
                <td>{{ formatAmount(item.amount) }}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="3" class="text-right">总计：</td>
                <td>{{ calculateTotalQuantity(orderItems) }}</td>
                <td>{{ formatAmount(calculateTotalAmount(orderItems)) }}</td>
              </tr>
            </tfoot>
          </table>
        </div>

        <!-- 收款信息 -->
        <div class="info-section">
          <div class="section-title">收款信息</div>
          <div class="payment-summary">
            <table class="payment-table">
              <tr>
                <td class="payment-label">订单金额：</td>
                <td class="payment-value">{{ formatAmount(orderInfo.totalAmount) }} 元</td>
                <td class="payment-label">优惠金额：</td>
                <td class="payment-value">{{ formatAmount(orderInfo.discountAmount) }} 元</td>
                <td class="payment-label">应付金额：</td>
                <td class="payment-value highlight">{{ formatAmount(orderInfo.actualAmount) }} 元</td>
              </tr>
            </table>
          </div>
        </div>

        <!-- 扫码支付 -->
        <div class="payment-section">
          <div class="section-title">扫码支付</div>
          <div class="qrcode-container">
            <div class="payment-instructions">
              <div class="instructions-title">支付说明</div>
              <ol class="instruction-list">
                <li>请使用微信或支付宝扫描右侧二维码进行支付</li>
                <li>支付金额: <span class="highlight">{{ formatAmount(orderInfo.actualAmount) }} 元</span></li>
                <li>订单编号: <span class="highlight">{{ orderInfo.orderNo }}</span></li>
              </ol>
            </div>
            <div class="qrcode-wrapper">
              <div ref="qrcode" class="qrcode"></div>
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="info-section" v-if="orderInfo.remark">
          <div class="section-title">备注信息</div>
          <div class="remark-content">{{ orderInfo.remark }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getSalesOrder } from "@/api/sales/order";
import { listSalesOrderItem } from "@/api/sales/orderItem";
import { parseTime } from "@/utils/ruoyi";
import { formatSpecData, formatAmount } from "@/views/sales/orderItem/utils";
import QRCode from 'qrcodejs2';

export default {
  name: "PaymentSlip",
  data() {
    return {
      // 订单ID
      orderId: null,
      // 订单编号
      orderNo: null,
      // 订单信息
      orderInfo: {},
      // 订单明细
      orderItems: [],
      // 二维码实例
      qrcode: null
    };
  },
  created() {
    // 获取路由参数
    this.orderId = this.$route.params.orderId;
    this.orderNo = this.$route.params.orderNo;

    if (!this.orderId || !this.orderNo) {
      this.$message.error("参数错误，无法生成收款单");
      this.goBack();
      return;
    }

    // 加载订单信息
    this.getOrderInfo();
  },
  mounted() {
    // 在DOM渲染完成后生成二维码
    this.$nextTick(() => {
      this.generateQRCode();
    });
  },
  methods: {
    // 格式化金额显示
    formatAmount,

    // 格式化规格数据
    formatSpecData,

    // 格式化日期
    formatDate(date) {
      return parseTime(date, '{y}-{m}-{d}');
    },

    // 格式化日期时间
    formatDateTime(date) {
      return parseTime(date);
    },

    // 返回列表页
    goBack() {
      this.$router.go(-1);
    },

    // 获取订单信息
    async getOrderInfo() {
      try {
        const response = await getSalesOrder(this.orderId);
        if (response.data) {
          this.orderInfo = response.data;
          // 不需要在这里转换金额，formatAmount函数会处理单位转换
          // 后端金额单位为分，formatAmount函数会将分转换为元并格式化

          // 加载订单明细
          this.getOrderItems();

          // 重新生成二维码
          this.$nextTick(() => {
            this.generateQRCode();
          });
        }
      } catch (error) {
        this.$message.error("获取订单信息失败");
        console.error("获取订单信息失败", error);
      }
    },

    // 获取订单明细
    async getOrderItems() {
      try {
        const response = await listSalesOrderItem({ orderId: this.orderId });
        if (response.rows && response.rows.length > 0) {
          // 直接使用原始数据，不进行转换
          // formatAmount函数会在显示时将分转换为元
          this.orderItems = response.rows;
        } else {
          this.orderItems = [];
        }
      } catch (error) {
        this.$message.error("获取订单明细失败");
        console.error("获取订单明细失败", error);
      }
    },

    // 计算明细总数量
    calculateTotalQuantity(items) {
      return items.reduce((sum, item) => {
        return sum + (parseFloat(item.quantity) || 0);
      }, 0).toFixed(2);
    },

    // 计算明细总金额
    calculateTotalAmount(items) {
      return items.reduce((sum, item) => {
        return sum + (item.amount || 0);
      }, 0);
    },

    // 生成二维码
    generateQRCode() {
      if (this.$refs.qrcode && this.orderNo) {
        // 清空二维码容器
        this.$refs.qrcode.innerHTML = '';

        // 生成支付链接
        const paymentUrl = `http://mall.meloon.cn/mchweb/webapi/#/terminal/detection?orderNo=${this.orderNo}`;

        // 创建二维码
        this.qrcode = new QRCode(this.$refs.qrcode, {
          text: paymentUrl,
          width: 180,
          height: 180,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H
        });
      }
    },

    // 打印收款单
    printPaymentSlip() {
      // 创建一个新的内容，只包含我们需要的部分
      const orderNo = this.orderInfo.orderNo || '';

      // 构建打印内容
      const printContent = `
        <div class="main-title">
          <h2>销售订单收款单</h2>
        </div>

        <div class="order-no-section">
          <div class="order-no">订单编号：${orderNo}</div>
        </div>

        <!-- 订单基本信息 -->
        <div class="info-section">
          <div class="section-title">订单基本信息</div>
          <div class="info-content">
            <div class="info-row">
              <div class="info-item">
                <span class="label">客户名称：</span>
                <span class="value">${this.orderInfo.partnerName || ''}</span>
              </div>
              <div class="info-item">
                <span class="label">订单日期：</span>
                <span class="value">${this.formatDate(this.orderInfo.orderDate)}</span>
              </div>
              <div class="info-item">
                <span class="label">订单状态：</span>
                <span class="value status-tag ${this.orderInfo.orderStatus === 1 ? 'approved' : 'pending'}">
                  ${this.orderInfo.orderStatus === 1 ? '已审核' : '未审核'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 订单明细 -->
        <div class="info-section">
          <div class="section-title">订单明细</div>
          <table class="order-items-table">
            <thead>
              <tr>
                <th>商品名称</th>
                <th>商品编码</th>
                <th>规格</th>
                <th>数量</th>
                <th>总金额</th>
              </tr>
            </thead>
            <tbody>
              ${this.orderItems.map(item => `
                <tr>
                  <td>${item.productName}</td>
                  <td>${item.productCode}</td>
                  <td>${this.formatSpecData(item.specData)}</td>
                  <td>${item.quantity}</td>
                  <td>${this.formatAmount(item.amount)}</td>
                </tr>
              `).join('')}
            </tbody>
            <tfoot>
              <tr>
                <td colspan="3" class="text-right">总计：</td>
                <td>${this.calculateTotalQuantity(this.orderItems)}</td>
                <td>${this.formatAmount(this.calculateTotalAmount(this.orderItems))}</td>
              </tr>
            </tfoot>
          </table>
        </div>

        <!-- 收款信息 -->
        <div class="info-section">
          <div class="section-title">收款信息</div>
          <div class="payment-summary">
            <table class="payment-table">
              <tr>
                <td class="payment-label">订单金额：</td>
                <td class="payment-value">${this.formatAmount(this.orderInfo.totalAmount)} 元</td>
                <td class="payment-label">优惠金额：</td>
                <td class="payment-value">${this.formatAmount(this.orderInfo.discountAmount)} 元</td>
                <td class="payment-label">应付金额：</td>
                <td class="payment-value highlight">${this.formatAmount(this.orderInfo.actualAmount)} 元</td>
              </tr>
            </table>
          </div>
        </div>

        <!-- 扫码支付 -->
        <div class="payment-section">
          <div class="section-title">扫码支付</div>
          <div class="qrcode-container">
            <div class="payment-instructions">
              <div class="instructions-title">支付说明</div>
              <ol class="instruction-list">
                <li>请使用微信或支付宝扫描右侧二维码进行支付</li>
                <li>支付金额: <span class="highlight">${this.formatAmount(this.orderInfo.actualAmount)} 元</span></li>
                <li>订单编号: <span class="highlight">${orderNo}</span></li>
              </ol>
            </div>
            <div class="qrcode-wrapper">
              <div class="qrcode">
                <img src="${this.getQRCodeDataURL()}" width="180" height="180" />
              </div>
            </div>
          </div>
        </div>

        ${this.orderInfo.remark ? `
        <!-- 备注信息 -->
        <div class="info-section">
          <div class="section-title">备注信息</div>
          <div class="remark-content">${this.orderInfo.remark}</div>
        </div>
        ` : ''}
      `;

      // 使用iframe方式打印，避免about:blank问题
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      document.body.appendChild(iframe);

      // 写入内容到iframe
      const doc = iframe.contentWindow.document;
      doc.open();
      doc.write(
        '<!DOCTYPE html>' +
        '<html>' +
        '<head>' +
        '<meta charset="utf-8">' +
        '<title></title>' + // 空标题，避免显示URL
        '<style>' +
        '/* 隐藏页眉页脚 */' +
        '@page { size: A4; margin: 10mm; }' +
        '@media print {' +
        '  @page { margin: 0; }' +
        '  body { margin: 10mm; }' +
        '  html, body { height: 99%; }' + // 避免出现页眉页脚
        '}' +
        'body { font-family: Arial, sans-serif; margin: 0; padding: 0; font-size: 12px; }' +
        '.payment-slip { max-width: 100%; padding: 10px; }' +
        '.main-title { text-align: center; margin-bottom: 10px; }' +
        '.main-title h2 { font-size: 18px; margin: 0; }' +
        '.order-no-section { text-align: center; margin-bottom: 15px; }' +
        '.order-no { font-weight: bold; }' +
        '.info-section { margin-bottom: 15px; border: 1px solid #ddd; border-radius: 4px; overflow: hidden; }' +
        '.section-title { background-color: #f5f7fa; padding: 8px 15px; font-weight: bold; border-bottom: 1px solid #ddd; }' +
        '.info-content { padding: 10px 15px; }' +
        '.info-row { display: flex; flex-wrap: wrap; }' +
        '.info-item { flex: 1; min-width: 200px; margin-bottom: 5px; display: flex; }' +
        '.label { font-weight: bold; min-width: 70px; }' +
        '.status-tag { padding: 2px 6px; border-radius: 3px; font-size: 12px; }' +
        '.status-tag.approved { background-color: #67C23A; color: #fff; }' +
        '.status-tag.pending { background-color: #E6A23C; color: #fff; }' +
        '.payment-summary { padding: 10px 15px; }' +
        '.payment-table { width: 100%; border-collapse: collapse; }' +
        '.payment-table td { padding: 8px; }' +
        '.payment-label { font-weight: bold; color: #606266; white-space: nowrap; }' +
        '.payment-value { color: #303133; padding-right: 20px; }' +
        '.payment-value.highlight { color: #F56C6C; font-weight: bold; }' +
        '.order-items-table { width: 100%; border-collapse: collapse; font-size: 12px; }' +
        '.order-items-table th, .order-items-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }' +
        '.order-items-table th { background-color: #f5f7fa; }' +
        '.order-items-table tfoot { font-weight: bold; }' +
        '.text-right { text-align: right; }' +
        '.payment-section { margin-bottom: 15px; border: 1px solid #ddd; border-radius: 4px; overflow: hidden; }' +
        '.qrcode-container { display: flex; overflow: hidden; }' +
        '.qrcode-wrapper { width: 220px; padding: 15px; text-align: center; border-left: 1px solid #ddd; }' +
        '.qrcode img { width: 180px; height: 180px; }' +
        '.qrcode-tip { margin-top: 10px; font-size: 12px; color: #666; }' +
        '.payment-instructions { flex: 1; }' +
        '.instructions-title { background-color: #f8f8f8; padding: 8px 15px; font-weight: bold; border-bottom: 1px solid #ddd; }' +
        '.instruction-list { padding: 10px 15px 10px 35px; margin: 0; }' +
        '.instruction-list li { margin-bottom: 8px; color: #666; }' +
        '.highlight { font-weight: bold; color: #F56C6C; }' +
        '.remark-content { padding: 10px 15px; line-height: 1.5; }' +
        '</style>' +
        '</head>' +
        '<body>' +
        '<div class="payment-slip">' +
        printContent +
        '</div>' +
        '<script>' +
        'window.onload = function() { window.print(); }' +
        '</' + 'script>' +
        '</body>' +
        '</html>'
      );
      doc.close();

      // 等待图片加载完成后打印
      iframe.onload = function() {
        try {
          // 确保没有页眉页脚
          const style = iframe.contentWindow.document.createElement('style');
          style.textContent = `
            @media print {
              @page { margin: 0; }
              body { margin: 10mm; }
              html, body { height: 99%; }
            }
          `;
          iframe.contentWindow.document.head.appendChild(style);

          // 设置打印选项 - 使用现代API
          const mediaQueryList = iframe.contentWindow.matchMedia('print');
          // 使用addEventListener代替addListener (现代API)
          mediaQueryList.addEventListener('change', function(e) {
            if (!e.matches) {
              // 打印完成后移除iframe
              setTimeout(function() {
                if (document.body.contains(iframe)) {
                  document.body.removeChild(iframe);
                }
              }, 500);
            }
          });

          iframe.contentWindow.focus();
          iframe.contentWindow.print();

          // 备用清理方法
          setTimeout(function() {
            if (document.body.contains(iframe)) {
              document.body.removeChild(iframe);
            }
          }, 2000);
        } catch (e) {
          console.error('打印失败', e);
          document.body.removeChild(iframe);
        }
      };
    },

    // 获取二维码图片的DataURL
    getQRCodeDataURL() {
      // 如果二维码已经生成，获取其图片
      if (this.$refs.qrcode && this.$refs.qrcode.querySelector('img')) {
        return this.$refs.qrcode.querySelector('img').src;
      }

      // 如果没有生成，返回空白图片
      return 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
    }
  }
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
