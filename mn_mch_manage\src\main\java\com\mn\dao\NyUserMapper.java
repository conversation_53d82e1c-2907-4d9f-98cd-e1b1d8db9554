package com.mn.dao;

import com.mn.entity.NyUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.form.UserForm;
import com.mn.model.NyUserRole;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
public interface NyUserMapper extends BaseMapper<NyUser> {

    NyUser selectUserByUserName(String userName);

    List<NyUser> selectUserList(UserForm form);

    int countByUserName(String userName);

    int batchUserRole(List<NyUserRole> userRoleList);

    int deleteUserRoleByUserId(Integer userId);

    /**
     * 获取商户管理员用户列表
     * @param userName 用户名（模糊搜索）
     * @return 用户列表
     */
    List<NyUser> selectMerchantAdminList(String userName);
}
