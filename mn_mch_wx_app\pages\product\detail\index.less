.product-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 140rpx; /* 增加底部间距，确保内容不被底部按钮遮挡 */
}

/* 商品图片轮播 */
.product-images-section {
  position: relative;
  background-color: #fff;
}

.product-swiper {
  width: 100%;
  height: 750rpx;
}

.product-image {
  width: 100%;
  height: 100%;
}

.no-image {
  width: 100%;
  height: 750rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  color: #999;
}

/* 商品基本信息 - 1688风格 */
.product-info-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 0; /* 去掉底部间距，让内容更紧凑 */
}

.product-price-box {
  margin-bottom: 24rpx;
}

.current-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.price-symbol {
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: 600;
  margin-right: 4rpx;
}

.price-value {
  font-size: 56rpx;
  color: #ff4d4f;
  font-weight: 600;
}

.price-range {
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: 600;
}

.price-label {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

/* 红包价样式已删除 */

.stock-info {
  font-size: 24rpx;
  color: #999;
}

.product-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 0; /* 去掉底部间距 */
}

/* 底部操作栏 - 优化版 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx; /* 减少高度 */
  background: #fff;
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx; /* 减少内边距 */
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e8e8e8;
  box-shadow: 0 -2rpx 12rpx rgba(0,0,0,0.06);
  z-index: 100;
}

.action-left {
  display: flex;
  align-items: center;
  gap: 32rpx; /* 减少间距 */
  margin-right: 32rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 68rpx; /* 减少宽度 */
  height: 68rpx; /* 减少高度 */
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  width: 36rpx; /* 减少图标大小 */
  height: 36rpx;
  margin-bottom: 6rpx;
  border-radius: 50%; /* 改为圆形 */
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-icon {
  background-color: #f5f5f5;
  border: 1rpx solid #e8e8e8;
}

.home-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-bottom: 10rpx solid #666;
}

.home-icon::after {
  content: '';
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 12rpx;
  height: 8rpx;
  background-color: #666;
}

.service-icon {
  background-color: #f5f5f5;
  border: 1rpx solid #e8e8e8;
}

.service-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 18rpx;
  height: 12rpx;
  border: 2rpx solid #1890ff;
  border-radius: 8rpx;
}

.service-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6rpx;
  height: 6rpx;
  background-color: #1890ff;
  border-radius: 50%;
}

.action-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 400;
}

.action-right {
  flex: 1;
  display: flex;
  gap: 16rpx; /* 减少间距 */
}

.action-btn {
  flex: 1;
  height: 68rpx; /* 减少高度 */
  line-height: 68rpx;
  border-radius: 34rpx; /* 更圆润的按钮 */
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx; /* 减少图标和文字间距 */
  transition: all 0.3s ease;
  border: none;
}

.action-btn:active {
  transform: scale(0.98);
}

.cart-btn {
  background: linear-gradient(135deg, #ffa726, #ff9800);
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(255, 167, 38, 0.3);
}

.cart-icon {
  width: 24rpx;
  height: 24rpx;
  position: relative;
}

.cart-icon::before {
  content: '';
  position: absolute;
  top: 6rpx;
  left: 2rpx;
  width: 16rpx;
  height: 10rpx;
  border: 2rpx solid #fff;
  border-radius: 2rpx 2rpx 0 0;
}

.cart-icon::after {
  content: '';
  position: absolute;
  bottom: 2rpx;
  right: 4rpx;
  width: 4rpx;
  height: 4rpx;
  background-color: #fff;
  border-radius: 50%;
}

.order-btn {
  background: linear-gradient(135deg, #ff5722, #e64a19);
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(255, 87, 34, 0.3);
}

/* 规格选择弹窗 - 1688风格 */
.sku-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
}

.sku-popup.show {
  visibility: visible;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: opacity 0.3s;
}

.sku-popup.show .popup-mask {
  opacity: 1;
}

.popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  max-height: 75vh;
  overflow-y: auto;
}

.sku-popup.show .popup-content {
  transform: translateY(0);
}

.popup-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.product-brief {
  display: flex;
  align-items: flex-start;
}

.brief-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  border: 1rpx solid #f0f0f0;
}

.brief-info {
  flex: 1;
}

.brief-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.brief-stock, .brief-selected {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.popup-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 40rpx;
  color: #999;
}

.popup-body {
  padding: 30rpx;
}

.sku-selection {
  margin-bottom: 40rpx;
}

.selection-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.sku-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.sku-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  min-height: 88rpx; /* 增加高度以适应新布局 */
}

.sku-option.selected {
  border-color: #ff5722;
  background-color: #fff5f5;
}

.sku-option-content {
  flex: 1;
}

.sku-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.sku-price-stock {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.sku-price {
  font-size: 26rpx;
  color: #ff4d4f;
  font-weight: 600;
}

.sku-stock {
  font-size: 22rpx;
  color: #999;
}

.sku-quantity {
  margin-left: 20rpx;
  flex-shrink: 0; /* 防止数量控制器被压缩 */
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #fff;
}

.quantity-btn {
  width: 56rpx; /* 稍微减小宽度 */
  height: 56rpx; /* 稍微减小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 600;
  background-color: #f8f9fa;
  color: #333;
  transition: all 0.3s ease;
}

.quantity-btn:active {
  background-color: #e9ecef;
}

.quantity-btn.disabled {
  color: #ccc;
  background-color: #f5f5f5;
}

.quantity-input {
  width: 72rpx; /* 稍微减小宽度 */
  height: 56rpx; /* 稍微减小高度 */
  text-align: center;
  font-size: 24rpx;
  font-weight: 500;
  border-left: 2rpx solid #f0f0f0;
  border-right: 2rpx solid #f0f0f0;
  background-color: #fff;
}

.order-summary {
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 28rpx;
  color: #666;
}

.summary-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.total-amount {
  color: #ff4d4f;
  font-size: 32rpx;
  font-weight: 600;
}

.popup-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(135deg, #ff5722, #e64a19);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 87, 34, 0.3);
}

