<template>
  <div class="order-item-container">
    <!-- 销售订单明细管理卡片 -->
    <el-card class="box-card mb-20">
      <div slot="header" class="card-header">
        <span><i class="el-icon-shopping-cart-full"></i> 销售订单明细管理</span>
        <div class="header-buttons">
          <el-button type="success" plain icon="el-icon-shopping-cart-2" size="mini" @click="openProductSelector">
            选择商品
          </el-button>
        </div>
      </div>

      <!-- 订单明细列表 -->
      <el-table
        v-loading="loading"
        :data="orderItemList"
        border
        size="small"
        stripe
        highlight-current-row
        class="mt-10"
        style="width: 100%"
      >
        <el-table-column label="商品名称" align="center" prop="productName" min-width="150">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.productName"
              placeholder="请输入商品名称"
              size="mini"
              :disabled="true"
            />
          </template>
        </el-table-column>

        <el-table-column label="商品编码" align="center" prop="productCode" min-width="120">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.productCode"
              placeholder="请输入商品编码"
              size="mini"
              :disabled="true"
            />
          </template>
        </el-table-column>

        <el-table-column label="SKU编码" align="center" prop="skuCode" min-width="120">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.skuId"
              filterable
              placeholder="请选择SKU"
              size="mini"
              @change="(val) => handleSkuChange(val, scope.row)"
            >
              <el-option
                v-for="item in scope.row.skuOptions || []"
                :key="item.skuId"
                :label="item.skuCode"
                :value="item.skuId"
              >
                <span style="float: left">{{ item?.skuCode }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ formatSpecData(item.specData) }}
                </span>
              </el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="规格" align="center" prop="specData" min-width="150">
          <template slot-scope="scope">
            <span>{{ formatSpecData(scope.row.specData) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="单位" align="center" width="80">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.unit"
              placeholder="单位"
              size="mini"
            />
          </template>
        </el-table-column>

        <el-table-column label="数量" align="center" width="100">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.quantity"
              :precision="2"
              :step="1"
              :min="0.01"
              controls-position="right"
              size="mini"
              @change="(val) => handleQuantityChange(scope.row, val)"
            />
          </template>
        </el-table-column>

        <el-table-column label="单价(元)" align="center" width="100">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.price"
              :precision="2"
              :step="0.01"
              :min="0"
              controls-position="right"
              size="mini"
              @change="(val) => handlePriceChange(scope.row, val)"
            />
          </template>
        </el-table-column>

        <el-table-column label="金额(元)" align="center" width="100">
          <template slot-scope="scope">
            <span>{{ formatAmount(scope.row.amount) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="60">
          <template slot-scope="scope">
            <el-button
              type="danger"
              icon="el-icon-delete"
              circle
              size="mini"
              @click="removeOrderItem(scope.$index)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 合计信息 -->
      <div class="total-info mt-10" v-if="orderItemList.length > 0">
        <el-divider content-position="left">合计信息</el-divider>
        <div class="total-row">
          <span class="label">总数量:</span>
          <span class="value">{{ totalQuantity }}</span>
          <span class="label ml-20">总金额:</span>
          <span class="value amount">{{ formatAmount(totalAmount) }}</span>
        </div>
      </div>
    </el-card>
    <!-- 商品选择器 -->
    <product-selector
      :visible.sync="productSelectorVisible"
      @confirm="handleProductsSelected"
    />
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import * as methods from "./methods";
import { formatSpecData, formatAmount } from "./utils";
import ProductSelector from "./productSelector.vue";

export default {
  name: "OrderItemEdit",
  components: {
    ProductSelector
  },
  props: {
    orderId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 商品加载状态
      productLoading: false,
      // 商品选项
      productOptions: [],
      // 选中的商品
      selectedProduct: null,
      // 商品表单
      productForm: {},
      // 订单明细列表
      orderItemList: [],
      // 上传头部
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // 批量设置
      batchSettings: {
        price: 0,
        quantity: 0
      },
      // 商品选择器是否可见
      productSelectorVisible: false
    };
  },
  computed: {
    // 总数量
    totalQuantity() {
      return this.orderItemList.reduce((sum, item) => {
        return sum + (parseFloat(item.quantity) || 0);
      }, 0).toFixed(2);
    },
    // 总金额
    totalAmount() {
      return this.orderItemList.reduce((sum, item) => {
        return sum + (item.amount || 0);
      }, 0);
    }
  },
  watch: {
    // 监听orderId变化
    orderId: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          // 获取新订单的明细列表
          this.getOrderItemList();
        }
      },
      immediate: true
    }
  },
  created() {
    // 初始化
  },
  methods: {
    ...methods,
    formatAmount,
    formatSpecData,

    // 打开商品选择器
    openProductSelector() {
      // 确保先设置为 false，然后在下一个事件循环中设置为 true
      // 这样可以确保对话框重新渲染
      this.productSelectorVisible = false;

      // 使用 nextTick 确保在 DOM 更新后再打开对话框
      this.$nextTick(() => {
        this.productSelectorVisible = true;
      });
    },

    // 处理商品选择确认
    handleProductsSelected(products) {
      if (!products || products.length === 0) return;

      // 将选中的商品添加到订单明细列表
      this.orderItemList = [...this.orderItemList, ...products];

      // 确保对话框关闭
      this.productSelectorVisible = false;

      // 显示成功消息
      this.$message.success(`成功添加 ${products.length} 个商品到订单明细`);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>