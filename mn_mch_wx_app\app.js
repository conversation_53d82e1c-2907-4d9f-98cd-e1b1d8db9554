// app.js
import config from './config';
import Mock from './mock/index';
//import createBus from './utils/eventBus';

if (config.isMock) {
  Mock();
}

App({
  onLaunch() {
    // 只在没有token时才获取token
    const token = wx.getStorageSync('token');
    if (!token) {
      this.checkAndGetToken().catch(err => {
        console.error('Initial token fetch failed:', err);
      });
    }
    
    const updateManager = wx.getUpdateManager();

    updateManager.onCheckForUpdate((res) => {
      // console.log(res.hasUpdate)
    });

    updateManager.onUpdateReady(() => {
      wx.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success(res) {
          if (res.confirm) {
            updateManager.applyUpdate();
          }
        },
      });
    });

  },
  globalData: {
    baseUrl: 'http://127.0.0.1:16060', // API基础地址
    //userInfo: null,
    //unreadNum: 0, // 未读消息数量
    //socket: null, // SocketTask 对象
  },

  checkAndGetToken() {
    return new Promise((resolve, reject) => {
      // 清除旧的token相关信息
      wx.removeStorageSync('token');
      wx.removeStorageSync('nickName');
      wx.removeStorageSync('avatar');

      // 登录获取code
      wx.login({
        success: (res) => {
          if (res.code) {
            // 通过code获取token
            const API = require('./api/request').default;
            API.getToken(res.code)
              .then(token => {
                if (!token) {
                  const error = new Error('获取token失败: 返回值为空');
                  console.error(error);
                  reject(error);
                  return;
                }
                resolve(token);
              })
              .catch(err => {
                console.error('获取token失败:', err);
                reject(err);
              });
          } else {
            const error = new Error('登录失败: ' + res.errMsg);
            console.error(error);
            reject(error);
          }
        },
        fail: (err) => {
          console.error('wx.login 调用失败:', err);
          reject(err);
        }
      });
    });
  },

});
