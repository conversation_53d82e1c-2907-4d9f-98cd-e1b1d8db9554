package com.mn.controller;

import com.mn.constants.RedisConstants;
import com.mn.entity.NyMenu;
import com.mn.entity.NyUser;
import com.mn.security.LoginBody;
import com.mn.security.service.LoginService;
import com.mn.security.service.SysPermissionService;
import com.mn.service.IMeloonUserService;
import com.mn.service.INyMenuService;
import com.mn.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Slf4j
public class LoginController {

    @Resource
    LoginService loginService;

    @Resource
    SysPermissionService permissionService;

    @Resource
    INyMenuService menuService;

    @Resource
    IMeloonUserService meloonUserService;

    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(RedisConstants.TOKEN, token);
        return ajax;
    }


    @GetMapping("/getToken")
    public MisRspModel getToken(String code, String distributorId) {
        log.info("获取token----code:" + code);
        if (StringUtils.isEmpty(code))
            throw new MeloonException("code不能为空");
        return new MisRspModel(meloonUserService.getToken(code));
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo() {
        NyUser user = SecurityUtils.getLoginUser().getUser();
        List<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("/getRouters")
    public AjaxResult getRouters() {
        Integer userId = SecurityUtils.getUserId();
        List<NyMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

}
