<t-toast id="t-toast" />

<view class="my-container">
  <!-- 顶部用户信息区域 -->
  <view class="user-header-section">
    <view class="user-info">
      <view class="user-avatar">
        <text class="avatar-text" wx:if="{{!isLoad}}">未</text>
        <image wx:if="{{isLoad}}" src="{{personalInfo.image}}" mode="aspectFill"></image>
      </view>
      <view class="user-details">
        <text class="user-name" wx:if="{{isLoad}}">{{personalInfo.name}}</text>
        <text class="user-name" wx:if="{{!isLoad}}">请先登录</text>
        <text class="user-subtitle" wx:if="{{isLoad}}">{{personalInfo.city}} · {{personalInfo.star}}</text>
        <text class="user-subtitle" wx:if="{{!isLoad}}" bindtap="onLogin">点击登录/注册</text>
      </view>
      <view class="edit-btn" wx:if="{{isLoad}}" bindtap="onNavigateTo">
        <view class="css-icon icon-edit"></view>
      </view>
    </view>
  </view>

  <!-- 功能菜单区域 -->
  <view class="menu-section">
    <view class="menu-list">
      <view class="menu-item" wx:for="{{settingList}}" wx:key="type" bindtap="onEleClick" data-data="{{item}}">
        <view class="menu-icon">
          <view class="css-icon {{item.iconClass}}"></view>
        </view>
        <view class="menu-content">
          <text class="menu-title">{{item.name}}</text>
        </view>
        <view class="menu-arrow">
          <view class="css-icon icon-arrow-right"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用统一的custom-tab-bar，删除旧的导航栏 -->
</view>
