package com.mn.dao;

import com.mn.entity.ProductSku;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.form.ProductSkuForm;

import java.util.List;

/**
 * <p>
 * 商品SKU表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface ProductSkuMapper extends BaseMapper<ProductSku> {

    List<ProductSku> selectSkuList(ProductSkuForm form);
    
    List<ProductSku> selectSkuByCode(String skuCode);
    
    List<ProductSku> selectSkuByProductId(Long productId);

}
