import Message from 'tdesign-miniprogram/message/index';
import API from '../../../api/request';

Page({
  data: {
    orderId: '',
    orderDetail: {},
    goodsList: [],
    countDown: '',
    timer: null,
    createTimeFormatted: ''
  },

  onLoad: function(options) {
    if (options.orderId) {
      this.setData({
        orderId: options.orderId
      });
      this.getOrderDetail(options.orderId); // 假设有这个方法获取订单详情
    }
  },

  onUnload: function() {
    // 清除定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  // 获取订单详情
  getOrderDetail: function(orderId) {
    if (!orderId) {
      console.error('订单ID为空，无法获取订单详情');
      wx.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      return;
    }

    console.log('开始获取订单详情，订单ID:', orderId);
    wx.showLoading({
      title: '加载中...',
    });

    API.getOrderDetail(orderId).then(res => {
      console.log('获取订单详情成功:', res);

      // API模块返回的是完整响应：{code: 0, data: {...}, msg: "成功"}
      if (res && res.code === 0 && res.data) {
        const orderData = res.data;

        // 解析商品信息JSON
        let goodsList = [];
        if (orderData.goodsInfo) {
          try {
            goodsList = JSON.parse(orderData.goodsInfo);
          } catch (e) {
            console.error('解析商品信息失败', e);
            // 如果解析失败，尝试使用goodsList字段
            if (orderData.goodsList && Array.isArray(orderData.goodsList)) {
              goodsList = orderData.goodsList;
            }
          }
        } else if (orderData.goodsList && Array.isArray(orderData.goodsList)) {
          // 直接使用goodsList字段
          goodsList = orderData.goodsList;
        }

        // 格式化创建时间
        const createTimeFormatted = this.formatTimestamp(orderData.createTime);

        this.setData({
          orderDetail: orderData,
          goodsList: goodsList,
          createTimeFormatted: createTimeFormatted
        });

        // 如果订单状态是处理中，开始倒计时
        if (orderData.status === 'PROCESSING' && orderData.expireTime) {
          this.startCountDown(new Date(orderData.expireTime));
        }
      } else {
        console.error('订单详情数据为空:', res);
        wx.showToast({
          title: res?.msg || '获取订单详情失败',
          icon: 'none'
        });
      }
      wx.hideLoading();
    }).catch(err => {
      console.error('获取订单详情失败', err);
      Message.error({
        context: this,
        offset: [20, 32],
        content: '获取订单详情失败: ' + (err.message || '网络错误')
      });
      wx.hideLoading();
    });
  },

  // 格式化时间戳
  formatTimestamp: function(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    const second = date.getSeconds().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  },

  // 开始倒计时
  startCountDown: function(expireTime) {
    // 清除之前的定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }

    const calculateTimeLeft = () => {
      const now = new Date();
      const diff = expireTime - now;

      if (diff <= 0) {
        clearInterval(this.data.timer);
        this.setData({
          countDown: '订单已过期',
          'orderDetail.status': 'CLOSED'
        });
        return;
      }

      const minutes = Math.floor(diff / 1000 / 60);
      const seconds = Math.floor((diff / 1000) % 60);
      
      this.setData({
        countDown: `${minutes}分${seconds}秒`
      });
    };

    // 立即执行一次
    calculateTimeLeft();

    // 设置定时器，每秒更新一次
    this.setData({
      timer: setInterval(calculateTimeLeft, 1000)
    });
  },

  // 支付订单
  payOrder: function() {
    wx.showLoading({
      title: '发起支付...',
    });

    API.payOrder(this.data.orderId).then(res => {
      wx.hideLoading();
      console.log('支付接口返回:', res);

      // API模块返回的是完整响应：{code: 0, data: {...}, msg: "成功"}
      if (res && res.code === 0 && res.data && res.data.miniProgram) {
        const mpInfo = res.data.miniProgram;

        console.log('准备跳转小程序', mpInfo);

        // 跳转到其他小程序
        wx.navigateToMiniProgram({
          appId: mpInfo.mpAppid,
          path: mpInfo.mpPath,
          extraData: {},
          envVersion: 'trial', // 根据mpVersion设置环境版本
          success: (result) => {
            console.log('跳转成功', result);
            // 跳转成功后，可以定时查询订单状态
            this.checkOrderStatus();
          },
          fail: (err) => {
            console.error('跳转失败', err);
            wx.showToast({
              title: '跳转失败: ' + (err.errMsg || '未知错误'),
              icon: 'none',
              duration: 3000
            });
          }
        });
      } else {
        console.error('支付数据格式不正确', res);
        wx.showToast({
          title: res?.msg || '获取支付信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('支付失败', err);
      Message.error({
        context: this,
        offset: [20, 32],
        content: '支付失败: ' + (err.message || '未知错误')
      });
    });
  },
  
  // 检查订单状态
  checkOrderStatus: function() {
    // 设置一个定时器，每隔几秒查询一次订单状态
    let checkCount = 0;
    const maxChecks = 10; // 最多查询10次
    
    const statusTimer = setInterval(() => {
      checkCount++;
      if (checkCount > maxChecks) {
        clearInterval(statusTimer);
        return;
      }
      
      API.getOrderDetail(this.data.orderId).then(res => {
        console.log('查询订单状态结果:', res);

        // API模块返回的是完整响应：{code: 0, data: {...}, msg: "成功"}
        if (res && res.code === 0 && res.data) {
          // 更新订单详情
          this.setData({
            orderDetail: res.data
          });

          if (res.data.status === 'SUCCEEDED') {
            // 订单支付成功
            clearInterval(statusTimer);

            // 清除倒计时
            if (this.data.timer) {
              clearInterval(this.data.timer);
            }

            wx.showToast({
              title: '支付成功',
              icon: 'success',
              duration: 2000
            });
          }
        }
      }).catch(err => {
        console.error('查询订单状态失败', err);
      });
    }, 3000); // 每3秒查询一次
  },
  // 添加 onShow 生命周期函数
  onShow() {
    // 如果页面有订单ID，则重新获取订单详情
    if (this.data.orderId) {
      console.log('页面显示，重新获取订单详情，订单ID:', this.data.orderId);
      this.getOrderDetail(this.data.orderId);
    }
  },
});