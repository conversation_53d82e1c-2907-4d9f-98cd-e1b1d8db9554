<template>
  <div class="sku-manager">
    <!-- 规格选择区域 -->
    <el-card class="spec-selection-card" v-if="productSpecs.length > 0">
      <div slot="header" class="card-header">
        <span><i class="el-icon-collection-tag"></i> 商品规格选择</span>
      </div>

      <el-alert
        title="选择商品规格，系统将自动生成规格组合SKU"
        type="info"
        :closable="false"
        show-icon
        class="mb-15"
      />

      <el-form label-width="80px">
        <el-form-item label="选择规格">
          <el-select
            v-model="selectedSpecs"
            multiple
            collapse-tags
            placeholder="请选择规格"
            @change="handleSpecChange"
            style="width: 100%"
          >
            <el-option
              v-for="spec in productSpecs"
              :key="spec.specId"
              :label="spec.specName"
              :value="spec.specId"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <div v-if="currentSelectedSpecs.length > 0" class="spec-preview">
        <el-tag v-for="specId in currentSelectedSpecs" :key="specId" class="spec-tag">
          {{ getSpecName(specId) }}
        </el-tag>
        <el-button
          type="primary"
          size="small"
          @click="generateSkuCombinations"
          :loading="generating"
          class="ml-10"
        >
          <i class="el-icon-magic-stick"></i> 生成SKU组合
        </el-button>
      </div>
    </el-card>

    <!-- SKU管理区域 -->
    <el-card class="sku-list-card">
      <div slot="header" class="card-header">
        <span><i class="el-icon-shopping-cart-full"></i> SKU列表管理</span>
        <div class="header-buttons">
          <el-button
            type="success"
            size="small"
            @click="generateSkuCombinations"
            :disabled="currentSelectedSpecs.length === 0"
            :loading="generating"
          >
            <i class="el-icon-magic-stick"></i> 生成SKU组合
          </el-button>
          <el-button type="primary" size="small" @click="handleAdd" :disabled="currentSelectedSpecs.length === 0">
            <i class="el-icon-plus"></i> 手动添加SKU
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="skuList"
        border
        stripe
        highlight-current-row
        size="small"
        style="width: 100%"
      >
        <el-table-column label="SKU编码" width="150" align="center">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.skuCode"
              placeholder="请输入SKU编码"
              size="mini"
              @blur="checkSkuCode(scope.row)"
              @change="() => markSkuCodeAsEdited(scope.row)"
            />
            <div v-if="scope.row.codeCheckResult !== null" class="code-check-result">
              <i :class="scope.row.codeCheckResult ? 'el-icon-success' : 'el-icon-error'"
                 :style="{ color: scope.row.codeCheckResult ? '#67c23a' : '#f56c6c' }"></i>
              <span :style="{ color: scope.row.codeCheckResult ? '#67c23a' : '#f56c6c' }">
                {{ scope.row.codeCheckResult ? '编码可用' : '编码已存在' }}
              </span>
            </div>
          </template>
        </el-table-column>

        <!-- 规格值列 - 动态生成 -->
        <el-table-column
          v-for="(spec, index) in specColumns"
          :key="index"
          :label="spec.specName"
          align="center"
          min-width="120"
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.specDataObj[index].value"
              size="mini"
              placeholder="请输入规格值"
              @change="handleSpecValueChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="库存" width="100" align="center">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.stock"
              :min="0"
              controls-position="right"
              size="mini"
              style="width: 100%"
            />
          </template>
        </el-table-column>

        <el-table-column label="SKU图片" width="100" align="center">
          <template slot-scope="scope">
            <el-upload
              class="avatar-uploader"
              :action="uploadImgUrl"
              :headers="headers"
              :show-file-list="false"
              :on-success="(res) => handleImageSuccess(res, scope.row)"
            >
              <img v-if="scope.row.image" :src="scope.row.image" class="avatar" alt="SKU图片">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="80" align="center">
          <template slot-scope="scope">
            <el-button
              type="danger"
              icon="el-icon-delete"
              circle
              size="mini"
              @click="handleDelete(scope.$index)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量设置 -->
      <div class="batch-setting" v-if="skuList.length > 0">
        <el-divider content-position="left">批量设置</el-divider>
        <el-form :inline="true" class="batch-form" size="mini">
          <el-form-item label="库存">
            <el-input-number
              v-model="batchSettings.stock"
              :min="0"
              controls-position="right"
              size="mini"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" @click="applyBatchSettings">应用到所有SKU</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 空状态 -->
      <div v-if="skuList.length === 0" class="empty-state">
        <i class="el-icon-goods"></i>
        <p v-if="currentSelectedSpecs.length === 0">请先选择商品规格</p>
        <p v-else>暂无SKU信息，请点击"生成SKU组合"或"手动添加SKU"</p>
      </div>
    </el-card>
  </div>
</template>

<script>
import { checkSkuV2Code } from "@/api/product/v2";
import { getSpec } from "@/api/product/spec";
import { getToken } from "@/utils/auth";

export default {
  name: "SkuManager",
  props: {
    value: {
      type: Array,
      default: () => []
    },
    productSpecs: {
      type: Array,
      default: () => []
    },
    productCode: {
      type: String,
      default: ""
    },
    // 选中的规格ID数组（从父组件传入）
    selectedSpecs: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      generating: false,
      // SKU列表
      skuList: [],
      // 内部选中的规格（用于内部状态管理）
      internalSelectedSpecs: [],
      // 规格列
      specColumns: [],
      // SKU计数器
      skuCounter: 1,
      // 上传图片地址
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      // 上传头部
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // 批量设置
      batchSettings: {
        stock: 0
      }
    };
  },
  computed: {
    /** 当前有效的选中规格 */
    currentSelectedSpecs() {
      // 优先使用从父组件传入的selectedSpecs，如果没有则使用内部状态
      return this.selectedSpecs && this.selectedSpecs.length > 0
        ? this.selectedSpecs
        : this.internalSelectedSpecs;
    }
  },
  watch: {
    value: {
      handler(val) {
        this.skuList = val.map(item => {
          // 解析规格数据
          let specDataObj = {};
          if (item.specData) {
            try {
              specDataObj = JSON.parse(item.specData);
            } catch (e) {
              console.error("解析规格数据失败", e);
            }
          }

          return {
            ...item,
            specDataObj: specDataObj,
            codeCheckResult: null
          };
        });

        // 提取规格列
        this.extractSpecColumns();
      },
      immediate: true,
      deep: true
    },
    selectedSpecs: {
      handler(val) {
        console.log('SkuManager 接收到规格变化:', JSON.stringify(val));
        if (val && val.length > 0) {
          this.internalSelectedSpecs = [...val];
          this.updateSpecColumns();
          console.log('更新后的规格列:', JSON.stringify(this.specColumns));
        } else {
          this.internalSelectedSpecs = [];
          this.specColumns = [];
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /** 提取规格列 */
    extractSpecColumns() {
      if (!this.skuList || this.skuList.length === 0) {
        this.specColumns = [];
        return;
      }

      // 从第一个SKU中提取规格信息
      const firstSku = this.skuList[0];
      if (!firstSku.specDataObj) {
        return;
      }

      const specs = [];
      // 如果specDataObj是数组形式
      if (Array.isArray(firstSku.specDataObj)) {
        firstSku.specDataObj.forEach(spec => {
          if (spec.specId && spec.specName) {
            specs.push({
              specId: spec.specId,
              specName: spec.specName
            });
          }
        });
      }

      this.specColumns = specs;

      // 更新已选规格
      if (this.currentSelectedSpecs.length === 0) {
        this.internalSelectedSpecs = specs.map(spec => spec.specId);
      }
    },

    /** 更新规格列（基于传入的selectedSpecs） */
    updateSpecColumns() {
      if (!this.currentSelectedSpecs || this.currentSelectedSpecs.length === 0) {
        this.specColumns = [];
        return;
      }

      this.specColumns = this.currentSelectedSpecs.map(specId => ({
        specId: specId,
        specName: this.getSpecName(specId)
      }));
    },
    /** 处理规格变化 */
    async handleSpecChange(specIds) {
      if (!specIds || specIds.length === 0) {
        this.skuList = [];
        this.specColumns = [];
        this.emitSpecChange();
        this.emitChange();
        return;
      }

      // 更新规格列
      this.specColumns = specIds.map(specId => ({
        specId: specId,
        specName: this.getSpecName(specId)
      }));

      this.emitSpecChange();
    },

    /** 生成SKU组合 */
    async generateSkuCombinations() {
      if (this.currentSelectedSpecs.length === 0) {
        this.$message.warning("请先选择规格");
        return;
      }

      this.generating = true;
      try {
        // 获取选中的规格详情
        const promises = this.currentSelectedSpecs.map(specId => getSpec(specId));
        const responses = await Promise.all(promises);

        // 收集所有规格值
        const specList = responses.map(response => {
          const specData = response.data;
          return {
            specId: specData.specId,
            specName: specData.specName,
            specValues: JSON.parse(specData.specValues || '[]')
          };
        });

        // 保存当前已有的SKU数据
        const existingSkus = [...this.skuList];

        // 生成新的规格组合
        this.generateCombinations(specList, existingSkus);

        this.$message.success(`成功生成 ${this.skuList.length} 个SKU组合`);
      } catch (error) {
        console.error("获取规格详情失败", error);
        this.$message.error("获取规格详情失败");
      } finally {
        this.generating = false;
      }
    },

    /** 生成SKU组合 */
    generateCombinations(specList, existingSkus = []) {
      // 创建一个映射来存储已有的SKU
      const existingSkuMap = new Map();

      // 处理已有的SKU
      existingSkus.forEach(sku => {
        if (sku.specDataObj) {
          const specKey = this.createSpecKey(sku.specDataObj);
          existingSkuMap.set(specKey, sku);
        }
      });

      // 生成新的SKU列表
      const newSkuList = [];

      const generateCombinations = (specs, current = [], index = 0) => {
        if (index === specs.length) {
          // 创建规格组合
          const specDataObj = current.map(item => ({
            specId: item.specId,
            specName: item.specName,
            value: item.value
          }));

          const specKey = this.createSpecKey(specDataObj);

          // 检查是否已存在相同规格组合的SKU
          if (existingSkuMap.has(specKey)) {
            // 使用已有的SKU
            const existingSku = existingSkuMap.get(specKey);
            newSkuList.push(existingSku);
            existingSkuMap.delete(specKey);
          } else {
            // 创建新的SKU
            const newSku = {
              skuId: null,
              skuCode: this.generateIntelligentSkuCode(specDataObj),
              stock: 0,
              image: '',
              status: true,
              specDataObj: specDataObj,
              codeCheckResult: null
            };

            newSkuList.push(newSku);
          }
          return;
        }

        const currentSpec = specs[index];
        currentSpec.specValues.forEach(value => {
          generateCombinations(specs, [...current, {
            specId: currentSpec.specId,
            specName: currentSpec.specName,
            value: value
          }], index + 1);
        });
      };

      generateCombinations(specList);

      // 更新SKU列表
      this.skuList = newSkuList;
      this.emitChange();
    },
    /** 创建规格组合的唯一标识 */
    createSpecKey(specDataObj) {
      if (Array.isArray(specDataObj)) {
        // 如果是数组形式，按规格ID和值排序后拼接
        return specDataObj
          .sort((a, b) => a.specId - b.specId)
          .map(spec => `${spec.specId}:${spec.value}`)
          .join('|');
      } else {
        // 如果是对象形式，按键排序后拼接
        return Object.keys(specDataObj)
          .sort()
          .map(key => `${key}:${specDataObj[key]}`)
          .join('|');
      }
    },

    /** 手动添加SKU */
    handleAdd() {
      if (this.currentSelectedSpecs.length === 0) {
        this.$message.warning("请先选择规格");
        return;
      }

      // 创建一个新的SKU对象
      const specDataObj = [];

      // 为每个规格添加默认值
      this.specColumns.forEach(spec => {
        specDataObj.push({
          specId: spec.specId,
          specName: spec.specName,
          value: ''  // 默认为空，用户需要手动填写
        });
      });

      const newSku = {
        skuId: null,
        skuCode: this.generateIntelligentSkuCode(specDataObj),
        stock: 0,
        image: '',
        status: true,
        specDataObj: specDataObj,
        codeCheckResult: null
      };

      this.skuList.push(newSku);
      this.emitChange();
    },

    /** 删除SKU */
    handleDelete(index) {
      this.$confirm('确定要删除该SKU吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.skuList.splice(index, 1);
        this.emitChange();
        this.$message.success('删除成功');
      }).catch(() => {
        // 用户取消删除
      });
    },

    /** 处理规格值变更 */
    handleSpecValueChange(sku) {
      // 如果SKU编码是自动生成的（没有手动修改过），则更新SKU编码
      if (!sku.skuCodeManuallyEdited) {
        sku.skuCode = this.generateIntelligentSkuCode(sku.specDataObj);
      }
      this.emitChange();
    },

    /** 标记SKU编码为手动编辑过 */
    markSkuCodeAsEdited(sku) {
      sku.skuCodeManuallyEdited = true;
    },
    /** 检查SKU编码 */
    checkSkuCode(sku) {
      if (!sku.skuCode) {
        sku.codeCheckResult = null;
        return;
      }

      checkSkuV2Code(sku.skuCode, sku.skuId).then(response => {
        sku.codeCheckResult = response.data;
      }).catch(() => {
        sku.codeCheckResult = false;
      });
    },

    /** 处理图片上传成功 */
    handleImageSuccess(res, sku) {
      if (res.code === 200) {
        sku.image = res.data.url;
        this.emitChange();
      } else {
        this.$message.error("上传失败");
      }
    },

    /** 应用批量设置 */
    applyBatchSettings() {
      if (this.skuList.length === 0) {
        return;
      }

      this.skuList.forEach(sku => {
        if (this.batchSettings.stock > 0) {
          sku.stock = this.batchSettings.stock;
        }
      });

      this.$message.success("批量设置已应用");
      this.emitChange();
    },

    /** 获取规格名称 */
    getSpecName(specId) {
      const spec = this.productSpecs.find(item => item.specId === specId);
      return spec ? spec.specName : `规格${specId}`;
    },

    /** 生成智能SKU编码 */
    generateIntelligentSkuCode(specDataObj) {
      // 如果没有商品编码，使用时间戳
      if (!this.productCode) {
        return `SKU${Date.now().toString().slice(-6)}`;
      }

      // 基础前缀：商品编码
      let skuCode = this.productCode;

      // 如果规格数据为空，直接返回商品编码加序号
      if (!specDataObj || specDataObj.length === 0) {
        return `${skuCode}-${this.skuList.length + 1}`;
      }

      // 从规格值中提取信息
      const specCodes = [];

      specDataObj.forEach(spec => {
        if (spec.value) {
          // 尝试提取规格值的首字母或首个字符
          let specCode = '';

          // 如果是中文，取第一个字符
          if (/[\u4e00-\u9fa5]/.test(spec.value)) {
            specCode = spec.value.charAt(0);
          }
          // 如果是英文，取首字母并大写
          else if (/[a-zA-Z]/.test(spec.value)) {
            // 提取所有单词的首字母
            specCode = spec.value
              .split(/\s+/)
              .map(word => word.charAt(0).toUpperCase())
              .join('');
          }
          // 如果是数字，直接使用
          else if (/^\d+$/.test(spec.value)) {
            specCode = spec.value;
          }
          // 其他情况，取前两个字符
          else {
            specCode = spec.value.substring(0, 2);
          }

          if (specCode) {
            specCodes.push(specCode);
          }
        }
      });

      // 如果提取到了规格编码，添加到SKU编码中
      if (specCodes.length > 0) {
        skuCode += `-${specCodes.join('')}`;
      } else {
        // 如果没有提取到规格编码，添加序号
        skuCode += `-${this.skuList.length + 1}`;
      }

      return skuCode;
    },

    /** 发送规格变化事件 */
    emitSpecChange() {
      const specData = {
        selectedSpecs: this.currentSelectedSpecs,
        specNames: this.currentSelectedSpecs.map(specId => ({
          specId,
          specName: this.getSpecName(specId)
        }))
      };
      this.$emit('spec-change', specData);
    },

    /** 发送变化事件 */
    emitChange() {
      const result = this.skuList.map(item => ({
        skuId: item.skuId,
        skuCode: item.skuCode,
        specData: JSON.stringify(item.specDataObj || {}),
        stock: item.stock,
        image: item.image,
        status: item.status
      }));
      this.$emit('input', result);
    }
  }
};
</script>

<style lang="scss" scoped>
.sku-manager {
  .mb-15 {
    margin-bottom: 15px;
  }

  .ml-10 {
    margin-left: 10px;
  }

  .spec-selection-card {
    margin-bottom: 20px;
  }

  .sku-list-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-weight: 600;
      color: #303133;
    }
  }

  .header-buttons {
    display: flex;
    gap: 8px;
  }

  .spec-preview {
    margin-top: 15px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .spec-tag {
    margin-right: 8px;
  }

  .code-check-result {
    margin-top: 5px;
    font-size: 12px;

    i {
      margin-right: 5px;
    }
  }

  .batch-setting {
    margin-top: 20px;
    padding-top: 15px;
  }

  .batch-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .el-form-item {
      margin-bottom: 10px;
      margin-right: 0;
    }
  }

  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &:hover {
        border-color: #409EFF;
      }
    }

    .avatar-uploader-icon {
      font-size: 20px;
      color: #8c939d;
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
    }

    .avatar {
      width: 40px;
      height: 40px;
      display: block;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  // 调整表格内部元素的间距
  ::v-deep .el-table {
    font-size: 12px;

    .el-input-number {
      width: 100%;
    }

    .el-input__inner {
      padding-left: 5px;
      padding-right: 5px;
    }
  }
}
</style>
