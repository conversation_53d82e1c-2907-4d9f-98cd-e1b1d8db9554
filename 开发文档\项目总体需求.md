美企付项目

通过走访浏阳纸尿裤工厂，发现现有收款模式主要是经销商通过电话沟通的方式，让销售人员去 ERP 上下单，确认订单之后将收款码发给经销商，或者让经销商打款到指定的账户中，然后销售人员再去和财务核对。

通过了解明意湖工厂需求，针对各个国家的经销商，希望都通过微信小程序，经销商自助下单，需要翻译成多国语言。

这两个工厂都遇到了同样的问题，微信收款码被风控。

上面的方式存在一些问题： 1.收款码太多，造成了混乱，财务人员不清楚从经销商从哪个渠道付款，需要多次沟通。 2.收款码被风控，无法及时得知，经销商可能需要扫多个收款码，一直遇到一个不被风控的收款渠道，给经销商造成麻烦。

美企付项目要做的就是将多个收款渠道通过 API 的方式接入到系统中。比如 微信支付，支付宝，微企付，银联，抖音,银盛等。

支持微企付，微企付能解决微信收款码被风控的问题。

支持客户多终端使用，比如 PC 电脑，微信小程序，抖音小程序，支付宝小程序等。

打通多个 ERP 系统，比如金蝶，用友等，可以将 ERP 系统中导出的商品信息，库存信息，销售订单信息导入到系统中。

打通电子发票系统，用户付款后，可以申请电子发票。

最主要的功能：

1.工厂上传一次资料，就可以批量申请并开通收款渠道。

2.工厂可以将 ERP 系统导出的商品信息和库存信息导入到美企付系统中，美企付项目可以管理商品信息（如商品基础信息，图片，规格，采购单价，库存等），便于经销商采购商品。

3.工厂的商品销售人员仍然可以通过电话的方式替经销商下单然后将销售订单打印出来，让经销商付款，销售订单中带有统一收款码。

4.提供多个自助下单渠道，如微信小程序，抖音小程序，支付宝等，下单后通过统一收款渠道完成支付。

5.统一收款码，聚合所有收款渠道，通过系统智能管理收款渠道，按照配置或者是否可用，后台切换。

6.经销商支付完成后， 订单支付状态会通过 多个收款渠道的 API 接口推送到美企付平台。

7. 工厂可以在美企付平台 ，将销售订单信息，商品销售信息导回到 ERP 中。

8. 经销商完成支付后，扫码可以申请电子发票。
