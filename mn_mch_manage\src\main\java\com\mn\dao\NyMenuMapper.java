package com.mn.dao;

import com.mn.entity.NyMenu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.form.MenuForm;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
public interface NyMenuMapper extends BaseMapper<NyMenu> {

    List<String> selectPermissionByUserId(Integer userId);

    List<String> selectPermissionByRoleId(Integer roleId);

    List<NyMenu> selectMenuTreeByUserId(Integer userId);

    List<NyMenu> selectMenuList(MenuForm form);

    Integer countChildByMenuId(Integer menuId);

    Integer countRoleByMenuId(Integer menuId);

    List<Integer> selectMenuListByRoleId(Integer roleId);

}
