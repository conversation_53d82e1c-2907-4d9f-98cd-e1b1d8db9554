<template>
	<div class="app-container">
		<!-- 搜索区域 -->
		<el-card class="search-card" shadow="never">
			<el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
				<el-form-item label="集团名称" prop="merchantName">
					<el-input v-model="queryParams.merchantName" placeholder="请输入集团名称" clearable
						@keyup.enter.native="handleQuery">
						<i slot="prefix" class="el-input__icon el-icon-search"></i>
					</el-input>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<!-- 操作按钮区 -->
		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
					v-hasPermi="['system:dept:add']">新增集团</el-button>
			</el-col>
			<el-col :span="1.5">
				<el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
			</el-col>
			<right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
		</el-row>

		<!-- 集团树形表格 -->
		<el-card shadow="never">
			<el-table v-if="refreshTable" v-loading="loading" :data="deptList" row-key="deptId"
				:default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
				:header-cell-style="{ background: '#f5f7fa' }">
				<el-table-column prop="merchantName" label="集团名称" min-width="260">
					<template slot-scope="scope">
						<el-link type="primary" :underline="false" @click="handleUpdate(scope.row)">
							{{ scope.row.merchantName }}
						</el-link>
					</template>
				</el-table-column>
				<el-table-column prop="contactName" label="联系人" width="120" />
				<el-table-column prop="contactMobile" label="联系电话" width="140" />
				<el-table-column prop="status" label="入驻状态" width="100">
					<template slot-scope="scope">
						<el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" align="center" prop="createTime" width="180">
					<template slot-scope="scope">
						<span>{{ parseTime(scope.row.createTime) }}</span>
					</template>
				</el-table-column>
				<!-- 修改操作列 -->
				<el-table-column label="操作" align="center" width="120">
				  <template slot-scope="scope">
				    <el-dropdown trigger="click">
				      <span class="el-dropdown-link">
				        <el-button type="text">
				          更多操作<i class="el-icon-arrow-down el-icon--right"></i>
				        </el-button>
				      </span>
				      <el-dropdown-menu slot="dropdown">
				        <el-dropdown-item v-hasPermi="['system:dept:edit']"
				          @click.native="handleUpdate(scope.row)">
				          <i class="el-icon-edit"></i> 修改
				        </el-dropdown-item>
				        <el-dropdown-item v-hasPermi="['system:dept:add']"
				          @click.native="handleAdd(scope.row)">
				          <i class="el-icon-plus"></i> 新增子集团
				        </el-dropdown-item>
				        <el-dropdown-item v-if="scope.row.parentId != 0" 
				          v-hasPermi="['system:dept:remove']"
				          @click.native="handleDelete(scope.row)">
				          <i class="el-icon-delete"></i> 删除
				        </el-dropdown-item>
				      </el-dropdown-menu>
				    </el-dropdown>
				  </template>
				</el-table-column>
			</el-table>
		</el-card>

		<!-- 添加或修改集团对话框 -->
		<el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
			<el-form ref="form" :model="form" :rules="rules" label-width="120px">
				<el-row>
					<el-col :span="24" v-if="form.parentId !== 0">
						<el-form-item label="上级集团" prop="parentId">
							<treeselect v-model="form.parentId" :options="deptOptions" :normalizer="normalizer"
								placeholder="选择上级集团" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="24">
						<el-form-item label="集团名称" prop="merchantName">
							<el-input v-model="form.merchantName" placeholder="请输入集团名称" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="联系人姓名" prop="contactName">
							<el-input v-model="form.contactName" placeholder="请输入联系人姓名" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系人手机" prop="contactMobile">
							<el-input v-model="form.contactMobile" placeholder="请输入联系人手机号" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="24">
						<el-form-item label="联系邮箱" prop="contactEmail">
							<el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="24">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submitForm">确 定</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import {
	listDept,
	getDept,
	delDept,
	addDept,
	updateDept,
	listDeptExcludeChild
} from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
	name: "Dept",
	components: {
		Treeselect
	},
	data() {
		return {
			// 遮罩层
			loading: true,
			// 显示搜索条件
			showSearch: true,
			// 表格树数据
			deptList: [],
			// 企业树选项
			deptOptions: [],
			// 弹出层标题
			title: "",
			// 是否显示弹出层
			open: false,
			// 是否展开，默认全部展开
			isExpandAll: true,
			// 重新渲染表格状态
			refreshTable: true,
			// 查询参数
			businessTypeOptions: [
				{ label: '企业', value: 'ENTERPRISE' },
				{ label: '个体工商户', value: 'INDIVIDUAL' }
			],
			// 状态映射
			statusMap: {
				'PROCESSING': '处理中',
				'SUCCESS': '成功',
				'FAILED': '失败'
			},
			statusTypeMap: {
				'PROCESSING': 'warning',
				'SUCCESS': 'success',
				'FAILED': 'danger'
			},
			// 修改查询参数
			queryParams: {
				merchantName: undefined,
				status: undefined
			},

			// 表单参数
			form: {
				deptId: undefined,
				parentId: 0,
				merchantName: undefined,
				contactName: undefined,
				contactMobile: undefined,
				contactEmail: undefined,
				remark: undefined,
				status: "SUCCESS"
			},
			// 修改表单校验规则
			rules: {
				parentId: [{ required: true, message: "上级集团不能为空", trigger: "blur" }],
				merchantName: [{ required: true, message: "集团名称不能为空", trigger: "blur" }],
				contactMobile: [
					{ pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
				],
				contactEmail: [
					{ type: 'email', message: "请输入正确的邮箱地址", trigger: "blur" }
				]
			}
		};
	},
	created() {
		this.getList();
	},
	methods: {
		/** 查询企业列表 */
		getList() {
			this.loading = true;
			listDept(this.queryParams).then(response => {
				this.deptList = this.handleTree(response.data, "deptId");
				this.loading = false;
			});
		},
		/** 转换企业数据结构 */
		normalizer(node) {
			if (node.children && !node.children.length) {
				delete node.children;
			}
			return {
				id: node.deptId,
				label: node.merchantName,
				children: node.children
			};
		},
		// 取消按钮
		cancel() {
			this.open = false;
			this.reset();
		},
		// 表单重置
		reset() {
			this.form = {
				deptId: undefined,
				parentId: 0,
				merchantName: undefined,
				contactName: undefined,
				contactMobile: undefined,
				contactEmail: undefined,
				remark: undefined,
				status: "SUCCESS"
			};
			this.resetForm("form");
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.getList();
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.resetForm("queryForm");
			this.handleQuery();
		},
		/** 新增按钮操作 */
		handleAdd(row) {
			this.reset();
			if (row != undefined) {
				this.form.parentId = row.deptId;
			}
			this.open = true;
			this.title = "添加集团";
			listDept().then(response => {
				this.deptOptions = this.handleTree(response.data, "deptId");
			});
		},
		/** 展开/折叠操作 */
		toggleExpandAll() {
			this.refreshTable = false;
			this.isExpandAll = !this.isExpandAll;
			this.$nextTick(() => {
				this.refreshTable = true;
			});
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset();
			getDept(row.deptId).then(response => {
				this.form = response.data;
				this.open = true;
				this.title = "修改集团";
				listDeptExcludeChild(row.deptId).then(response => {
					this.deptOptions = this.handleTree(response.data, "deptId");
					if (this.deptOptions.length == 0) {
						const noResultsOptions = {
							deptId: this.form.parentId,
							merchantName: this.form.parentName,
							children: []
						};
						this.deptOptions.push(noResultsOptions);
					}
				});
			});
		},
		/** 提交按钮 */
		submitForm: function () {
			this.$refs["form"].validate(valid => {
				if (valid) {
					if (this.form.deptId != undefined) {
						updateDept(this.form).then(response => {
							this.$modal.msgSuccess("修改成功");
							this.open = false;
							this.getList();
						});
					} else {
						addDept(this.form).then(response => {
							this.$modal.msgSuccess("新增成功");
							this.open = false;
							this.getList();
						});
					}
				}
			});
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			this.$modal.confirm('是否确认删除名称为"' + row.merchantName + '"的数据项？').then(function () {
				return delDept(row.deptId);
			}).then(() => {
				this.getList();
				this.$modal.msgSuccess("删除成功");
			}).catch(() => { });
		},
		/** 获取状态类型 */
		getStatusType(status) {
		  return this.statusTypeMap[status] || '';
		},
		
		/** 获取状态文本 */
		getStatusText(status) {
		  return this.statusMap[status] || status;
		},




	}
};
</script>

<style lang="scss" scoped>
.app-container {
  .search-card {
    margin-bottom: 15px;

    .el-form {
      margin-bottom: -18px;
    }
  }

  .el-card {
    .el-table {
      margin: 15px 0;
    }
  }

  .el-tag {
    margin-right: 5px;
  }

  .fixed-width {
    .el-button--mini {
      padding: 5px 8px;
    }
  }

  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
    
    .el-button {
      padding: 0;
      font-size: 14px;
    }
  }

  .el-dropdown-menu {
    .el-dropdown-item {
      i {
        margin-right: 5px;
      }
    }
  }
}
</style>