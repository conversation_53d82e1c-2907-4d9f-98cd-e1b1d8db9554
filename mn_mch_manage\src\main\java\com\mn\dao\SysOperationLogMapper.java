package com.mn.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mn.entity.SysOperationLog;
import com.mn.form.SysOperationLogForm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 系统操作日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-28
 */
public interface SysOperationLogMapper extends BaseMapper<SysOperationLog> {

    /**
     * 查询系统操作日志列表
     *
     * @param form 查询条件
     * @return 操作日志列表
     */
    List<SysOperationLog> selectOperationLogList(SysOperationLogForm form);

    /**
     * 根据用户ID查询操作日志
     *
     * @param userId 用户ID
     * @return 操作日志列表
     */
    List<SysOperationLog> selectOperationLogByUserId(@Param("userId") Integer userId);

    /**
     * 根据集团ID查询操作日志
     *
     * @param deptId 集团ID
     * @return 操作日志列表
     */
    List<SysOperationLog> selectOperationLogByDeptId(@Param("deptId") Integer deptId);

    /**
     * 清空操作日志
     *
     * @return 影响行数
     */
    int cleanOperationLog();
}
